<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许开发和测试环境使用明文HTTP连接 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 开发和测试环境IP -->
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">dev.yjsoft.com.cn</domain>
        <!-- 本地开发环境 -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
    </domain-config>
    
    <!-- 生产环境默认使用HTTPS，提高安全性 -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- 信任系统证书颁发机构 -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
    
    <!-- 调试模式下允许用户添加的证书颁发机构 -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config> 