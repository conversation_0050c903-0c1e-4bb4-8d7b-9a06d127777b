package com.yjsoft.roadtravel.basiclibrary.speech.compose

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color

/**
 * 语音识别按钮颜色配置
 */
@Immutable
data class SpeechButtonColors(
    val idleColor: Color,
    val listeningColor: Color,
    val recordingColor: Color,
    val processingColor: Color,
    val successColor: Color,
    val errorColor: Color,
    val contentColor: Color
)

/**
 * 语音识别按钮默认配置
 */
object SpeechButtonDefaults {
    
    /**
     * 默认颜色配置
     */
    @Composable
    fun colors(
        idleColor: Color = MaterialTheme.colorScheme.primary,
        listeningColor: Color = MaterialTheme.colorScheme.primary,
        recordingColor: Color = Color(0xFFE53E3E), // 红色
        processingColor: Color = MaterialTheme.colorScheme.secondary,
        successColor: Color = Color(0xFF38A169), // 绿色
        errorColor: Color = MaterialTheme.colorScheme.error,
        contentColor: Color = Color.White
    ): SpeechButtonColors = SpeechButtonColors(
        idleColor = idleColor,
        listeningColor = listeningColor,
        recordingColor = recordingColor,
        processingColor = processingColor,
        successColor = successColor,
        errorColor = errorColor,
        contentColor = contentColor
    )
}
