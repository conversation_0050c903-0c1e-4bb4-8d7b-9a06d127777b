package com.yjsoft.roadtravel.basiclibrary.speech.config

/**
 * 百度语音识别配置类
 * 包含所有语音识别相关的配置参数
 */
object SpeechConfig {
    
    /**
     * 百度语音识别API配置
     */
    object BaiduSpeech {
        // 百度语音识别API Key和Secret Key
        // 注意：在生产环境中，这些密钥应该从安全的配置文件或服务器获取
        const val API_KEY = "sy8SPFpEsaDNJl2NrWirn91S"
        const val SECRET_KEY = "7e4Lx3I0MSsqYVtzm1bb6fuhdcJVn5Bz"
        
        // 应用ID
        const val APP_ID = "*********"
        
        // 语音识别参数
        const val LANGUAGE = "zh"  // 语言，zh表示中文
        const val SAMPLE_RATE = 16000  // 采样率，支持8000或16000
        const val FORMAT = "pcm"  // 音频格式
        const val CHANNEL = 1  // 声道数
        const val CUID = "roadtravel_android"  // 用户唯一标识
        
        // 识别模式
        const val DEV_PID = 1537  // 普通话(支持简单的英文识别)
        // 其他可选模式：
        // 1536：普通话(纯中文识别)
        // 1737：英语
        // 1637：粤语
        // 1837：四川话
        
        // 超时设置
        const val TIMEOUT_CONNECTION = 10000  // 连接超时时间(ms)
        const val TIMEOUT_SOCKET = 60000  // Socket超时时间(ms)
        
        // 音频录制参数
        const val MAX_SPEECH_TIME_S = 60  // 最长录音时间(秒)
        const val MIN_SPEECH_TIME_S = 1   // 最短录音时间(秒)
        
        // VAD(Voice Activity Detection)参数
        const val VAD_ENDPOINT_TIMEOUT = 2000  // 静音检测超时时间(ms)
        const val VAD_BEGINNING_TIMEOUT = 5000  // 开始录音超时时间(ms)
    }
    
    /**
     * 录音配置
     */
    object Recording {
        const val AUDIO_SOURCE = android.media.MediaRecorder.AudioSource.MIC
        const val AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT
        const val CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_MONO
        
        // 缓冲区大小
        const val BUFFER_SIZE_MULTIPLIER = 2
        
        // 录音文件配置
        const val TEMP_AUDIO_FILE_PREFIX = "speech_temp_"
        const val TEMP_AUDIO_FILE_SUFFIX = ".pcm"
    }
    
    /**
     * UI配置
     */
    object UI {
        // 动画时长
        const val ANIMATION_DURATION_MS = 300L
        const val WAVE_ANIMATION_DURATION_MS = 1000L
        
        // 录音按钮状态
        const val RECORDING_BUTTON_SCALE_PRESSED = 0.95f
        const val RECORDING_BUTTON_SCALE_NORMAL = 1.0f
        
        // 音量显示级别
        const val VOLUME_LEVELS = 10
        const val VOLUME_UPDATE_INTERVAL_MS = 100L
    }
    
    /**
     * 错误码定义
     */
    object ErrorCodes {
        const val SUCCESS = 0
        const val ERROR_NETWORK = -1001
        const val ERROR_PERMISSION_DENIED = -1002
        const val ERROR_AUDIO_RECORD = -1003
        const val ERROR_RECOGNITION_FAILED = -1004
        const val ERROR_TIMEOUT = -1005
        const val ERROR_INVALID_PARAMS = -1006
        const val ERROR_SDK_NOT_INITIALIZED = -1007
    }
    
    /**
     * 日志配置
     */
    object Logging {
        const val TAG = "SpeechRecognition"
        const val ENABLE_DEBUG_LOG = true
        const val LOG_AUDIO_DATA = false  // 是否记录音频数据日志（调试用）
    }
}
