package com.yjsoft.roadtravel.basiclibrary.mvvm.extensions

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Event
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Compose扩展函数
 * 
 * 功能：
 * - 简化MVVM在Compose中的使用
 * - 提供通用的UI状态处理组件
 * - 事件处理工具
 * - 状态收集工具
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

// ========== 状态收集扩展 ==========

/**
 * 收集UiState并提供生命周期感知
 */
@Composable
fun <T> StateFlow<UiState<T>>.collectAsUiState(): State<UiState<T>> {
    return collectAsStateWithLifecycle()
}

/**
 * 收集Resource并提供生命周期感知
 */
@Composable
fun <T> StateFlow<Resource<T>>.collectAsResourceState(): State<Resource<T>> {
    return collectAsStateWithLifecycle()
}

/**
 * 收集加载状态
 */
@Composable
fun StateFlow<Boolean>.collectAsLoadingState(): State<Boolean> {
    return collectAsStateWithLifecycle()
}



// ========== UiState处理组件 ==========

/**
 * UiState通用处理组件
 */
@Composable
fun <T> UiStateHandler(
    uiState: UiState<T>,
    onRetry: (() -> Unit)? = null,
    loadingContent: @Composable () -> Unit = { DefaultLoadingContent() },
    errorContent: @Composable (Throwable?, String, Int, (() -> Unit)?) -> Unit = { _, message, _, retry ->
        DefaultErrorContent(message, retry)
    },
    emptyContent: @Composable (String) -> Unit = { message ->
        DefaultEmptyContent(message)
    },
    successContent: @Composable (T) -> Unit
) {
    when (uiState) {
        is UiState.Idle -> {
            // 空闲状态，不显示任何内容
        }
        is UiState.Loading -> {
            loadingContent()
        }
        is UiState.Success -> {
            successContent(uiState.data)
        }
        is UiState.Error -> {
            errorContent(uiState.exception, uiState.message, uiState.code, onRetry)
        }
        is UiState.Empty -> {
            emptyContent(uiState.message)
        }
    }
}

/**
 * Resource通用处理组件
 */
@Composable
fun <T> ResourceHandler(
    resource: Resource<T>,
    onRetry: (() -> Unit)? = null,
    successContent: @Composable (T) -> Unit,
    loadingContent: @Composable (T?) -> Unit = { data ->
        if (data != null) {
            // 有缓存数据时显示数据和加载指示器
            Column {
                successContent(data)
                DefaultLoadingContent()
            }
        } else {
            DefaultLoadingContent()
        }
    },
    errorContent: @Composable (Throwable?, String, Int, (() -> Unit)?) -> Unit = { _, message, _, retry ->
        DefaultErrorContent(message, retry)
    }
) {
    when (resource) {
        is Resource.Success -> {
            successContent(resource.data)
        }
        is Resource.Error -> {
            errorContent(resource.exception, resource.message, resource.code, onRetry)
        }
        is Resource.Loading -> {
            loadingContent(resource.data)
        }
    }
}

// ========== 默认UI组件 ==========

/**
 * 默认加载内容
 */
@Composable
fun DefaultLoadingContent(
    message: String = "加载中..."
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            if (message.isNotEmpty()) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
        }
    }
}

/**
 * 默认错误内容
 */
@Composable
fun DefaultErrorContent(
    message: String,
    onRetry: (() -> Unit)? = null
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.error
            )
            
            if (onRetry != null) {
                Button(
                    onClick = onRetry,
                    modifier = Modifier.padding(top = 16.dp)
                ) {
                    Text("重试")
                }
            }
        }
    }
}

/**
 * 默认空数据内容
 */
@Composable
fun DefaultEmptyContent(
    message: String = "暂无数据"
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

// ========== 事件处理扩展 ==========

/**
 * 处理单次事件
 */
@Composable
fun <T> HandleEvent(
    eventFlow: Flow<Event<T>>,
    onEvent: (T) -> Unit
) {
    LaunchedEffect(eventFlow) {
        eventFlow.collect { event ->
            event.getContentIfNotHandled()?.let { content ->
                onEvent(content)
            }
        }
    }
}

/**
 * 处理简单事件（无Event包装）
 */
@Composable
fun <T> HandleSimpleEvent(
    eventFlow: Flow<T>,
    onEvent: (T) -> Unit
) {
    LaunchedEffect(eventFlow) {
        eventFlow.collect { event ->
            onEvent(event)
        }
    }
}

// ========== 状态转换扩展 ==========

/**
 * 将UiState转换为加载状态
 */
@Composable
fun <T> UiState<T>.isLoading(): Boolean {
    return this is UiState.Loading
}

/**
 * 将UiState转换为错误状态
 */
@Composable
fun <T> UiState<T>.isError(): Boolean {
    return this is UiState.Error
}

/**
 * 将UiState转换为成功状态
 */
@Composable
fun <T> UiState<T>.isSuccess(): Boolean {
    return this is UiState.Success
}

/**
 * 在Compose中安全获取UiState数据
 */
@Composable
fun <T> UiState<T>.getDataInCompose(): T? {
    return if (this is UiState.Success) data else null
}

// ========== 组合状态处理 ==========

/**
 * 处理多个UiState
 */
@Composable
fun <T1, T2> HandleMultipleUiStates(
    state1: UiState<T1>,
    state2: UiState<T2>,
    onBothSuccess: @Composable (T1, T2) -> Unit,
    onLoading: @Composable () -> Unit = { DefaultLoadingContent() },
    onError: @Composable (String) -> Unit = { message -> DefaultErrorContent(message) }
) {
    when {
        state1.isLoading || state2.isLoading -> {
            onLoading()
        }
        state1.isError -> {
            onError(state1.getErrorMessage())
        }
        state2.isError -> {
            onError(state2.getErrorMessage())
        }
        state1.isSuccess && state2.isSuccess -> {
            val data1 = (state1 as UiState.Success).data
            val data2 = (state2 as UiState.Success).data
            onBothSuccess(data1, data2)
        }
        else -> {
            // 其他状态，可以显示空闲或默认内容
        }
    }
}

// ========== 记忆化扩展 ==========

/**
 * 记忆化状态配置
 * 用于避免不必要的重组
 */
@Composable
fun <T> rememberStateConfig(
    uiState: UiState<T>,
    onRetry: (() -> Unit)? = null
): Pair<UiState<T>, (() -> Unit)?> {
    return remember(uiState, onRetry) {
        Pair(uiState, onRetry)
    }
}

// ========== 工具函数 ==========

/**
 * 检查是否有任何加载状态
 */
@Composable
fun hasAnyLoading(vararg states: UiState<*>): Boolean {
    return states.any { it.isLoading }
}

/**
 * 检查是否有任何错误状态
 */
@Composable
fun hasAnyError(vararg states: UiState<*>): Boolean {
    return states.any { it.isError }
}

/**
 * 获取第一个错误消息
 */
@Composable
fun getFirstErrorMessage(vararg states: UiState<*>): String {
    return states.firstOrNull { it.isError }?.getErrorMessage() ?: ""
}
