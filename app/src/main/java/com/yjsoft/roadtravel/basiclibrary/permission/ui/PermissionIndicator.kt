package com.yjsoft.roadtravel.basiclibrary.permission.ui

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionMessages
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionStatus


/**
 * 权限状态指示器
 * 显示权限的当前状态（已授予、被拒绝、永久拒绝等）
 */
@Composable
fun PermissionIndicator(
    modifier: Modifier = Modifier,
    permissionState: PermissionState,
    showLabel: Boolean = true,
    showDescription: Boolean = false
) {
    val permission = permissionState.permission
    val status = permissionState.status
    val permissionMessage = PermissionMessages.getMessage(permission)
    
    // 根据权限状态确定颜色和图标
    val (color, icon, statusText) = when (status) {
        PermissionStatus.GRANTED -> Triple(
            MaterialTheme.colorScheme.primary,
            Icons.Default.Check,
            "已授予"
        )
        PermissionStatus.DENIED -> Triple(
            MaterialTheme.colorScheme.error,
            Icons.Default.Close,
            "被拒绝"
        )
        PermissionStatus.PERMANENTLY_DENIED -> Triple(
            MaterialTheme.colorScheme.error,
            Icons.Default.Warning,
            "永久拒绝"
        )
        PermissionStatus.UNKNOWN -> Triple(
            MaterialTheme.colorScheme.outline,
            Icons.Default.Warning,
            "未知"
        )
    }
    
    // 动画颜色
    val animatedColor by animateColorAsState(
        targetValue = color,
        animationSpec = tween(300),
        label = "permission_color"
    )
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 状态图标
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
                .background(animatedColor.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = statusText,
                modifier = Modifier.size(16.dp),
                tint = animatedColor
            )
        }
        
        if (showLabel || showDescription) {
            Column {
                if (showLabel) {
                    Text(
                        text = permissionMessage.title,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                if (showDescription) {
                    Text(
                        text = statusText,
                        style = MaterialTheme.typography.bodySmall,
                        color = animatedColor
                    )
                }
            }
        }
    }
}

/**
 * 权限状态卡片
 * 更详细的权限状态显示组件
 */
@Composable
fun PermissionStatusCard(
    modifier: Modifier = Modifier,
    permissionState: PermissionState,
    onRetryClick: (() -> Unit)? = null
) {
    val permission = permissionState.permission
    val status = permissionState.status
    val permissionMessage = PermissionMessages.getMessage(permission)
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 权限指示器
            PermissionIndicator(
                permissionState = permissionState,
                showLabel = true,
                showDescription = true
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 权限描述
            Text(
                text = when (status) {
                    PermissionStatus.GRANTED -> "权限已授予，功能可正常使用"
                    PermissionStatus.DENIED -> permissionMessage.deniedMessage ?: "权限被拒绝，部分功能可能无法使用"
                    PermissionStatus.PERMANENTLY_DENIED -> "权限被永久拒绝，请在设置中手动开启"
                    PermissionStatus.UNKNOWN -> "权限状态未知"
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 重试按钮（仅在权限被拒绝时显示）
            if (status == PermissionStatus.DENIED && onRetryClick != null) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Button(
                    onClick = onRetryClick,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("重新申请")
                }
            }
        }
    }
}

/**
 * 权限状态徽章
 * 简洁的权限状态显示组件
 */
@Composable
fun PermissionStatusBadge(
    permissionState: PermissionState,
    modifier: Modifier = Modifier
) {
    val status = permissionState.status
    
    val (backgroundColor, textColor, statusText) = when (status) {
        PermissionStatus.GRANTED -> Triple(
            MaterialTheme.colorScheme.primaryContainer,
            MaterialTheme.colorScheme.onPrimaryContainer,
            "已授予"
        )
        PermissionStatus.DENIED -> Triple(
            MaterialTheme.colorScheme.errorContainer,
            MaterialTheme.colorScheme.onErrorContainer,
            "被拒绝"
        )
        PermissionStatus.PERMANENTLY_DENIED -> Triple(
            MaterialTheme.colorScheme.errorContainer,
            MaterialTheme.colorScheme.onErrorContainer,
            "永久拒绝"
        )
        PermissionStatus.UNKNOWN -> Triple(
            MaterialTheme.colorScheme.surfaceVariant,
            MaterialTheme.colorScheme.onSurfaceVariant,
            "未知"
        )
    }
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        color = backgroundColor
    ) {
        Text(
            text = statusText,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 权限列表指示器
 * 显示多个权限的状态
 */
@Composable
fun PermissionListIndicator(
    permissionStates: List<PermissionState>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        permissionStates.forEach { permissionState ->
            PermissionStatusCard(
                permissionState = permissionState
            )
        }
    }
}

/**
 * 权限摘要指示器
 * 显示权限的整体状态摘要
 */
@Composable
fun PermissionSummaryIndicator(
    permissionStates: List<PermissionState>,
    modifier: Modifier = Modifier
) {
    val grantedCount = permissionStates.count { it.status == PermissionStatus.GRANTED }
    val totalCount = permissionStates.size
    val allGranted = grantedCount == totalCount
    
    val (color, icon, statusText) = if (allGranted) {
        Triple(
            MaterialTheme.colorScheme.primary,
            Icons.Default.Check,
            "所有权限已授予"
        )
    } else {
        Triple(
            MaterialTheme.colorScheme.error,
            Icons.Default.Warning,
            "$grantedCount/$totalCount 权限已授予"
        )
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Text(
                text = statusText,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = color
            )
        }
    }
}
