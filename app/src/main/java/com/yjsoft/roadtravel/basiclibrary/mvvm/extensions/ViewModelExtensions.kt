package com.yjsoft.roadtravel.basiclibrary.mvvm.extensions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.toUiState
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

/**
 * ViewModel扩展函数
 * 
 * 功能：
 * - 简化ViewModel中的常用操作
 * - 提供安全的协程启动方法
 * - 统一的异常处理
 * - 状态流转换工具
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

// ========== 协程扩展 ==========

/**
 * 安全启动协程（带异常处理）
 */
fun ViewModel.safeLaunch(
    onError: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
) {
    val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        LogManager.e(exception, "[%s] 协程异常", this::class.simpleName)
        onError?.invoke(exception) ?: run {
            // 默认异常处理
            LogManager.e(exception, "ViewModel协程执行异常")
        }
    }
    
    viewModelScope.launch(exceptionHandler, block = block)
}

/**
 * 带加载状态的协程启动
 */
fun ViewModel.launchWithLoading(
    loadingState: MutableStateFlow<Boolean>,
    onError: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
) {
    safeLaunch(onError = onError) {
        loadingState.value = true
        try {
            block()
        } finally {
            loadingState.value = false
        }
    }
}

/**
 * 带UiState管理的协程启动
 */
fun <T> ViewModel.launchWithUiState(
    uiState: MutableStateFlow<UiState<T>>,
    loadingMessage: String = "加载中...",
    block: suspend CoroutineScope.() -> T
) {
    safeLaunch {
        uiState.value = UiState.Loading(loadingMessage)
        try {
            val result = block()
            uiState.value = UiState.Success(result)
        } catch (exception: Exception) {
            val errorMessage = exception.message ?: "操作失败"
            uiState.value = UiState.Error(exception, errorMessage)
        }
    }
}

// ========== StateFlow扩展 ==========

/**
 * 创建UiState的StateFlow
 */
fun <T> ViewModel.createUiStateFlow(initialState: UiState<T> = UiState.Idle): MutableStateFlow<UiState<T>> {
    return MutableStateFlow(initialState)
}

/**
 * 创建加载状态的StateFlow
 */
fun ViewModel.createLoadingStateFlow(initialLoading: Boolean = false): MutableStateFlow<Boolean> {
    return MutableStateFlow(initialLoading)
}

/**
 * 更新UiState为成功状态
 */
fun <T> MutableStateFlow<UiState<T>>.setSuccess(data: T, message: String = "") {
    value = UiState.Success(data, message)
}

/**
 * 更新UiState为错误状态
 */
fun <T> MutableStateFlow<UiState<T>>.setError(
    exception: Throwable? = null,
    message: String = "操作失败",
    code: Int = -1
) {
    value = UiState.Error(exception, message, code)
}

/**
 * 更新UiState为加载状态
 */
fun <T> MutableStateFlow<UiState<T>>.setLoading(message: String = "加载中...") {
    value = UiState.Loading(message)
}

/**
 * 更新UiState为空数据状态
 */
fun <T> MutableStateFlow<UiState<T>>.setEmpty(message: String = "暂无数据") {
    value = UiState.Empty(message)
}

/**
 * 重置UiState为空闲状态
 */
fun <T> MutableStateFlow<UiState<T>>.setIdle() {
    value = UiState.Idle
}

// ========== Flow转换扩展 ==========

/**
 * 将Resource Flow转换为UiState Flow
 */
fun <T> Flow<Resource<T>>.toUiStateFlow(): Flow<UiState<T>> {
    return this.map { resource ->
        resource.toUiState()
    }.catch { exception ->
        emit(UiState.Error(exception, exception.message ?: "未知错误"))
    }
}

/**
 * 为Flow添加加载状态
 */
fun <T> Flow<T>.withLoadingState(): Flow<UiState<T>> {
    return this.map<T, UiState<T>> { data ->
        UiState.Success(data)
    }.onStart {
        emit(UiState.Loading())
    }.catch { exception ->
        emit(UiState.Error(exception, exception.message ?: "操作失败"))
    }
}

/**
 * 处理Resource Flow并更新UiState
 */
fun <T> ViewModel.handleResourceFlow(
    resourceFlow: Flow<Resource<T>>,
    uiState: MutableStateFlow<UiState<T>>,
    onSuccess: ((T) -> Unit)? = null,
    onError: ((Throwable?, String, Int) -> Unit)? = null
) {
    safeLaunch {
        resourceFlow.collect { resource ->
            when (resource) {
                is Resource.Success -> {
                    uiState.setSuccess(resource.data)
                    onSuccess?.invoke(resource.data)
                }
                is Resource.Error -> {
                    uiState.setError(resource.exception, resource.message, resource.code)
                    onError?.invoke(resource.exception, resource.message, resource.code)
                }
                is Resource.Loading -> {
                    if (resource.data != null) {
                        uiState.setSuccess(resource.data)
                    } else {
                        uiState.setLoading()
                    }
                }
            }
        }
    }
}

// ========== 数据处理扩展 ==========

/**
 * 安全地更新StateFlow
 */
fun <T> MutableStateFlow<T>.safeUpdate(update: (T) -> T) {
    try {
        value = update(value)
    } catch (e: Exception) {
        LogManager.e(e, "StateFlow更新失败")
    }
}

/**
 * 条件更新StateFlow
 */
fun <T> MutableStateFlow<T>.updateIf(condition: (T) -> Boolean, update: (T) -> T) {
    if (condition(value)) {
        value = update(value)
    }
}

/**
 * 批量更新StateFlow（减少重组次数）
 */
fun <T> MutableStateFlow<T>.batchUpdate(vararg updates: (T) -> T) {
    var currentValue = value
    updates.forEach { update ->
        currentValue = update(currentValue)
    }
    value = currentValue
}

// ========== 调试扩展 ==========

/**
 * 为StateFlow添加日志
 */
fun <T> StateFlow<T>.withLogging(tag: String): StateFlow<T> {
    return this.also { flow ->
        // 这里可以添加Flow的日志监听
        LogManager.d("[%s] StateFlow创建: %s", tag, this::class.simpleName)
    }
}

/**
 * 记录ViewModel操作
 */
fun ViewModel.logOperation(operation: String, details: String = "") {
    LogManager.d("[%s] %s %s", this::class.simpleName, operation, details)
}

// ========== 生命周期扩展 ==========

/**
 * 在ViewModel清理时执行操作
 */
fun ViewModel.onCleared(action: () -> Unit) {
    // 这里可以通过反射或其他方式在onCleared时执行action
    // 简化实现，直接在当前作用域执行
    action()
}

// ========== 工具扩展 ==========

/**
 * 获取ViewModel的简单名称
 */
val ViewModel.simpleName: String
    get() = this::class.simpleName ?: "UnknownViewModel"

/**
 * 检查ViewModel是否处于活跃状态
 */
fun ViewModel.isActive(): Boolean {
    return try {
        // 简单检查viewModelScope是否可用
        !viewModelScope.coroutineContext.job.isCancelled
    } catch (e: Exception) {
        false
    }
}

/**
 * 创建带标签的异常处理器
 */
fun ViewModel.createExceptionHandler(
    tag: String = this.simpleName,
    onError: ((Throwable) -> Unit)? = null
): CoroutineExceptionHandler {
    return CoroutineExceptionHandler { _, exception ->
        LogManager.e(exception, "[%s] 协程异常", tag)
        onError?.invoke(exception)
    }
}

/**
 * 带标签的安全协程作用域
 */
fun ViewModel.createSafeScope(
    tag: String = this.simpleName,
    onError: ((Throwable) -> Unit)? = null
): CoroutineScope {
    val exceptionHandler = createExceptionHandler(tag, onError)
    return viewModelScope + exceptionHandler
}
