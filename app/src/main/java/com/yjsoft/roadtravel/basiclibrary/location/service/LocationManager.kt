package com.yjsoft.roadtravel.basiclibrary.location.service

import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationError
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationState
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 定位管理器
 * 统一的定位服务接口，管理定位状态和生命周期
 *
 * 注意：为了避免内存泄漏，使用ApplicationContext并确保正确的生命周期管理
 */
class LocationManager private constructor(
    context: Context
) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "LocationManager %s"

        @Volatile
        private var INSTANCE: LocationManager? = null

        /**
         * 获取定位管理器实例
         * 使用ApplicationContext避免内存泄漏
         */
        fun getInstance(context: Context): LocationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LocationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // 使用ApplicationContext避免内存泄漏
    private val applicationContext = context.applicationContext

    // 协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 高德定位服务
    private val aMapLocationService = AMapLocationService(applicationContext)
    
    // 定位状态流
    private val _locationState = MutableStateFlow(LocationState.initial())
    val locationState: StateFlow<LocationState> = _locationState.asStateFlow()
    
    // 当前定位配置
    private var currentConfig: LocationConfig = LocationConfig.default()
    
    // 是否已初始化
    private var isInitialized = false
    
    /**
     * 初始化定位管理器
     */
    fun initialize(config: LocationConfig = LocationConfig.default()): Result<Unit> {
        return try {
            if (isInitialized) {
                LogManager.d(TAG, "定位管理器已初始化")
                return Result.success(Unit)
            }
            
            LogManager.d(TAG, "初始化定位管理器")
            _locationState.value = LocationState.initializing()
            
            // 初始化高德定位服务
            val result = aMapLocationService.initialize(config)
            if (result.isFailure) {
                val error = LocationError.unknown(result.exceptionOrNull())
                _locationState.value = _locationState.value.toFailed(error)
                return result
            }
            
            currentConfig = config
            isInitialized = true
            
            // 重置为初始状态
            _locationState.value = LocationState.initial()
            
            LogManager.d(TAG, "定位管理器初始化成功")
            Result.success(Unit)
            
        } catch (e: Exception) {
            LogManager.e(TAG, "定位管理器初始化失败", e)
            val error = LocationError.unknown(e)
            _locationState.value = _locationState.value.toFailed(error)
            Result.failure(e)
        }
    }
    
    /**
     * 开始定位
     */
    fun startLocation(config: LocationConfig? = null) {
        if (!isInitialized) {
            LogManager.w(TAG, "定位管理器未初始化")
            val error = LocationError.serviceUnavailable()
            _locationState.value = _locationState.value.toFailed(error)
            return
        }
        
        if (_locationState.value.isLocating) {
            LogManager.w(TAG, "定位已在进行中")
            return
        }
        
        LogManager.d(TAG, "开始定位")
        
        // 更新配置
        config?.let {
            currentConfig = it
            aMapLocationService.setLocationConfig(it)
        }
        
        // 更新状态为定位中
        _locationState.value = _locationState.value.toLocating()
        
        // 开始定位
        scope.launch {
            try {
                aMapLocationService.startLocation().collect { result ->
                    result.fold(
                        onSuccess = { locationData ->
                            _locationState.value = _locationState.value.toSuccess(locationData)
                        },
                        onFailure = { throwable ->
                            val error = LocationError.unknown(throwable)
                            _locationState.value = _locationState.value.toFailed(error)
                        }
                    )
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "定位过程中发生错误", e)
                val error = LocationError.unknown(e)
                _locationState.value = _locationState.value.toFailed(error)
            }
        }
    }
    
    /**
     * 停止定位
     */
    fun stopLocation() {
        LogManager.d(TAG, "停止定位")
        aMapLocationService.stopLocation()
        _locationState.value = _locationState.value.toStopped()
    }
    
    /**
     * 单次定位
     */
    suspend fun getSingleLocation(config: LocationConfig = LocationConfig.singleHighAccuracy()): Result<LocationData> {
        if (!isInitialized) {
            val error = LocationError.serviceUnavailable()
            return Result.failure(Exception(error.message))
        }
        
        LogManager.d(TAG, "执行单次定位")
        
        return try {
            // 更新状态为定位中
            _locationState.value = _locationState.value.toLocating()
            
            val result = aMapLocationService.getSingleLocation(config)
            
            result.fold(
                onSuccess = { locationData ->
                    _locationState.value = _locationState.value.toSuccess(locationData)
                },
                onFailure = { throwable ->
                    val error = LocationError.unknown(throwable)
                    _locationState.value = _locationState.value.toFailed(error)
                }
            )
            
            result
            
        } catch (e: Exception) {
            LogManager.e(TAG, "单次定位失败", e)
            val error = LocationError.unknown(e)
            _locationState.value = _locationState.value.toFailed(error)
            Result.failure(e)
        }
    }
    
    /**
     * 获取最后一次定位结果
     */
    fun getLastKnownLocation(): LocationData? {
        return aMapLocationService.getLastKnownLocation()
    }
    
    /**
     * 获取当前定位状态
     */
    fun getCurrentLocationState(): LocationState {
        return _locationState.value
    }
    
    /**
     * 检查定位服务是否可用
     */
    fun isLocationServiceAvailable(): Boolean {
        return isInitialized && aMapLocationService.isLocationServiceAvailable()
    }
    
    /**
     * 是否正在定位
     */
    fun isLocating(): Boolean {
        return _locationState.value.isLocating
    }
    
    /**
     * 设置定位配置
     */
    fun setLocationConfig(config: LocationConfig) {
        currentConfig = config
        if (isInitialized) {
            aMapLocationService.setLocationConfig(config)
        }
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): LocationConfig {
        return currentConfig
    }
    
    /**
     * 重试定位
     */
    fun retryLocation() {
        LogManager.d(TAG, "重试定位")
        
        val currentState = _locationState.value
        if (currentState.error?.isRetryable == true) {
            startLocation()
        } else {
            LogManager.w(TAG, "当前错误不支持重试")
        }
    }
    
    /**
     * 清除定位状态
     */
    fun clearLocationState() {
        _locationState.value = LocationState.initial()
    }
    
    /**
     * 处理权限被拒绝
     */
    fun handlePermissionDenied(deniedPermissions: List<String>) {
        LogManager.w(TAG, "位置权限被拒绝: $deniedPermissions")
        val error = LocationError.permissionDenied(deniedPermissions)
        _locationState.value = _locationState.value.toPermissionDenied()
    }
    
    /**
     * 处理GPS未开启
     */
    fun handleGPSDisabled() {
        LogManager.w(TAG, "GPS未开启")
        _locationState.value = _locationState.value.toGPSDisabled()
    }
    
    /**
     * 处理网络不可用
     */
    fun handleNetworkUnavailable() {
        LogManager.w(TAG, "网络不可用")
        _locationState.value = _locationState.value.toNetworkUnavailable()
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        LogManager.d(TAG, "定位管理器销毁")
        
        try {
            stopLocation()
            aMapLocationService.destroy()
            isInitialized = false
        } catch (e: Exception) {
            LogManager.e(TAG, "销毁定位管理器时发生错误", e)
        }
    }
}
