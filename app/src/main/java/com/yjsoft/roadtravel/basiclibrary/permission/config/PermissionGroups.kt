package com.yjsoft.roadtravel.basiclibrary.permission.config

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi

/**
 * 预定义权限组
 * 将相关权限组织成逻辑组，便于批量管理
 */
object PermissionGroups {
    
    // 权限组名称常量
    const val LOCATION_GROUP = "location"
    const val STORAGE_GROUP = "storage"
    const val CAMERA_GROUP = "camera"
    const val MICROPHONE_GROUP = "microphone"
    const val CONTACTS_GROUP = "contacts"
    const val PHONE_GROUP = "phone"
    const val SMS_GROUP = "sms"
    const val CALENDAR_GROUP = "calendar"
    const val SENSORS_GROUP = "sensors"
    const val NOTIFICATION_GROUP = "notification"
    
    /**
     * 位置权限组
     * 包含精确位置、粗略位置和后台位置权限
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    val LOCATION_PERMISSIONS = listOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.ACCESS_BACKGROUND_LOCATION
    )
    
    /**
     * 存储权限组
     * 根据Android版本包含不同的存储权限
     */
    val STORAGE_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        // Android 13+ 使用细分的媒体权限
        listOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO,
            Manifest.permission.READ_MEDIA_AUDIO
        )
    } else {
        // Android 12 及以下使用传统存储权限
        listOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }
    
    /**
     * 相机权限组
     */
    val CAMERA_PERMISSIONS = listOf(
        Manifest.permission.CAMERA
    )
    
    /**
     * 麦克风权限组
     */
    val MICROPHONE_PERMISSIONS = listOf(
        Manifest.permission.RECORD_AUDIO
    )
    
    /**
     * 联系人权限组
     */
    val CONTACTS_PERMISSIONS = listOf(
        Manifest.permission.READ_CONTACTS,
        Manifest.permission.WRITE_CONTACTS,
        Manifest.permission.GET_ACCOUNTS
    )
    
    /**
     * 电话权限组
     */
    val PHONE_PERMISSIONS = listOf(
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.READ_CALL_LOG,
        Manifest.permission.WRITE_CALL_LOG,
        Manifest.permission.ADD_VOICEMAIL,
        Manifest.permission.USE_SIP,
        Manifest.permission.PROCESS_OUTGOING_CALLS
    )
    
    /**
     * 短信权限组
     */
    val SMS_PERMISSIONS = listOf(
        Manifest.permission.SEND_SMS,
        Manifest.permission.RECEIVE_SMS,
        Manifest.permission.READ_SMS,
        Manifest.permission.RECEIVE_WAP_PUSH,
        Manifest.permission.RECEIVE_MMS
    )
    
    /**
     * 日历权限组
     */
    val CALENDAR_PERMISSIONS = listOf(
        Manifest.permission.READ_CALENDAR,
        Manifest.permission.WRITE_CALENDAR
    )
    
    /**
     * 传感器权限组
     */
    val SENSORS_PERMISSIONS = buildList {
        add(Manifest.permission.BODY_SENSORS)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            add(Manifest.permission.BODY_SENSORS_BACKGROUND)
        }
    }
    
    /**
     * 通知权限组（Android 13+）
     */
    val NOTIFICATION_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        listOf(Manifest.permission.POST_NOTIFICATIONS)
    } else {
        emptyList()
    }
    
    /**
     * 权限组映射表
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private val permissionGroupMap = mapOf(
        LOCATION_GROUP to LOCATION_PERMISSIONS,
        STORAGE_GROUP to STORAGE_PERMISSIONS,
        CAMERA_GROUP to CAMERA_PERMISSIONS,
        MICROPHONE_GROUP to MICROPHONE_PERMISSIONS,
        CONTACTS_GROUP to CONTACTS_PERMISSIONS,
        PHONE_GROUP to PHONE_PERMISSIONS,
        SMS_GROUP to SMS_PERMISSIONS,
        CALENDAR_GROUP to CALENDAR_PERMISSIONS,
        SENSORS_GROUP to SENSORS_PERMISSIONS,
        NOTIFICATION_GROUP to NOTIFICATION_PERMISSIONS
    )
    
    /**
     * 获取权限组的权限列表
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getPermissions(groupName: String): List<String> {
        return permissionGroupMap[groupName] ?: emptyList()
    }
    
    /**
     * 获取所有权限组名称
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getAllGroupNames(): List<String> {
        return permissionGroupMap.keys.toList()
    }
    
    /**
     * 检查权限是否属于某个组
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun isPermissionInGroup(permission: String, groupName: String): Boolean {
        return getPermissions(groupName).contains(permission)
    }
    
    /**
     * 获取权限所属的组
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getGroupForPermission(permission: String): String? {
        return permissionGroupMap.entries.find { (_, permissions) ->
            permissions.contains(permission)
        }?.key
    }
    
    /**
     * 权限组描述信息
     */
    data class PermissionGroupInfo(
        val name: String,
        val displayName: String,
        val description: String,
        val permissions: List<String>,
        val isRequired: Boolean = false
    )
    
    /**
     * 获取权限组信息
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getGroupInfo(groupName: String): PermissionGroupInfo? {
        val permissions = getPermissions(groupName)
        if (permissions.isEmpty()) return null
        
        return when (groupName) {
            LOCATION_GROUP -> PermissionGroupInfo(
                name = LOCATION_GROUP,
                displayName = "位置权限",
                description = "获取设备位置信息，用于导航和定位服务",
                permissions = permissions,
                isRequired = true
            )
            STORAGE_GROUP -> PermissionGroupInfo(
                name = STORAGE_GROUP,
                displayName = "存储权限",
                description = "访问设备存储空间，用于保存和读取文件",
                permissions = permissions,
                isRequired = false
            )
            CAMERA_GROUP -> PermissionGroupInfo(
                name = CAMERA_GROUP,
                displayName = "相机权限",
                description = "使用设备相机进行拍照和录制视频",
                permissions = permissions,
                isRequired = false
            )
            MICROPHONE_GROUP -> PermissionGroupInfo(
                name = MICROPHONE_GROUP,
                displayName = "麦克风权限",
                description = "使用设备麦克风进行录音",
                permissions = permissions,
                isRequired = false
            )
            CONTACTS_GROUP -> PermissionGroupInfo(
                name = CONTACTS_GROUP,
                displayName = "联系人权限",
                description = "访问设备联系人信息",
                permissions = permissions,
                isRequired = false
            )
            PHONE_GROUP -> PermissionGroupInfo(
                name = PHONE_GROUP,
                displayName = "电话权限",
                description = "访问电话状态和拨打电话",
                permissions = permissions,
                isRequired = false
            )
            SMS_GROUP -> PermissionGroupInfo(
                name = SMS_GROUP,
                displayName = "短信权限",
                description = "发送和接收短信",
                permissions = permissions,
                isRequired = false
            )
            CALENDAR_GROUP -> PermissionGroupInfo(
                name = CALENDAR_GROUP,
                displayName = "日历权限",
                description = "访问设备日历信息",
                permissions = permissions,
                isRequired = false
            )
            SENSORS_GROUP -> PermissionGroupInfo(
                name = SENSORS_GROUP,
                displayName = "传感器权限",
                description = "访问设备传感器数据",
                permissions = permissions,
                isRequired = false
            )
            NOTIFICATION_GROUP -> PermissionGroupInfo(
                name = NOTIFICATION_GROUP,
                displayName = "通知权限",
                description = "发送通知消息",
                permissions = permissions,
                isRequired = false
            )
            else -> null
        }
    }
    
    /**
     * 获取所有权限组信息
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getAllGroupInfos(): List<PermissionGroupInfo> {
        return getAllGroupNames().mapNotNull { getGroupInfo(it) }
    }
}
