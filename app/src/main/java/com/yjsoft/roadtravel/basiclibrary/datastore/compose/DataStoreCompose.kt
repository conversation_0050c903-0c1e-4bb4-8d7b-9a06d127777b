package com.yjsoft.roadtravel.basiclibrary.datastore.compose

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.launch

/**
 * DataStore Compose集成
 * 提供在Compose中使用DataStore的便捷函数
 */

/**
 * 记住DataStore中的值，并提供更新函数
 * @param key 偏好设置键
 * @return Pair<当前值, 更新函数>
 */
@Composable
fun <T> rememberDataStoreValue(key: PreferenceKey<T>): Pair<T, suspend (T) -> Unit> {
    val scope = rememberCoroutineScope()
    val value by DataStoreManager.getValueFlow(key).collectAsState(initial = key.defaultValue)
    val updateValue: suspend (T) -> Unit = { newValue ->
        scope.launch {
            try {
                DataStoreManager.setValue(key, newValue)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "更新值失败: ${key.name}", e)
            }
        }
    }
    
    return Pair(value, updateValue)
}

/**
 * 记住DataStore中的值（只读）
 * @param key 偏好设置键
 * @return 当前值
 */
@Composable
fun <T> rememberDataStoreValueAsState(key: PreferenceKey<T>): T {
    return DataStoreManager.getValueFlow(key).collectAsState(initial = key.defaultValue).value
}

/**
 * 记住DataStore中的值，支持初始化时设置默认值
 * @param key 偏好设置键
 * @param initialValue 初始值（如果DataStore中没有值时使用）
 * @return Pair<当前值, 更新函数>
 */
@Composable
fun <T> rememberDataStoreValueWithDefault(
    key: PreferenceKey<T>,
    initialValue: T? = null
): Pair<T, suspend (T) -> Unit> {
    val scope = rememberCoroutineScope()
    var isInitialized by remember { mutableStateOf(false) }
    val value by DataStoreManager.getValueFlow(key).collectAsState(initial = key.defaultValue)
    
    // 如果提供了初始值且还未初始化，则设置初始值
    LaunchedEffect(key, initialValue) {
        if (initialValue != null && !isInitialized) {
            val currentValue = DataStoreManager.getValue(key)
            if (currentValue == key.defaultValue) {
                DataStoreManager.setValue(key, initialValue)
            }
            isInitialized = true
        }
    }
    
    val updateValue: suspend (T) -> Unit = { newValue ->
        scope.launch {
            try {
                DataStoreManager.setValue(key, newValue)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "更新值失败: ${key.name}", e)
            }
        }
    }
    
    return Pair(value, updateValue)
}

/**
 * 记住用户登录状态
 * @return Pair<是否已登录, 登出函数>
 */
@Composable
fun rememberLoginState(): Pair<Boolean, suspend () -> Unit> {
    val repository = remember { DataStoreRepository.getInstance() }
    val scope = rememberCoroutineScope()
    val isLoggedIn by repository.getLoginStatusFlow().collectAsState(initial = false)
    
    val logout: suspend () -> Unit = {
        scope.launch {
            try {
                repository.logout()
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "登出失败", e)
            }
        }
    }
    
    return Pair(isLoggedIn, logout)
}

/**
 * 记住主题模式
 * @return Pair<主题模式, 更新主题函数>
 */
@Composable
fun rememberThemeMode(): Pair<String, suspend (String) -> Unit> {
    val repository = remember { DataStoreRepository.getInstance() }
    val scope = rememberCoroutineScope()
    val themeMode by repository.getThemeModeFlow().collectAsState(initial = "system")
    
    val updateTheme: suspend (String) -> Unit = { newTheme ->
        scope.launch {
            try {
                repository.setThemeMode(newTheme)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "更新主题失败", e)
            }
        }
    }
    
    return Pair(themeMode, updateTheme)
}

/**
 * 记住最后已知城市
 * @return Pair<城市名称, 更新城市函数>
 */
@Composable
fun rememberLastKnownCity(): Pair<String, suspend (String) -> Unit> {
    val repository = remember { DataStoreRepository.getInstance() }
    val scope = rememberCoroutineScope()
    val city by repository.getLastKnownCityFlow().collectAsState(initial = "上海市")
    
    val updateCity: suspend (String) -> Unit = { newCity ->
        scope.launch {
            try {
                repository.setLastKnownCity(newCity)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "更新城市失败", e)
            }
        }
    }
    
    return Pair(city, updateCity)
}

/**
 * 记住收藏城市列表
 * @return Triple<收藏城市列表, 添加收藏函数, 移除收藏函数>
 */
@Composable
fun rememberFavoriteCities(): Triple<Set<String>, suspend (String) -> Unit, suspend (String) -> Unit> {
    val repository = remember { DataStoreRepository.getInstance() }
    val scope = rememberCoroutineScope()
    val favoriteCities by repository.getFavoriteCitiesFlow().collectAsState(initial = emptySet())
    
    val addFavorite: suspend (String) -> Unit = { city ->
        scope.launch {
            try {
                repository.addFavoriteCity(city)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "添加收藏城市失败", e)
            }
        }
    }
    
    val removeFavorite: suspend (String) -> Unit = { city ->
        scope.launch {
            try {
                repository.removeFavoriteCity(city)
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "移除收藏城市失败", e)
            }
        }
    }
    
    return Triple(favoriteCities, addFavorite, removeFavorite)
}

/**
 * 记住DataStore Repository实例
 * @return DataStoreRepository实例
 */
@Composable
fun rememberDataStoreRepository(): DataStoreRepository {
    return remember { DataStoreRepository.getInstance() }
}

/**
 * 执行DataStore操作的Effect
 * @param key 操作标识
 * @param operation 要执行的操作
 */
@Composable
fun DataStoreEffect(
    key: Any?,
    operation: suspend () -> Unit
) {
    val scope = rememberCoroutineScope()
    
    LaunchedEffect(key) {
        scope.launch {
            try {
                operation()
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "DataStore操作失败", e)
            }
        }
    }
}

/**
 * 批量DataStore操作的Effect
 * @param key 操作标识
 * @param operations 要执行的操作列表
 */
@Composable
fun BatchDataStoreEffect(
    key: Any?,
    operations: List<suspend () -> Unit>
) {
    val scope = rememberCoroutineScope()
    
    LaunchedEffect(key) {
        scope.launch {
            try {
                operations.forEach { operation ->
                    operation()
                }
            } catch (e: Exception) {
                LogManager.e("DataStoreCompose", "批量DataStore操作失败", e)
            }
        }
    }
}

/**
 * 条件DataStore操作的Effect
 * @param condition 执行条件
 * @param operation 要执行的操作
 */
@Composable
fun ConditionalDataStoreEffect(
    condition: Boolean,
    operation: suspend () -> Unit
) {
    val scope = rememberCoroutineScope()
    
    LaunchedEffect(condition) {
        if (condition) {
            scope.launch {
                try {
                    operation()
                } catch (e: Exception) {
                    LogManager.e("DataStoreCompose", "条件DataStore操作失败", e)
                }
            }
        }
    }
}
