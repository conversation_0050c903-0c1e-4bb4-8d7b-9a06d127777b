package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

/**
 * 认证拦截器
 * 负责添加认证头
 */
class AuthInterceptor(
    private val context: Context,
    private val tokenProvider: TokenProvider
) : Interceptor {

    init {
        // 初始化TokenManager
        if (!TokenManager.isInitialized()) {
            TokenManager.initialize(context)
        }
    }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 如果是登录相关请求，直接放行
        if (isAuthRequest(originalRequest)) {
            return chain.proceed(originalRequest)
        }

        // 添加认证头
        val authenticatedRequest = addAuthHeader(originalRequest)
        val response = chain.proceed(authenticatedRequest)

        // 如果返回401，清除认证信息并通知认证失败
        if (response.code == 401) {
            runBlocking { clearAuthInfo() }
            notifyAuthFailure()
        }

        return response
    }

    /**
     * 添加认证头
     */
    private fun addAuthHeader(request: Request): Request {
        val token = getAccessToken()
        return if (token.isNotEmpty()) {
            request.newBuilder()
                .header(NetworkConfig.HEADER_AUTHORIZATION, token)
                .build()
        } else {
            request
        }
    }

    /**
     * 判断是否为认证相关请求
     */
    private fun isAuthRequest(request: Request): Boolean {
        val url = request.url.toString()
        return url.contains("/auth/login") ||
                url.contains("/auth/refresh") ||
                url.contains("/auth/register")
    }


    /**
     * 获取访问Token
     */
    private fun getAccessToken(): String {
        return TokenManager.getAccessToken()
    }


    /**
     * 清除认证信息
     */
    private suspend fun clearAuthInfo() {
        TokenManager.clearTokens()
    }

    /**
     * 通知认证失败
     */
    private fun notifyAuthFailure() {
        tokenProvider.onAuthFailure()
    }


}

/**
 * Token提供者接口
 */
interface TokenProvider {
    /**
     * 认证失败回调
     */
    fun onAuthFailure()
}

/**
 * Token信息数据类
 */
data class TokenInfo(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long = 0L
)
