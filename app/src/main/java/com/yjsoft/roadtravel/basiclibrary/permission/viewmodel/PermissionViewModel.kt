package com.yjsoft.roadtravel.basiclibrary.permission.viewmodel

import android.app.Application
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionGroups
import com.yjsoft.roadtravel.basiclibrary.permission.core.MultiplePermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequest
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequestConfig
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionResult
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 权限ViewModel基类
 * 提供权限管理的ViewModel集成
 */
open class PermissionViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "PermissionViewModel"
    }
    
    // 权限管理器
    protected val permissionManager = PermissionManager.getInstance(application)
    
    // 权限状态持有者
    protected val stateHolder = PermissionStateHolder(application, permissionManager)
    
    // 权限状态流
    val permissionStates: StateFlow<Map<String, PermissionState>> = stateHolder.permissionStates
    
    // 权限请求状态
    val isRequesting: StateFlow<Boolean> = stateHolder.isRequesting
    
    // 最后的权限请求结果
    val lastResult: StateFlow<PermissionResult?> = stateHolder.lastResult
    
    // 活动的权限请求
    val activeRequests: StateFlow<List<PermissionRequest>> = stateHolder.activeRequests
    
    /**
     * 初始化权限管理器
     */
    fun initialize(activity: ComponentActivity) {
        PermissionLogger.d(TAG, "初始化权限ViewModel")
        permissionManager.initialize(activity)
    }
    
    /**
     * 请求单个权限
     */
    fun requestPermission(
        permission: String,
        config: PermissionRequestConfig = PermissionConfig.defaultRequestConfig,
        source: String? = null,
        callback: ((PermissionResult) -> Unit)? = null
    ) {
        viewModelScope.launch {
            val request = PermissionRequest.single(permission, config, source)
            val result = stateHolder.requestPermissions(request)
            callback?.invoke(result)
        }
    }
    
    /**
     * 请求多个权限
     */
    fun requestPermissions(
        permissions: List<String>,
        config: PermissionRequestConfig = PermissionConfig.defaultRequestConfig,
        source: String? = null,
        callback: ((PermissionResult) -> Unit)? = null
    ) {
        viewModelScope.launch {
            val request = PermissionRequest.multiple(permissions, config, source)
            val result = stateHolder.requestPermissions(request)
            callback?.invoke(result)
        }
    }
    
    /**
     * 请求权限组
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun requestPermissionGroup(
        groupName: String,
        config: PermissionRequestConfig? = null,
        source: String? = null,
        callback: ((PermissionResult) -> Unit)? = null
    ) {
        val permissions = PermissionGroups.getPermissions(groupName)
        if (permissions.isEmpty()) {
            PermissionLogger.w(TAG, "权限组 '$groupName' 不存在或为空")
            return
        }
        
        val groupConfig = config 
            ?: PermissionConfig.getPermissionGroupConfig(groupName)
            ?: PermissionConfig.defaultRequestConfig
        
        requestPermissions(permissions, groupConfig, source, callback)
    }
    
    /**
     * 请求位置权限
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun requestLocationPermissions(
        includeBackgroundLocation: Boolean = false,
        callback: ((PermissionResult) -> Unit)? = null
    ) {
        val permissions = if (includeBackgroundLocation) {
            PermissionGroups.LOCATION_PERMISSIONS
        } else {
            PermissionGroups.LOCATION_PERMISSIONS.filter {
                it != android.Manifest.permission.ACCESS_BACKGROUND_LOCATION 
            }
        }
        
        val config = PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "位置权限申请",
            rationaleMessage = "应用需要获取您的位置信息来提供导航和定位服务",
            showSettingsDialog = true,
            settingsTitle = "开启位置权限",
            settingsMessage = "请在设置中开启位置权限，以便应用为您提供更好的服务"
        )
        
        requestPermissions(permissions, config, "location_request", callback)
    }
    
    /**
     * 请求相机权限
     */
    fun requestCameraPermission(callback: ((Boolean) -> Unit)? = null) {
        val config = PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "相机权限申请",
            rationaleMessage = "应用需要使用相机来拍摄照片和录制视频",
            showSettingsDialog = true,
            settingsTitle = "开启相机权限",
            settingsMessage = "请在设置中开启相机权限"
        )
        
        requestPermission(
            permission = android.Manifest.permission.CAMERA,
            config = config,
            source = "camera_request"
        ) { result ->
            callback?.invoke(result.isSuccess)
        }
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermissions(callback: ((PermissionResult) -> Unit)? = null) {
        val config = PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "存储权限申请",
            rationaleMessage = "应用需要访问存储空间来保存和读取文件",
            showSettingsDialog = true,
            settingsTitle = "开启存储权限",
            settingsMessage = "请在设置中开启存储权限"
        )
        
        requestPermissions(
            permissions = PermissionGroups.STORAGE_PERMISSIONS,
            config = config,
            source = "storage_request",
            callback = callback
        )
    }
    
    /**
     * 检查权限状态
     */
    fun checkPermission(permission: String): StateFlow<PermissionState> {
        return stateHolder.checkPermission(permission)
    }
    
    /**
     * 检查多个权限状态
     */
    fun checkPermissions(permissions: List<String>): StateFlow<MultiplePermissionState> {
        return stateHolder.checkPermissions(permissions)
    }
    
    /**
     * 检查权限组状态
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun checkPermissionGroup(groupName: String): StateFlow<MultiplePermissionState> {
        val permissions = PermissionGroups.getPermissions(groupName)
        return checkPermissions(permissions)
    }
    
    /**
     * 刷新权限状态
     */
    fun refreshPermissionStates(permissions: List<String>? = null) {
        stateHolder.refreshPermissionStates(permissions)
    }
    
    /**
     * 检查权限是否已授予
     */
    fun isPermissionGranted(permission: String): Boolean {
        return stateHolder.isPermissionGranted(permission)
    }
    
    /**
     * 检查多个权限是否都已授予
     */
    fun arePermissionsGranted(permissions: List<String>): Boolean {
        return stateHolder.arePermissionsGranted(permissions)
    }
    
    /**
     * 检查权限组是否已授予
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun isPermissionGroupGranted(groupName: String): Boolean {
        val permissions = PermissionGroups.getPermissions(groupName)
        return arePermissionsGranted(permissions)
    }
    
    /**
     * 获取被拒绝的权限
     */
    fun getDeniedPermissions(permissions: List<String>): List<String> {
        return stateHolder.getDeniedPermissions(permissions)
    }
    
    /**
     * 获取已授予的权限
     */
    fun getGrantedPermissions(permissions: List<String>): List<String> {
        return stateHolder.getGrantedPermissions(permissions)
    }
    
    /**
     * 清除权限状态
     */
    fun clearPermissionStates() {
        stateHolder.clearPermissionStates()
    }
    
    /**
     * 清除请求结果
     */
    fun clearLastResult() {
        stateHolder.clearLastResult()
    }
    
    /**
     * 创建权限状态的组合流
     */
    fun combinePermissionStates(permissions: List<String>): StateFlow<MultiplePermissionState> {
        return stateHolder.combinePermissionStates(permissions)
    }
    
    override fun onCleared() {
        super.onCleared()
        PermissionLogger.d(TAG, "权限ViewModel已清理")
    }
}
