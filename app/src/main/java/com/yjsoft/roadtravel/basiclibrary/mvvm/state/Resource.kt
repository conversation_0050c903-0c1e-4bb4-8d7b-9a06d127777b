package com.yjsoft.roadtravel.basiclibrary.mvvm.state

/**
 * 资源状态封装类
 * 
 * 功能：
 * - 封装网络请求和数据操作的结果
 * - 提供统一的成功/失败处理机制
 * - 支持链式调用和函数式编程
 * 
 * 设计原则：
 * - 类型安全：使用密封类确保状态完整性
 * - 函数式：支持map、flatMap等函数式操作
 * - 异常安全：统一的异常处理机制
 * 
 * 使用方式：
 * ```kotlin
 * // 在Repository中
 * suspend fun getUser(id: String): Resource<User> {
 *     return try {
 *         val user = apiService.getUser(id)
 *         Resource.Success(user)
 *     } catch (e: Exception) {
 *         Resource.Error(e, "获取用户信息失败")
 *     }
 * }
 * 
 * // 在ViewModel中
 * viewModelScope.launch {
 *     val result = repository.getUser("123")
 *     result.onSuccess { user ->
 *         _uiState.value = UiState.Success(user)
 *     }.onError { exception, message ->
 *         _uiState.value = UiState.Error(exception, message)
 *     }
 * }
 * ```
 * 
 * @param T 数据类型
 * <AUTHOR> Team
 * @since 1.0.0
 */
sealed class Resource<out T> {
    
    /**
     * 成功状态
     * @param data 成功返回的数据
     */
    data class Success<T>(val data: T) : Resource<T>()
    
    /**
     * 错误状态
     * @param exception 异常信息
     * @param message 错误消息
     * @param code 错误代码
     */
    data class Error(
        val exception: Throwable? = null,
        val message: String = "操作失败",
        val code: Int = -1
    ) : Resource<Nothing>()
    
    /**
     * 加载状态
     * @param data 可选的缓存数据
     */
    data class Loading<T>(val data: T? = null) : Resource<T>()
    
    // ========== 便捷属性 ==========
    
    /**
     * 是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success
    
    /**
     * 是否为错误状态
     */
    val isError: Boolean
        get() = this is Error
    
    /**
     * 是否为加载状态
     */
    val isLoading: Boolean
        get() = this is Loading
    
    /**
     * 获取数据（如果存在）
     */
    fun getDataOrNull(): T? {
        return when (this) {
            is Success -> data
            is Loading -> data
            is Error -> null
        }
    }
    
    /**
     * 获取错误信息
     */
    fun getErrorMessage(): String {
        return if (this is Error) message else ""
    }
    
    /**
     * 获取异常信息
     */
    fun getThrowable(): Throwable? {
        return if (this is Error) exception else null
    }
    
    companion object {
        /**
         * 创建成功状态
         */
        fun <T> success(data: T): Resource<T> = Success(data)
        
        /**
         * 创建错误状态
         */
        fun error(
            exception: Throwable? = null,
            message: String = "操作失败",
            code: Int = -1
        ): Resource<Nothing> = Error(exception, message, code)
        
        /**
         * 创建加载状态
         */
        fun <T> loading(data: T? = null): Resource<T> = Loading(data)
    }
}

/**
 * Resource扩展函数
 */

/**
 * 映射数据类型
 */
inline fun <T, R> Resource<T>.map(transform: (T) -> R): Resource<R> {
    return when (this) {
        is Resource.Success -> Resource.Success(transform(data))
        is Resource.Error -> Resource.Error(exception, message, code)
        is Resource.Loading -> Resource.Loading(data?.let(transform))
    }
}

/**
 * 平铺映射
 */
inline fun <T, R> Resource<T>.flatMap(transform: (T) -> Resource<R>): Resource<R> {
    return when (this) {
        is Resource.Success -> transform(data)
        is Resource.Error -> Resource.Error(exception, message, code)
        is Resource.Loading -> Resource.Loading()
    }
}

/**
 * 当成功时执行操作
 */
inline fun <T> Resource<T>.onSuccess(action: (T) -> Unit): Resource<T> {
    if (this is Resource.Success) {
        action(data)
    }
    return this
}

/**
 * 当错误时执行操作
 */
inline fun <T> Resource<T>.onError(action: (Throwable?, String, Int) -> Unit): Resource<T> {
    if (this is Resource.Error) {
        action(exception, message, code)
    }
    return this
}

/**
 * 当加载时执行操作
 */
inline fun <T> Resource<T>.onLoading(action: (T?) -> Unit): Resource<T> {
    if (this is Resource.Loading) {
        action(data)
    }
    return this
}

/**
 * 转换为UiState
 */
fun <T> Resource<T>.toUiState(): UiState<T> {
    return when (this) {
        is Resource.Success -> UiState.Success(data)
        is Resource.Error -> UiState.Error(exception, message, code)
        is Resource.Loading -> if (data != null) {
            UiState.Success(data)
        } else {
            UiState.Loading()
        }
    }
}

/**
 * 从UiState转换
 */
fun <T> UiState<T>.toResource(): Resource<T> {
    return when (this) {
        is UiState.Success -> Resource.Success(data)
        is UiState.Error -> Resource.Error(exception, message, code)
        is UiState.Loading -> Resource.Loading()
        is UiState.Empty -> Resource.Error(message = message)
        is UiState.Idle -> Resource.Loading()
    }
}

/**
 * 安全获取数据
 */
fun <T> Resource<T>.getOrDefault(defaultValue: T): T {
    return getDataOrNull() ?: defaultValue
}

/**
 * 安全获取数据或执行操作
 */
inline fun <T> Resource<T>.getOrElse(defaultValue: () -> T): T {
    return getDataOrNull() ?: defaultValue()
}

/**
 * 折叠操作
 */
inline fun <T, R> Resource<T>.fold(
    onSuccess: (T) -> R,
    onError: (Throwable?, String, Int) -> R,
    onLoading: (T?) -> R
): R {
    return when (this) {
        is Resource.Success -> onSuccess(data)
        is Resource.Error -> onError(exception, message, code)
        is Resource.Loading -> onLoading(data)
    }
}
