package com.yjsoft.roadtravel.basiclibrary.map.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.model.*
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.ui.activities.plan.models.Route
import com.yjsoft.roadtravel.R

// 时间线颜色数组，与PlanDetailsScreen保持一致
private val timelineColors = listOf(
    Color(0xFF1890FF), // 蓝色
    Color(0xFF52C41A), // 绿色
    Color(0xFFFA8C16), // 橙色
    Color(0xFF722ED1), // 紫色
    Color(0xFFEB2F96), // 粉色
    Color(0xFF13C2C2), // 青色
    Color(0xFFF5222D), // 红色
    Color(0xFFFAAD14)  // 黄色
)

/**
 * 根据天数获取对应的时间线颜色
 * @param dayNumber 天数（从0开始）
 */
private fun getTimelineColor(dayNumber: Int): Color {
    // dayNumber从0开始，直接取模即可
    val safeIndex = if (dayNumber < 0) 0 else dayNumber % timelineColors.size
    return timelineColors[safeIndex]
}

/**
 * 支持Polyline绘制的地图组件
 * 
 * @param modifier 修饰符
 * @param latitude 中心纬度
 * @param longitude 中心经度
 * @param zoom 缩放级别
 * @param routes 路线数据列表
 */
@Composable
fun PolylineAMapView(
    modifier: Modifier = Modifier,
    latitude: Double,
    longitude: Double,
    zoom: Float = 15f,
    routes: List<Route> = emptyList()
) {
    val lifecycleOwner = LocalLifecycleOwner.current

    // 地图状态
    var mapView by remember { mutableStateOf<MapView?>(null) }
    var aMap by remember { mutableStateOf<AMap?>(null) }
    
    // 记录已绘制的Polyline，用于清理
    var drawnPolylines by remember { mutableStateOf<List<Polyline>>(emptyList()) }

    Box(modifier = modifier) {
        AndroidView(
            factory = { ctx ->
                MapView(ctx).apply {
                    onCreate(null)
                    mapView = this
                    val map = this.map
                    aMap = map
                    // 配置地图基础设置
                    setupPolylineMap(map, latitude, longitude, zoom)
                }
            },
            modifier = Modifier.fillMaxSize(),
            update = { view ->
                aMap?.let { map ->
                    // 更新地图中心位置
                    val latLng = LatLng(latitude, longitude)
                    val cameraUpdate = CameraUpdateFactory.newLatLngZoom(latLng, zoom)
                    map.animateCamera(cameraUpdate)
                    
                    // 绘制路线
                    if (routes.isNotEmpty()) {
                        drawPolylines(map, routes) { newPolylines ->
                            drawnPolylines = newPolylines
                        }
                    }
                }
            }
        )
    }

    // 处理生命周期
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            mapView?.let { view ->
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        LogManager.d("PolylineAMapView", "地图恢复")
                        view.onResume()
                    }

                    Lifecycle.Event.ON_PAUSE -> {
                        LogManager.d("PolylineAMapView", "地图暂停")
                        view.onPause()
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        LogManager.d("PolylineAMapView", "地图销毁")
                        // 清理Polyline
                        drawnPolylines.forEach { it.remove() }
                        view.onDestroy()
                    }

                    else -> {}
                }
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            // 清理Polyline
            drawnPolylines.forEach { it.remove() }
            mapView?.onDestroy()
        }
    }
}

/**
 * 配置支持Polyline的地图设置
 */
private fun setupPolylineMap(
    aMap: AMap,
    latitude: Double,
    longitude: Double,
    zoom: Float
) {
    try {
        // 设置地图类型为普通地图
        aMap.mapType = AMap.MAP_TYPE_NORMAL

        // 设置UI控件
        aMap.uiSettings.isZoomControlsEnabled = false
        aMap.uiSettings.isRotateGesturesEnabled = true
        aMap.uiSettings.isScaleControlsEnabled = false
        aMap.uiSettings.isCompassEnabled = false

        // 禁用我的位置显示
        aMap.isMyLocationEnabled = false

        // 移动到指定位置
        val latLng = LatLng(latitude, longitude)
        aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, zoom))

        LogManager.d("PolylineAMapView", "地图配置完成: ($latitude, $longitude), zoom: $zoom")
    } catch (e: Exception) {
        LogManager.e("PolylineAMapView", "地图配置失败", e)
    }
}

/**
 * 绘制多条Polyline路线
 */
private fun drawPolylines(
    aMap: AMap,
    routes: List<Route>,
    onPolylinesDrawn: (List<Polyline>) -> Unit
) {
    try {
        // 清除现有的所有覆盖物
        aMap.clear()
        
        val newPolylines = mutableListOf<Polyline>()
        val allPoints = mutableListOf<LatLng>()

        routes.forEach { route ->
            try {
                LogManager.d("PolylineAMapView", "处理路线: day=${route.day}, polyline点数=${route.polyline.size}")

                // 检查day值的有效性
                if (route.day < 0) {
                    LogManager.w("PolylineAMapView", "路线天数无效: ${route.day}，将使用第0天颜色")
                }

                // 获取该天的颜色
                val dayColor = getTimelineColor(route.day)

                // 转换坐标点
                val points = route.polyline.mapIndexedNotNull { index, point ->
                    if (point.size >= 2) {
                        try {
                            val latLng = LatLng(point[1], point[0]) // 注意：高德地图是 [经度, 纬度]，但LatLng是 (纬度, 经度)
                            allPoints.add(latLng)
                            latLng
                        } catch (e: Exception) {
                            LogManager.e("PolylineAMapView", "转换坐标点失败，索引: $index, 点: $point", e)
                            null
                        }
                    } else {
                        LogManager.w("PolylineAMapView", "坐标点数据不足，索引: $index, 点: $point")
                        null
                    }
                }

                if (points.isNotEmpty()) {
                    // 创建Polyline选项
                    val polylineOptions = PolylineOptions()
                        .addAll(points)
                        .color(dayColor.toArgb())
                        .width(30f) // 更粗的线宽
                        .geodesic(true) // 使用大地线

                    // 尝试设置线条样式（如果API支持）
                    try {
                        polylineOptions.lineCapType(PolylineOptions.LineCapType.LineCapRound) // 圆形端点
                        polylineOptions.lineJoinType(PolylineOptions.LineJoinType.LineJoinRound) // 圆形连接点
                    } catch (e: Exception) {
                        LogManager.d("PolylineAMapView", "线条样式API不支持，使用默认样式")
                    }

                    // 添加到地图
                    val polyline = aMap.addPolyline(polylineOptions)

                    // 设置箭头纹理
                    try {
                        polyline.isDottedLine = false // 不使用虚线

                        // 使用对应天数颜色的箭头纹理
                        val arrowTexture = BitmapDescriptorFactory.fromResource(getArrowResourceByDay(route.day))
                        polyline.setCustomTexture(arrowTexture)

                        LogManager.d("PolylineAMapView", "成功设置箭头纹理")
                    } catch (e: Exception) {
                        LogManager.w("PolylineAMapView", "设置箭头纹理失败，使用默认样式", e)
                    }

                    newPolylines.add(polyline)

                    LogManager.d("PolylineAMapView", "成功绘制第${route.day + 1}天路线，有效点数: ${points.size}")
                } else {
                    LogManager.w("PolylineAMapView", "第${route.day + 1}天路线没有有效坐标点")
                }
            } catch (e: Exception) {
                LogManager.e("PolylineAMapView", "处理第${route.day + 1}天路线失败", e)
            }
        }

        // 调整地图视角以显示所有路线
        if (allPoints.isNotEmpty()) {
            adjustMapBounds(aMap, allPoints)
        }
        
        onPolylinesDrawn(newPolylines)
        LogManager.d("PolylineAMapView", "成功绘制${newPolylines.size}条路线")
        
    } catch (e: Exception) {
        LogManager.e("PolylineAMapView", "绘制Polyline失败 $e")
    }
}

/**
 * 调整地图视角以显示所有点
 */
private fun adjustMapBounds(aMap: AMap, points: List<LatLng>) {
    try {
        if (points.isEmpty()) return
        
        // 计算边界
        val boundsBuilder = LatLngBounds.Builder()
        points.forEach { boundsBuilder.include(it) }
        val bounds = boundsBuilder.build()
        
        // 设置地图视角，添加一些边距
        val padding = 100 // 边距像素
        aMap.animateCamera(
            CameraUpdateFactory.newLatLngBounds(bounds, padding)
        )
        
        LogManager.d("PolylineAMapView", "调整地图视角完成，包含${points.size}个点")
    } catch (e: Exception) {
        LogManager.e("PolylineAMapView", "调整地图视角失败", e)
    }
}

/**
 * 根据天数获取对应颜色的箭头资源
 * @param dayNumber 天数（从0开始）
 */
private fun getArrowResourceByDay(dayNumber: Int): Int {
    // 暂时都使用同一个箭头资源
    // TODO: 需要UI设计师提供8个不同颜色的箭头图片：
    // map_alr_blue, map_alr_green, map_alr_orange, map_alr_purple,
    // map_alr_pink, map_alr_cyan, map_alr_red, map_alr_yellow
    return when (dayNumber % 8) {
        0 -> R.drawable.map_alr_blue    // 蓝色 #1890FF
        1 -> R.drawable.map_alr_green    // 绿色 #52C41A
        2 -> R.drawable.map_alr_orange   // 橙色 #FA8C16
        3 -> R.drawable.map_alr_purple   // 紫色 #722ED1
        4 -> R.drawable.map_alr_pink     // 粉色 #EB2F96
        5 -> R.drawable.map_alr_cyan     // 青色 #13C2C2
        6 -> R.drawable.map_alr_red      // 红色 #F5222D
        7 -> R.drawable.map_alr_yellow   // 黄色 #FAAD14
        else -> R.drawable.map_alr
    }
}




