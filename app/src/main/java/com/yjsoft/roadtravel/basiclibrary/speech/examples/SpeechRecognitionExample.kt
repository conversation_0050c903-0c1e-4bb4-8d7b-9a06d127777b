package com.yjsoft.roadtravel.basiclibrary.speech.examples

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.speech.compose.SpeechRecognitionButton
import com.yjsoft.roadtravel.basiclibrary.speech.compose.SpeechRecognitionDialog
import com.yjsoft.roadtravel.basiclibrary.speech.compose.SpeechStateIndicator
import com.yjsoft.roadtravel.basiclibrary.speech.compose.VolumeIndicator
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionResult

/**
 * 语音识别功能演示页面
 * 展示各种语音识别组件的使用方法
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpeechRecognitionExample() {
    val context = LocalContext.current
    val speechManager = remember { SpeechRecognitionManager.getInstance(context) }
    
    // 状态管理
    var showDialog by remember { mutableStateOf(false) }
    var recognitionHistory by remember { mutableStateOf(listOf<SpeechRecognitionResult>()) }
    var currentText by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf("") }
    
    // 收集语音识别状态
    val state by speechManager.state.collectAsStateWithLifecycle()
    val audioState by speechManager.audioState.collectAsStateWithLifecycle()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题
        Text(
            text = "语音识别演示",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // 当前识别文本显示
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "当前识别结果：",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = currentText.ifEmpty { "暂无识别结果" },
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurface,
                    minLines = 2
                )
            }
        }
        
        // 错误信息显示
        if (errorMessage.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "错误：$errorMessage",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.weight(1f)
                    )
                    
                    IconButton(
                        onClick = { errorMessage = "" }
                    ) {
                        Icon(
                            Icons.Default.Clear,
                            contentDescription = "清除错误",
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
        
        // 语音识别按钮
        SpeechRecognitionButton(
            size = 80.dp,
            onResult = { text ->
                currentText = text
                val result = SpeechRecognitionResult(
                    text = text,
                    isFinal = true,
                    timestamp = System.currentTimeMillis()
                )
                recognitionHistory = listOf(result) + recognitionHistory
                errorMessage = ""
            },
            onError = { error ->
                errorMessage = error
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 状态指示器
        SpeechStateIndicator(state = state)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 音量指示器（录音时显示）
        if (audioState.isRecording) {
            VolumeIndicator(
                volume = audioState.volume,
                modifier = Modifier.height(40.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "录音时长: ${audioState.duration / 1000}s",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 对话框模式按钮
            Button(
                onClick = { showDialog = true },
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    Icons.Default.Mic,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("对话框模式")
            }
            
            // 清除历史按钮
            OutlinedButton(
                onClick = {
                    recognitionHistory = emptyList()
                    currentText = ""
                    errorMessage = ""
                },
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    Icons.Default.Clear,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("清除历史")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 识别历史
        if (recognitionHistory.isNotEmpty()) {
            Text(
                text = "识别历史",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            )
            
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(recognitionHistory) { result ->
                    HistoryItem(result = result)
                }
            }
        }
    }
    
    // 语音识别对话框
    if (showDialog) {
        SpeechRecognitionDialog(
            onDismiss = { showDialog = false },
            onResult = { text ->
                currentText = text
                val result = SpeechRecognitionResult(
                    text = text,
                    isFinal = true,
                    timestamp = System.currentTimeMillis()
                )
                recognitionHistory = listOf(result) + recognitionHistory
                errorMessage = ""
            },
            onError = { error ->
                errorMessage = error
            },
            title = "语音输入",
            hint = "请说出您想要输入的内容..."
        )
    }
}

/**
 * 历史记录项组件
 */
@Composable
private fun HistoryItem(
    result: SpeechRecognitionResult,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = result.text,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "置信度: ${(result.confidence * 100).toInt()}%",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = formatTimestamp(result.timestamp),
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 格式化时间戳
 */
private fun formatTimestamp(timestamp: Long): String {
    val date = java.util.Date(timestamp)
    val format = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
    return format.format(date)
}
