package com.yjsoft.roadtravel.basiclibrary.location.config

import com.amap.api.location.AMapLocationClientOption

/**
 * 定位配置类
 */
data class LocationConfig(
    /** 定位模式 */
    val locationMode: LocationMode = LocationMode.HIGH_ACCURACY,
    /** 定位间隔（毫秒） */
    val interval: Long = 5000L,
    /** 是否只定位一次 */
    val isOnceLocation: Boolean = false,
    /** 是否需要地址信息 */
    val needAddress: Boolean = true,
    /** 是否允许模拟位置 */
    val isMockEnable: Boolean = false,
    /** 是否开启缓存 */
    val isLocationCacheEnable: Boolean = true,
    /** 是否开启WiFi扫描 */
    val isWifiScan: Boolean = true,
    /** 超时时间（毫秒） */
    val httpTimeOut: Long = 30000L,
    /** 是否开启传感器 */
    val isSensorEnable: Boolean = false,
    /** GPS优先超时时间（毫秒） */
    val gpsFirstTimeout: Long = 30000L,
    /** 是否开启自动停止定位 */
    val isOnceLocationLatest: Boolean = false
) {

    /**
     * 转换为高德定位选项
     */
    fun toAMapLocationClientOption(): AMapLocationClientOption {
        return AMapLocationClientOption().apply {
            locationMode = <EMAIL>()
            interval = <EMAIL>
            isOnceLocation = <EMAIL>
            isNeedAddress = <EMAIL>
            isMockEnable = <EMAIL>
            isLocationCacheEnable = <EMAIL>
            isWifiScan = <EMAIL>
            httpTimeOut = <EMAIL>
            isSensorEnable = <EMAIL>
            gpsFirstTimeout = <EMAIL>
            isOnceLocationLatest = <EMAIL>
        }
    }

    companion object {
        /**
         * 默认配置
         */
        fun default(): LocationConfig {
            return LocationConfig()
        }

        /**
         * 高精度单次定位配置
         */
        fun singleHighAccuracy(): LocationConfig {
            return LocationConfig(
                locationMode = LocationMode.HIGH_ACCURACY,
                isOnceLocation = true,
                isOnceLocationLatest = true,
                needAddress = true,
                gpsFirstTimeout = 20000L
            )
        }

        /**
         * 连续定位配置
         */
        fun continuous(intervalMillis: Long = 5000L): LocationConfig {
            return LocationConfig(
                locationMode = LocationMode.HIGH_ACCURACY,
                interval = intervalMillis,
                isOnceLocation = false,
                needAddress = true
            )
        }

        /**
         * 省电模式配置
         */
        fun batterySaving(): LocationConfig {
            return LocationConfig(
                locationMode = LocationMode.BATTERY_SAVING,
                interval = 10000L,
                isOnceLocation = false,
                needAddress = true,
                isWifiScan = true,
                isSensorEnable = false
            )
        }

        /**
         * 仅设备模式配置（仅GPS）
         */
        fun deviceOnly(): LocationConfig {
            return LocationConfig(
                locationMode = LocationMode.DEVICE_SENSORS,
                interval = 3000L,
                isOnceLocation = false,
                needAddress = false,
                isWifiScan = false,
                gpsFirstTimeout = 15000L
            )
        }
    }
}

/**
 * 定位模式枚举
 */
enum class LocationMode(val description: String) {
    /** 高精度模式 */
    HIGH_ACCURACY("高精度模式"),
    /** 省电模式 */
    BATTERY_SAVING("省电模式"),
    /** 仅设备模式 */
    DEVICE_SENSORS("仅设备模式");

    /**
     * 转换为高德定位模式
     */
    fun toAMapLocationMode(): AMapLocationClientOption.AMapLocationMode {
        return when (this) {
            HIGH_ACCURACY -> AMapLocationClientOption.AMapLocationMode.Hight_Accuracy
            BATTERY_SAVING -> AMapLocationClientOption.AMapLocationMode.Battery_Saving
            DEVICE_SENSORS -> AMapLocationClientOption.AMapLocationMode.Device_Sensors
        }
    }
}

/**
 * 定位策略配置
 */
object LocationStrategy {

    /**
     * 默认重试次数
     */
    const val DEFAULT_RETRY_COUNT = 3

    /**
     * 默认重试间隔（毫秒）
     */
    const val DEFAULT_RETRY_INTERVAL = 2000L

    /**
     * 最大重试间隔（毫秒）
     */
    const val MAX_RETRY_INTERVAL = 10000L

    /**
     * 定位超时时间（毫秒）
     */
    const val LOCATION_TIMEOUT = 30000L

    /**
     * 最小精度要求（米）
     */
    const val MIN_ACCURACY = 100f

    /**
     * 最佳精度要求（米）
     */
    const val BEST_ACCURACY = 20f

    /**
     * 位置更新最小距离（米）
     */
    const val MIN_DISTANCE_UPDATE = 10f

    /**
     * 位置更新最小时间间隔（毫秒）
     */
    const val MIN_TIME_UPDATE = 5000L

    /**
     * 缓存位置有效期（毫秒）
     */
    const val CACHE_VALID_DURATION = 5 * 60 * 1000L // 5分钟
}
