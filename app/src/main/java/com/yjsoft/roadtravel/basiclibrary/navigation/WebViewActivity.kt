package com.yjsoft.roadtravel.basiclibrary.navigation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.OnBackPressedCallback
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.ProgressIndicatorDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationManager
import com.yjsoft.roadtravel.basiclibrary.navigation.webview.JavaScriptBridge
import com.yjsoft.roadtravel.basiclibrary.navigation.webview.WebViewContainer
import com.yjsoft.roadtravel.basiclibrary.navigation.webview.WebViewNavigator
import com.yjsoft.roadtravel.ui.components.GradientBackground
import com.yjsoft.roadtravel.ui.components.TitleAlignment
import com.yjsoft.roadtravel.ui.components.TitleBar
import com.yjsoft.roadtravel.ui.components.TitleUtils
import com.yjsoft.roadtravel.ui.theme.RoadTravelTheme

/**
 * 独立的WebView Activity
 * 支持完整的H5页面显示、JavaScript Bridge和导航功能
 */
class WebViewActivity : BaseActivity() {

    companion object {
        private const val TAG = "WebViewActivity %s"
    }

    private var webViewNavigator: WebViewNavigator? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 设置返回键处理
        setupBackPressedHandler()

        // 获取传入参数
        val url = intent.getStringExtra(NavigationConstants.Params.URL) ?: "https://www.baidu.com"
        val title = intent.getStringExtra(NavigationConstants.Params.TITLE)
            ?: NavigationConstants.WebView.DEFAULT_TITLE
        val fromActivity = intent.getStringExtra(NavigationConstants.Params.FROM) ?: "Unknown"

        LogManager.d(TAG, "WebView Activity启动，URL: $url, 来源: $fromActivity")

        setContent {
            RoadTravelTheme {
                WebViewScreen(
                    url = url,
                    initialTitle = TitleUtils.smartLimitTitle(title), // 对初始标题也应用限制
                    onNavigateBack = { handleBackPressed() },
                    onWebViewNavigatorReady = { navigator ->
                        webViewNavigator = navigator
                    }
                )
            }
        }
    }

    /**
     * 设置返回键处理
     */
    private fun setupBackPressedHandler() {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    /**
     * 处理返回逻辑
     */
    private fun handleBackPressed() {
        val canGoBack = webViewNavigator?.goBack() ?: false
        if (!canGoBack) {
            // WebView无法返回，关闭Activity
            NavigationManager.finishWithResult(
                activity = this,
                resultCode = NavigationConstants.ResultCodes.WEBVIEW_FINISHED
            )
        }
    }
}

/**
 * WebView页面组件
 */
@Composable
private fun WebViewScreen(
    url: String,
    initialTitle: String,
    onNavigateBack: () -> Unit,
    onWebViewNavigatorReady: (WebViewNavigator) -> Unit
) {
    val context = LocalContext.current
    var pageTitle by remember { mutableStateOf(initialTitle) }
    var isLoading by remember { mutableStateOf(true) }
    var loadingProgress by remember { mutableStateOf(0) }

    GradientBackground {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent,
            topBar = {
                TitleBar(
                    title = pageTitle,
                    leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    titleAlignment = TitleAlignment.CENTER,
                    backgroundColor = Color.Transparent,
                    contentColor = Color.Black,
                    onLeftIconClick = onNavigateBack
                )
            }
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
            ) {
                // WebView容器
                WebViewContainer(
                    url = url,
                    onPageStarted = { startUrl ->
                        isLoading = true
                        LogManager.d("WebViewScreen", "页面开始加载: $startUrl")
                    },
                    onPageFinished = { finishedUrl ->
                        isLoading = false
                        LogManager.d("WebViewScreen", "页面加载完成: $finishedUrl")
                    },
                    onPageError = { errorUrl, errorMsg ->
                        isLoading = false
                        pageTitle = NavigationConstants.WebView.ERROR_TITLE
                        LogManager.e("WebViewScreen", "主页面加载错误: $errorMsg")
                    },
                    onProgressChanged = { progress ->
                        loadingProgress = progress
                        if (progress >= 100) {
                            isLoading = false
                        }
                    },
                    onTitleChanged = { title ->
                        pageTitle = TitleUtils.smartLimitTitle(title)
                        LogManager.d(
                            "WebViewScreen",
                            "标题已更新: $title -> ${TitleUtils.smartLimitTitle(title)}"
                        )
                    },
                    enableJavaScriptBridge = true,
                    javaScriptBridge = createJavaScriptBridge(context, onWebViewNavigatorReady),
                    onWebViewReady = { webView ->
                        // 创建WebViewNavigator并通知Activity
                        val navigator = WebViewNavigator(context as BaseActivity, webView)
                        onWebViewNavigatorReady(navigator)
                    }
                )

                // 加载进度条
                if (isLoading && loadingProgress < 100) {
                    LinearProgressIndicator(
                        progress = { loadingProgress / 100f },
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.TopCenter),
                        color = ProgressIndicatorDefaults.linearColor,
                        trackColor = ProgressIndicatorDefaults.linearTrackColor,
                        strokeCap = ProgressIndicatorDefaults.LinearStrokeCap,
                    )
                }
            }
        }
    }
}

/**
 * 创建JavaScript Bridge
 */
@Composable
private fun createJavaScriptBridge(
    context: android.content.Context,
    onWebViewNavigatorReady: (WebViewNavigator) -> Unit
): JavaScriptBridge {
    return remember {
        JavaScriptBridge(
            context = context,
            onNavigationRequest = { action, params ->
                LogManager.d("WebViewScreen", "导航请求: $action")
                // 导航请求会由WebViewNavigator处理
            },
            onDataReceived = { key, value ->
                LogManager.d("WebViewScreen", "接收H5数据: $key = $value")
            }
        )
    }
} 