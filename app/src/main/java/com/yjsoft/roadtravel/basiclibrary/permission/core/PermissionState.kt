package com.yjsoft.roadtravel.basiclibrary.permission.core

/**
 * 权限状态枚举
 * 定义权限的各种状态
 */
enum class PermissionStatus {
    /** 权限已授予 */
    GRANTED,
    /** 权限被拒绝 */
    DENIED,
    /** 权限被永久拒绝（用户选择了"不再询问"） */
    PERMANENTLY_DENIED,
    /** 权限状态未知（尚未检查） */
    UNKNOWN
}

/**
 * 单个权限的状态信息
 * 
 * @param permission 权限名称（如 android.permission.CAMERA）
 * @param status 权限状态
 * @param shouldShowRationale 是否应该显示权限说明
 * @param isFirstRequest 是否为首次请求该权限
 */
data class PermissionState(
    val permission: String,
    val status: PermissionStatus,
    val shouldShowRationale: Boolean = false,
    val isFirstRequest: Boolean = true
) {
    /**
     * 权限是否已授予
     */
    val isGranted: Boolean
        get() = status == PermissionStatus.GRANTED
    
    /**
     * 权限是否被拒绝
     */
    val isDenied: <PERSON>ole<PERSON>
        get() = status == PermissionStatus.DENIED
    
    /**
     * 权限是否被永久拒绝
     */
    val isPermanentlyDenied: Boolean
        get() = status == PermissionStatus.PERMANENTLY_DENIED
    
    /**
     * 是否需要显示权限说明
     */
    val needsRationale: Boolean
        get() = shouldShowRationale && !isPermanentlyDenied
}

/**
 * 多个权限的状态信息
 * 
 * @param permissions 权限状态映射表
 * @param requestId 请求ID，用于标识本次权限请求
 */
data class MultiplePermissionState(
    val permissions: Map<String, PermissionState>,
    val requestId: String = ""
) {
    /**
     * 所有权限是否都已授予
     */
    val allGranted: Boolean
        get() = permissions.values.all { it.isGranted }
    
    /**
     * 是否有权限被拒绝
     */
    val anyDenied: Boolean
        get() = permissions.values.any { it.isDenied }
    
    /**
     * 是否有权限被永久拒绝
     */
    val anyPermanentlyDenied: Boolean
        get() = permissions.values.any { it.isPermanentlyDenied }
    
    /**
     * 获取已授予的权限列表
     */
    val grantedPermissions: List<String>
        get() = permissions.filter { it.value.isGranted }.keys.toList()
    
    /**
     * 获取被拒绝的权限列表
     */
    val deniedPermissions: List<String>
        get() = permissions.filter { it.value.isDenied }.keys.toList()
    
    /**
     * 获取被永久拒绝的权限列表
     */
    val permanentlyDeniedPermissions: List<String>
        get() = permissions.filter { it.value.isPermanentlyDenied }.keys.toList()
    
    /**
     * 获取需要显示说明的权限列表
     */
    val permissionsNeedingRationale: List<String>
        get() = permissions.filter { it.value.needsRationale }.keys.toList()
    
    /**
     * 获取指定权限的状态
     */
    fun getPermissionState(permission: String): PermissionState? {
        return permissions[permission]
    }
    
    /**
     * 检查指定权限是否已授予
     */
    fun isPermissionGranted(permission: String): Boolean {
        return permissions[permission]?.isGranted ?: false
    }
}

/**
 * 权限请求配置
 * 
 * @param showRationaleDialog 是否显示权限说明对话框
 * @param rationaleTitle 权限说明对话框标题
 * @param rationaleMessage 权限说明对话框内容
 * @param showSettingsDialog 权限被永久拒绝时是否显示设置引导对话框
 * @param settingsTitle 设置引导对话框标题
 * @param settingsMessage 设置引导对话框内容
 * @param autoNavigateToSettings 是否自动跳转到设置页面
 */
data class PermissionRequestConfig(
    val showRationaleDialog: Boolean = true,
    val rationaleTitle: String = "权限申请",
    val rationaleMessage: String = "应用需要此权限才能正常工作",
    val showSettingsDialog: Boolean = true,
    val settingsTitle: String = "权限设置",
    val settingsMessage: String = "请在设置中手动开启权限",
    val autoNavigateToSettings: Boolean = false
)
