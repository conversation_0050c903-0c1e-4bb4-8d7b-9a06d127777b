# Hilt 依赖注入框架

## 概述

本模块为RoadTravel项目提供了基于Hilt的依赖注入框架，采用渐进式集成策略，在不破坏现有代码的前提下引入现代化的依赖注入能力。

## 主要特性

### 🎯 核心功能
- **渐进式集成** - 不破坏现有的手动初始化逻辑
- **兼容模式** - 支持手动初始化和依赖注入并存
- **模块化设计** - 每个基础库有独立的Hilt模块
- **统一配置** - 集中管理所有依赖注入配置

### 🎨 架构设计
- **分层注入** - Application、Activity、Fragment、ViewModel层级注入
- **类型安全** - 编译时检查依赖关系
- **作用域管理** - 合理的生命周期管理
- **限定符支持** - 支持多种实现的区分

### 📊 集成策略
- **向后兼容** - 现有代码无需修改即可运行
- **逐步迁移** - 可以逐个模块迁移到依赖注入
- **降级支持** - 依赖注入失败时自动回退到手动初始化

## 快速开始

### 1. 基础使用

在Activity中使用依赖注入：

```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var networkManager: NetworkManager
    
    @Inject
    lateinit var logManager: LogManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 依赖已自动注入，可直接使用
        logManager.d("MainActivity", "依赖注入成功")
    }
}
```

### 2. ViewModel中使用

```kotlin
@HiltViewModel
class MainViewModel @Inject constructor(
    private val repository: MainRepository,
    private val networkManager: NetworkManager
) : ViewModel() {
    
    fun loadData() {
        // 使用注入的依赖
        repository.getData()
    }
}
```

### 3. Repository中使用

```kotlin
@Singleton
class MainRepository @Inject constructor(
    private val apiService: ApiService,
    private val dataStoreManager: DataStoreManager
) {
    
    suspend fun getData(): Result<Data> {
        // 使用注入的依赖
        return apiService.fetchData()
    }
}
```

## 模块结构

```
di/
├── HiltConfig.kt                    # Hilt框架配置
├── modules/                         # Hilt模块目录
│   ├── LoggerModule.kt             # 日志框架模块
│   ├── NetworkModule.kt            # 网络框架模块
│   ├── DataStoreModule.kt          # 数据存储模块
│   ├── ImageModule.kt              # 图片加载模块
│   ├── PaymentModule.kt            # 支付框架模块
│   ├── PermissionModule.kt         # 权限框架模块
│   └── LocationModule.kt           # 定位框架模块
├── adapters/                       # 适配器目录
│   └── ManagerAdapter.kt           # Manager类适配器
├── qualifiers/                     # 限定符目录
│   └── Qualifiers.kt               # 自定义限定符
├── config/                         # 配置目录
│   └── DIConfig.kt                 # DI配置管理
├── examples/                       # 示例目录
│   ├── DIExampleViewModel.kt       # 示例ViewModel
│   ├── DIExampleRepository.kt      # 示例Repository
│   └── DIUsageExamples.kt          # 使用示例
├── README.md                       # 本文档
└── MIGRATION_GUIDE.md              # 迁移指南
```

## 兼容性说明

### 现有代码兼容性
- ✅ 现有的手动初始化代码继续工作
- ✅ 不需要修改现有的Manager类
- ✅ 可以逐步迁移到依赖注入
- ✅ 支持混合使用两种方式

### 迁移策略
1. **第一阶段**：添加Hilt支持，保持现有初始化
2. **第二阶段**：新功能优先使用依赖注入
3. **第三阶段**：逐步迁移现有功能
4. **第四阶段**：完全迁移到依赖注入

## 配置选项

### HiltConfig配置
```kotlin
// 启用/禁用Hilt框架
HiltConfig.enableHilt()
HiltConfig.disableHilt()

// 设置兼容模式
HiltConfig.setCompatibilityMode(true)  // 兼容模式
HiltConfig.setCompatibilityMode(false) // 纯DI模式

// 检查状态
val isEnabled = HiltConfig.isHiltEnabled()
val isCompatible = HiltConfig.isCompatibilityMode()
```

## 注意事项

1. **初始化顺序**：Hilt会在Application.onCreate()中自动初始化
2. **注解要求**：使用依赖注入的类需要添加相应注解
3. **作用域管理**：注意选择合适的作用域注解
4. **混淆配置**：已在proguard-rules.pro中添加相关规则
5. **测试支持**：提供了测试环境的依赖注入配置

## 故障排除

### 常见问题
1. **依赖注入失败** - 检查是否添加了@AndroidEntryPoint注解
2. **循环依赖** - 重新设计依赖关系或使用Provider
3. **作用域错误** - 确保依赖的作用域兼容
4. **编译错误** - 检查kapt配置和Hilt版本

### 调试技巧
```kotlin
// 检查Hilt配置状态
val config = hiltConfig.getConfigInfo()
LogManager.d("HiltDebug", "配置信息: $config")

// 检查依赖是否可用
val available = hiltConfig.isDependencyInjectionAvailable()
LogManager.d("HiltDebug", "依赖注入可用: $available")
```

## 版本历史

- **v1.0.0** - 初始版本，基于Hilt 2.52
  - 基础依赖注入框架
  - 兼容模式支持
  - 渐进式迁移策略
  - 完整的基础库模块支持

## 依赖

- Hilt 2.52
- Hilt Navigation Compose 1.2.0
- Kotlin KAPT

## 许可证

本项目采用与主项目相同的许可证。
