# Android 权限动态申请框架

## 概述

这是一个现代化的Android权限动态申请框架，专为Jetpack Compose和MVVM架构设计。框架提供了声明式的权限申请API、响应式的权限状态管理，以及完整的用户体验优化。

## 主要特性

### 🎯 核心功能
- **统一的权限申请API** - 支持单个权限、多个权限和权限组申请
- **响应式权限状态管理** - 基于StateFlow的权限状态监听
- **智能权限分组处理** - 预定义常用权限组（位置、存储、相机等）
- **权限拒绝后的引导处理** - 自动显示说明对话框和设置引导

### 🎨 Compose集成
- **声明式权限申请组件** - 与Compose生态完美集成
- **权限状态自动订阅** - 权限状态变化自动更新UI
- **内置权限说明UI** - 美观的权限说明和引导对话框
- **主题适配支持** - 支持Material Design 3主题

### 📊 日志集成
- **与现有LogManager完美集成** - 详细的权限申请日志记录
- **权限状态变化追踪** - 完整的权限生命周期日志
- **调试信息输出** - 便于开发调试和问题排查

### 🎭 用户体验
- **清晰的权限说明文案** - 用户友好的权限申请说明
- **优雅的权限拒绝处理** - 智能的权限拒绝后续处理
- **系统设置跳转引导** - 一键跳转到应用权限设置
- **权限状态可视化指示** - 直观的权限状态显示组件

## 快速开始

### 1. 初始化框架

在Application中初始化权限框架：

```kotlin
class RoadTravelApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // 初始化权限框架（全局配置）
        PermissionConfig.initializeDefaults()
    }
}
```

在Activity中初始化权限管理器（必须在onCreate中，setContent之前）：

```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化权限管理器（Activity级别）
        // 注意：必须在setContent之前初始化，因为ActivityResultLauncher必须在STARTED状态前注册
        PermissionManager.getInstance(this).initialize(this)

        setContent {
            // UI内容
        }
    }
}
```

### 2. 基础权限申请

#### 使用Compose组件申请单个权限

```kotlin
@Composable
fun CameraFeature() {
    SinglePermissionRequester(
        permission = Manifest.permission.CAMERA,
        onPermissionResult = { granted ->
            if (granted) {
                // 权限已授予，执行相机相关操作
            } else {
                // 权限被拒绝，显示提示或禁用功能
            }
        }
    ) { state ->
        val scope = rememberCoroutineScope()
        
        Button(
            onClick = {
                scope.launch {
                    state.requestPermissions()
                }
            },
            enabled = !state.isRequesting
        ) {
            Text("申请相机权限")
        }
    }
}
```

#### 使用便捷组件申请位置权限

```kotlin
@Composable
fun LocationFeature() {
    LocationPermissionRequester(
        includeBackgroundLocation = false,
        onPermissionResult = { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    // 所有位置权限已授予
                }
                is PermissionResult.Denied -> {
                    // 部分或全部位置权限被拒绝
                    val deniedPermissions = result.deniedPermissions
                }
            }
        }
    ) { state ->
        // UI内容
    }
}
```

### 3. ViewModel集成

```kotlin
class LocationViewModel(application: Application) : PermissionViewModel(application) {
    
    fun requestLocationPermissions() {
        requestLocationPermissions(
            includeBackgroundLocation = false
        ) { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    // 开始位置服务
                    startLocationService()
                }
                is PermissionResult.Denied -> {
                    // 处理权限拒绝
                    handlePermissionDenied(result.deniedPermissions)
                }
            }
        }
    }
    
    private fun startLocationService() {
        // 启动位置服务的具体实现
    }
    
    private fun handlePermissionDenied(deniedPermissions: List<String>) {
        // 处理权限拒绝的具体实现
    }
}
```

在Compose中使用ViewModel：

```kotlin
@Composable
fun LocationScreen() {
    val viewModel: LocationViewModel = viewModel()
    val context = LocalContext.current
    val activity = context as ComponentActivity
    
    // 初始化ViewModel
    LaunchedEffect(Unit) {
        viewModel.initialize(activity)
    }
    
    // 监听权限状态
    val permissionStates by viewModel.permissionStates.collectAsState()
    val isRequesting by viewModel.isRequesting.collectAsState()
    
    Column {
        Button(
            onClick = { viewModel.requestLocationPermissions() },
            enabled = !isRequesting
        ) {
            Text("申请位置权限")
        }
        
        // 显示权限状态
        permissionStates.forEach { (permission, state) ->
            PermissionIndicator(
                permissionState = state,
                showLabel = true,
                showDescription = true
            )
        }
    }
}
```

### 4. 权限状态显示

```kotlin
@Composable
fun PermissionStatusScreen() {
    val permissionStates = listOf(
        // 权限状态列表
    )
    
    LazyColumn {
        item {
            // 权限摘要
            PermissionSummaryIndicator(
                permissionStates = permissionStates
            )
        }
        
        items(permissionStates) { permissionState ->
            PermissionStatusCard(
                permissionState = permissionState,
                onRetryClick = {
                    // 重新申请权限
                }
            )
        }
    }
}
```

## 高级用法

### 自定义权限配置

```kotlin
// 设置全局默认配置
PermissionConfig.defaultRequestConfig = PermissionRequestConfig(
    showRationaleDialog = true,
    rationaleTitle = "自定义标题",
    rationaleMessage = "自定义说明",
    showSettingsDialog = true,
    settingsTitle = "自定义设置标题",
    settingsMessage = "自定义设置说明"
)

// 设置特定权限组的配置
PermissionConfig.setPermissionGroupConfig(
    PermissionGroups.LOCATION_GROUP,
    PermissionRequestConfig(
        showRationaleDialog = true,
        rationaleTitle = "位置权限申请",
        rationaleMessage = "应用需要获取您的位置信息来提供导航服务"
    )
)
```

### 自定义权限说明文案

```kotlin
// 设置单个权限的自定义文案
PermissionMessages.setCustomMessage(
    Manifest.permission.CAMERA,
    PermissionMessage(
        title = "相机权限",
        rationale = "需要使用相机拍摄照片",
        settingsMessage = "请在设置中开启相机权限",
        deniedMessage = "没有相机权限无法拍照"
    )
)
```

### 使用DSL构建权限请求

```kotlin
val request = permissionRequest {
    permissions(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO
    )
    rationale(
        show = true,
        title = "媒体权限申请",
        message = "需要相机和麦克风权限来录制视频"
    )
    settings(
        show = true,
        title = "开启媒体权限",
        autoNavigate = false
    )
    source("video_recording")
}
```

## 权限组

框架预定义了以下权限组：

- **LOCATION_GROUP** - 位置权限（精确位置、粗略位置、后台位置）
- **STORAGE_GROUP** - 存储权限（根据Android版本自动适配）
- **CAMERA_GROUP** - 相机权限
- **MICROPHONE_GROUP** - 麦克风权限
- **CONTACTS_GROUP** - 联系人权限
- **PHONE_GROUP** - 电话权限
- **SMS_GROUP** - 短信权限
- **CALENDAR_GROUP** - 日历权限
- **SENSORS_GROUP** - 传感器权限
- **NOTIFICATION_GROUP** - 通知权限（Android 13+）

## 注意事项

1. **Android版本兼容性** - 框架自动处理不同Android版本的权限差异
2. **权限声明** - 确保在AndroidManifest.xml中声明所需权限
3. **生命周期管理** - 权限管理器会自动处理Activity生命周期
4. **线程安全** - 所有API都是线程安全的，可以在任何线程调用

## 示例代码

完整的使用示例请参考 `PermissionExample.kt` 文件，其中包含了各种使用场景的详细示例。

## 许可证

本框架遵循项目的整体许可证协议。
