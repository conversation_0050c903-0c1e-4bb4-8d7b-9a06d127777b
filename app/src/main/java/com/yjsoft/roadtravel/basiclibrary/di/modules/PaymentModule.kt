package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.repository.PaymentRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 支付框架依赖注入模块
 * 
 * 功能：
 * - 提供PaymentManager的依赖注入
 * - 提供PaymentRepository的依赖注入
 * - 管理支付框架的生命周期
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object PaymentModule {
    
    /**
     * 提供PaymentManager实例
     */
    @Provides
    @Singleton
    fun providePaymentManager(
        @ApplicationContext context: Context
    ): PaymentManager {
        return PaymentManagerProvider.getInstance(context)
    }
    
    /**
     * 提供PaymentRepository实例
     */
    @Provides
    @Singleton
    fun providePaymentRepository(
        @ApplicationContext context: Context
    ): PaymentRepository {
        return PaymentRepositoryProvider.getInstance(context)
    }
    
    /**
     * PaymentManager提供器
     */
    private object PaymentManagerProvider {
        @Volatile
        private var instance: PaymentManager? = null
        
        fun getInstance(context: Context): PaymentManager {
            return instance ?: synchronized(this) {
                instance ?: createPaymentManager(context).also { instance = it }
            }
        }
        
        private fun createPaymentManager(context: Context): PaymentManager {
            // 检查PaymentManager是否已经通过手动方式初始化
            return if (PaymentManager.isInitialized()) {
                // 如果已经初始化，直接返回现有实例
                PaymentManager
            } else {
                // 如果未初始化，进行初始化
                // 这里需要判断是调试模式还是生产模式
                val isDebug = isDebugBuild(context)
                PaymentManager.apply {
                    init(context, debugMode = isDebug)
                }
            }
        }
        
        private fun isDebugBuild(context: Context): Boolean {
            return try {
                val applicationInfo = context.applicationInfo
                (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * PaymentRepository提供器
     */
    private object PaymentRepositoryProvider {
        @Volatile
        private var instance: PaymentRepository? = null
        
        fun getInstance(context: Context): PaymentRepository {
            return instance ?: synchronized(this) {
                instance ?: createPaymentRepository(context).also { instance = it }
            }
        }
        
        private fun createPaymentRepository(context: Context): PaymentRepository {
            // 确保PaymentManager已初始化
            PaymentManagerProvider.getInstance(context)
            
            // 创建PaymentRepository实例
            return PaymentRepository.getInstance().apply {
                init(context)
            }
        }
    }
}

/**
 * PaymentManager扩展函数
 * 用于检查是否已初始化
 */
private fun PaymentManager.isInitialized(): Boolean {
    return try {
        // PaymentManager是object，检查其内部的isInitialized字段
        // 通过反射访问私有字段来检查初始化状态
        val field = PaymentManager::class.java.getDeclaredField("isInitialized")
        field.isAccessible = true
        field.getBoolean(PaymentManager)
    } catch (e: Exception) {
        // 如果反射失败，尝试调用一个需要初始化的方法
        try {
            // 调用paymentState属性，如果未初始化可能会有问题
            paymentState.value
            true
        } catch (ex: Exception) {
            false
        }
    }
}
