package com.yjsoft.roadtravel.basiclibrary.mvvm.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Event
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.EventHandler
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.NavigationEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

/**
 * ViewModel基类
 * 
 * 功能：
 * - 统一的状态管理
 * - 统一的异常处理
 * - 事件管理系统
 * - 加载状态管理
 * - 生命周期感知
 * 
 * 设计原则：
 * - 单一职责：每个ViewModel只负责一个页面的逻辑
 * - 状态驱动：UI状态完全由ViewModel管理
 * - 事件分离：一次性事件与状态分离管理
 * - 异常安全：统一的异常处理机制
 * 
 * 使用方式：
 * ```kotlin
 * @HiltViewModel
 * class UserViewModel @Inject constructor(
 *     private val userRepository: UserRepository
 * ) : BaseViewModel<UserUiState>() {
 * 
 *     override fun createInitialState(): UserUiState = UserUiState()
 * 
 *     fun loadUser(id: String) {
 *         launchWithLoading {
 *             val result = userRepository.getUser(id)
 *             result.onSuccess { user ->
 *                 updateState { it.copy(user = user) }
 *             }.onError { _, message, _ ->
 *                 showError(message)
 *             }
 *         }
 *     }
 * }
 * ```
 * 
 * @param S UI状态类型
 * <AUTHOR> Team
 * @since 1.0.0
 */
abstract class BaseViewModel<S : Any> : ViewModel() {
    
    companion object {
        private const val TAG = "BaseViewModel"
    }
    
    // ========== 状态管理 ==========
    
    /**
     * UI状态
     */
    private val _uiState = MutableStateFlow(createInitialState())
    val uiState: StateFlow<S> = _uiState.asStateFlow()
    
    /**
     * 当前状态
     */
    protected val currentState: S
        get() = _uiState.value
    
    /**
     * 加载状态
     */
    val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // ========== 事件管理 ==========
    
    /**
     * UI事件处理器
     */
    private val uiEventHandler = EventHandler<UiEvent>()
    val uiEvents: SharedFlow<Event<UiEvent>> = uiEventHandler.events
    
    /**
     * 导航事件处理器
     */
    private val navigationEventHandler = EventHandler<NavigationEvent>()
    val navigationEvents: SharedFlow<Event<NavigationEvent>> = navigationEventHandler.events
    
    /**
     * 自定义事件流
     */
    private val _customEvents = MutableSharedFlow<Event<Any>>(
        extraBufferCapacity = 1,
        replay = 0
    )
    val customEvents: SharedFlow<Event<Any>> = _customEvents.asSharedFlow()
    
    // ========== 异常处理 ==========
    
    /**
     * 异常处理器
     */
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        LogManager.e(exception, "[%s] 协程异常", this::class.simpleName)
        handleException(exception)
    }
    
    /**
     * 带异常处理的协程作用域
     */
    protected val safeViewModelScope: CoroutineScope = viewModelScope + exceptionHandler
    
    // ========== 抽象方法 ==========
    
    /**
     * 创建初始状态
     */
    protected abstract fun createInitialState(): S
    
    // ========== 状态更新方法 ==========
    
    /**
     * 更新状态
     */
    protected fun updateState(update: (S) -> S) {
        _uiState.value = update(currentState)
    }
    
    /**
     * 设置状态
     */
    protected fun setState(newState: S) {
        _uiState.value = newState
    }
    
    /**
     * 设置加载状态
     */
    protected fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
    
    // ========== 事件发送方法 ==========
    
    /**
     * 发送UI事件
     */
    protected fun sendUiEvent(event: UiEvent) {
        uiEventHandler.sendEvent(event)
    }
    
    /**
     * 发送导航事件
     */
    protected fun sendNavigationEvent(event: NavigationEvent) {
        navigationEventHandler.sendEvent(event)
    }
    
    /**
     * 发送自定义事件
     */
    protected fun sendCustomEvent(event: Any) {
        _customEvents.tryEmit(Event(event))
    }
    
    // ========== 便捷方法 ==========
    
    /**
     * 显示Toast
     */
    protected fun showToast(message: String) {
        sendUiEvent(UiEvent.ShowToast(message))
    }
    
    /**
     * 显示Snackbar
     */
    protected fun showSnackbar(message: String, actionLabel: String? = null) {
        sendUiEvent(UiEvent.ShowSnackbar(message, actionLabel))
    }
    
    /**
     * 显示对话框
     */
    protected fun showDialog(title: String, message: String) {
        sendUiEvent(UiEvent.ShowDialog(title, message))
    }
    
    /**
     * 显示错误信息
     */
    protected fun showError(message: String) {
        sendUiEvent(UiEvent.ShowToast(message))
    }
    
    /**
     * 返回上一页
     */
    protected fun navigateBack() {
        sendNavigationEvent(NavigationEvent.Back)
    }
    
    /**
     * 导航到指定路由
     */
    protected fun navigateTo(route: String) {
        sendNavigationEvent(NavigationEvent.ToRoute(route))
    }
    
    // ========== 协程启动方法 ==========
    
    /**
     * 启动协程（带异常处理）
     */
    protected fun launch(block: suspend CoroutineScope.() -> Unit) {
        safeViewModelScope.launch(block = block)
    }
    
    /**
     * 启动协程（带加载状态）
     */
    protected fun launchWithLoading(block: suspend CoroutineScope.() -> Unit) {
        safeViewModelScope.launch {
            setLoading(true)
            try {
                block()
            } finally {
                setLoading(false)
            }
        }
    }
    
    /**
     * 处理Resource结果
     */
    protected fun <T> handleResource(
        resource: Resource<T>,
        onSuccess: (T) -> Unit,
        onError: ((Throwable?, String, Int) -> Unit)? = null,
        onLoading: ((T?) -> Unit)? = null
    ) {
        when (resource) {
            is Resource.Success -> onSuccess(resource.data)
            is Resource.Error -> {
                val errorMessage = resource.message.ifEmpty { "操作失败" }
                onError?.invoke(resource.exception, errorMessage, resource.code)
                    ?: showError(errorMessage)
            }
            is Resource.Loading -> onLoading?.invoke(resource.data)
        }
    }
    
    // ========== 异常处理 ==========
    
    /**
     * 处理异常
     */
    protected open fun handleException(exception: Throwable) {
        val message = when (exception) {
            is java.net.UnknownHostException -> "网络连接失败"
            is java.net.SocketTimeoutException -> "网络请求超时"
            is java.net.ConnectException -> "无法连接到服务器"
            else -> exception.message ?: "未知错误"
        }
        showError(message)
    }
    
    // ========== 生命周期 ==========
    
    override fun onCleared() {
        super.onCleared()
        LogManager.d("[%s] ViewModel已清理", this::class.simpleName)
    }
}
