package com.yjsoft.roadtravel.basiclibrary.di

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Hilt依赖注入框架配置类
 * 
 * 功能：
 * - 管理Hilt框架的全局配置
 * - 提供依赖注入的统一入口
 * - 兼容现有的手动初始化方式
 * - 支持渐进式迁移
 * 
 * 设计原则：
 * - 不破坏现有代码逻辑
 * - 保持向后兼容性
 * - 提供清晰的迁移路径
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class HiltConfig @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "HiltConfig"
        
        /**
         * Hilt框架是否已启用
         */
        @Volatile
        private var isHiltEnabled = false
        
        /**
         * 是否使用兼容模式（同时支持手动初始化和依赖注入）
         */
        @Volatile
        private var isCompatibilityMode = true
        
        /**
         * 启用Hilt框架
         */
        fun enableHilt() {
            isHiltEnabled = true
            LogManager.d("[%s] Hilt依赖注入框架已启用", TAG)
        }

        /**
         * 禁用Hilt框架（回退到手动初始化）
         */
        fun disableHilt() {
            isHiltEnabled = false
            LogManager.d("[%s] Hilt依赖注入框架已禁用，回退到手动初始化", TAG)
        }
        
        /**
         * 检查Hilt框架是否已启用
         */
        fun isHiltEnabled(): Boolean = isHiltEnabled
        
        /**
         * 设置兼容模式
         * @param enabled true=兼容模式，false=纯DI模式
         */
        fun setCompatibilityMode(enabled: Boolean) {
            isCompatibilityMode = enabled
            LogManager.d("[%s] 兼容模式设置为: %s", TAG, enabled)
        }
        
        /**
         * 检查是否为兼容模式
         */
        fun isCompatibilityMode(): Boolean = isCompatibilityMode
    }
    
    /**
     * 初始化Hilt配置
     */
    fun initialize() {
        try {
            LogManager.d("[%s] 开始初始化Hilt配置", TAG)
            LogManager.d("[%s] HiltConfig实例已创建，Context可用: %s", TAG, context != null)

            // 启用Hilt框架
            enableHilt()

            // 默认启用兼容模式
            setCompatibilityMode(true)

            LogManager.i("[%s] Hilt配置初始化完成", TAG)
            LogManager.d("[%s] 配置状态 - Hilt启用: %s, 兼容模式: %s", TAG, isHiltEnabled(), isCompatibilityMode())

        } catch (e: Exception) {
            LogManager.e(e, "[%s] Hilt配置初始化失败", TAG)
            // 发生错误时禁用Hilt，回退到手动初始化
            disableHilt()
        }
    }
    
    /**
     * 获取应用上下文
     */
    fun getApplicationContext(): Context = context
    
    /**
     * 检查依赖注入是否可用
     * 在兼容模式下，如果Hilt不可用会回退到手动初始化
     */
    fun isDependencyInjectionAvailable(): Boolean {
        return isHiltEnabled() || isCompatibilityMode()
    }
    
    /**
     * 获取配置信息
     */
    fun getConfigInfo(): Map<String, Any> {
        return mapOf(
            "hiltEnabled" to isHiltEnabled(),
            "compatibilityMode" to isCompatibilityMode(),
            "diAvailable" to isDependencyInjectionAvailable(),
            "contextAvailable" to (context != null)
        )
    }
}
