package com.yjsoft.roadtravel.basiclibrary.permission.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionGroups
import com.yjsoft.roadtravel.basiclibrary.permission.core.MultiplePermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionStatus

/**
 * 权限检查工具类
 * 提供各种权限检查和状态判断功能
 */
object PermissionChecker {
    
    /**
     * 检查单个权限是否已授予
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查多个权限是否都已授予
     */
    fun arePermissionsGranted(context: Context, permissions: List<String>): Boolean {
        return permissions.all { isPermissionGranted(context, it) }
    }
    
    /**
     * 检查权限组是否已授予
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun isPermissionGroupGranted(context: Context, groupName: String): Boolean {
        val permissions = PermissionGroups.getPermissions(groupName)
        return arePermissionsGranted(context, permissions)
    }
    
    /**
     * 获取单个权限的详细状态
     */
    fun getPermissionState(context: Context, permission: String, activity: ComponentActivity? = null): PermissionState {
        val isGranted = isPermissionGranted(context, permission)
        val status = if (isGranted) PermissionStatus.GRANTED else PermissionStatus.DENIED
        
        // 检查是否应该显示权限说明
        val shouldShowRationale = activity?.let { 
            ActivityCompat.shouldShowRequestPermissionRationale(it, permission) 
        } ?: false
        
        // 判断是否为永久拒绝
        val isPermanentlyDenied = !isGranted && !shouldShowRationale && activity != null
        val finalStatus = when {
            isGranted -> PermissionStatus.GRANTED
            isPermanentlyDenied -> PermissionStatus.PERMANENTLY_DENIED
            else -> PermissionStatus.DENIED
        }
        
        return PermissionState(
            permission = permission,
            status = finalStatus,
            shouldShowRationale = shouldShowRationale,
            isFirstRequest = shouldShowRationale || isGranted
        )
    }
    
    /**
     * 获取多个权限的详细状态
     */
    fun getPermissionsState(context: Context, permissions: List<String>, activity: ComponentActivity? = null): MultiplePermissionState {
        val permissionStates = permissions.associateWith { permission ->
            getPermissionState(context, permission, activity)
        }
        
        return MultiplePermissionState(permissionStates)
    }
    
    /**
     * 获取权限组的详细状态
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getPermissionGroupState(context: Context, groupName: String, activity: ComponentActivity? = null): MultiplePermissionState {
        val permissions = PermissionGroups.getPermissions(groupName)
        return getPermissionsState(context, permissions, activity)
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowRequestPermissionRationale(activity: ComponentActivity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 检查是否应该显示权限说明（多个权限）
     */
    fun shouldShowRequestPermissionRationale(activity: ComponentActivity, permissions: List<String>): Boolean {
        return permissions.any { shouldShowRequestPermissionRationale(activity, it) }
    }
    
    /**
     * 获取被拒绝的权限列表
     */
    fun getDeniedPermissions(context: Context, permissions: List<String>): List<String> {
        return permissions.filter { !isPermissionGranted(context, it) }
    }
    
    /**
     * 获取已授予的权限列表
     */
    fun getGrantedPermissions(context: Context, permissions: List<String>): List<String> {
        return permissions.filter { isPermissionGranted(context, it) }
    }
    
    /**
     * 获取需要显示说明的权限列表
     */
    fun getPermissionsNeedingRationale(activity: ComponentActivity, permissions: List<String>): List<String> {
        return permissions.filter { shouldShowRequestPermissionRationale(activity, it) }
    }
    
    /**
     * 获取被永久拒绝的权限列表
     */
    fun getPermanentlyDeniedPermissions(context: Context, activity: ComponentActivity, permissions: List<String>): List<String> {
        return permissions.filter { permission ->
            !isPermissionGranted(context, permission) && 
            !shouldShowRequestPermissionRationale(activity, permission)
        }
    }
    
    /**
     * 检查权限是否为危险权限
     */
    fun isDangerousPermission(permission: String): Boolean {
        return try {
            val permissionInfo = android.content.pm.PermissionInfo()
            // 这里可以根据需要实现更详细的危险权限检查逻辑
            // 目前简单地检查一些常见的危险权限
            when (permission) {
                android.Manifest.permission.ACCESS_FINE_LOCATION,
                android.Manifest.permission.ACCESS_COARSE_LOCATION,
                android.Manifest.permission.ACCESS_BACKGROUND_LOCATION,
                android.Manifest.permission.CAMERA,
                android.Manifest.permission.RECORD_AUDIO,
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                android.Manifest.permission.READ_CONTACTS,
                android.Manifest.permission.WRITE_CONTACTS,
                android.Manifest.permission.READ_PHONE_STATE,
                android.Manifest.permission.CALL_PHONE,
                android.Manifest.permission.SEND_SMS,
                android.Manifest.permission.RECEIVE_SMS,
                android.Manifest.permission.READ_SMS -> true
                else -> {
                    // Android 13+ 的新权限
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        when (permission) {
                            android.Manifest.permission.READ_MEDIA_IMAGES,
                            android.Manifest.permission.READ_MEDIA_VIDEO,
                            android.Manifest.permission.READ_MEDIA_AUDIO,
                            android.Manifest.permission.POST_NOTIFICATIONS -> true
                            else -> false
                        }
                    } else {
                        false
                    }
                }
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查权限是否在当前Android版本中可用
     */
    fun isPermissionAvailable(permission: String): Boolean {
        return when (permission) {
            android.Manifest.permission.ACCESS_BACKGROUND_LOCATION -> {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
            }
            android.Manifest.permission.READ_MEDIA_IMAGES,
            android.Manifest.permission.READ_MEDIA_VIDEO,
            android.Manifest.permission.READ_MEDIA_AUDIO,
            android.Manifest.permission.POST_NOTIFICATIONS -> {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
            }
            android.Manifest.permission.BODY_SENSORS_BACKGROUND -> {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
            }
            else -> true
        }
    }
    
    /**
     * 过滤出在当前Android版本中可用的权限
     */
    fun filterAvailablePermissions(permissions: List<String>): List<String> {
        return permissions.filter { isPermissionAvailable(it) }
    }
    
    /**
     * 获取权限的系统名称
     */
    fun getPermissionLabel(context: Context, permission: String): String? {
        return try {
            val packageManager = context.packageManager
            val permissionInfo = packageManager.getPermissionInfo(permission, 0)
            permissionInfo.loadLabel(packageManager).toString()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取权限的系统描述
     */
    fun getPermissionDescription(context: Context, permission: String): String? {
        return try {
            val packageManager = context.packageManager
            val permissionInfo = packageManager.getPermissionInfo(permission, 0)
            permissionInfo.loadDescription(packageManager)?.toString()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查应用是否在清单文件中声明了权限
     */
    fun isPermissionDeclaredInManifest(context: Context, permission: String): Boolean {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(
                context.packageName, 
                PackageManager.GET_PERMISSIONS
            )
            packageInfo.requestedPermissions?.contains(permission) ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取应用声明的所有权限
     */
    fun getDeclaredPermissions(context: Context): List<String> {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(
                context.packageName, 
                PackageManager.GET_PERMISSIONS
            )
            packageInfo.requestedPermissions?.toList() ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
}
