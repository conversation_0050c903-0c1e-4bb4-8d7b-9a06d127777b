package com.yjsoft.roadtravel.basiclibrary.network.utils

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.network.proxy.ProxyManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.HttpURLConnection
import java.net.InetSocketAddress
import java.net.Proxy
import java.net.URL

/**
 * 网络测试工具类
 * 用于测试直连功能和代理设置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object NetworkTestUtils {
    
    private const val TAG = "NetworkTestUtils"
    
    /**
     * 测试网络连接
     * @param context 应用上下文
     * @param testUrl 测试URL，默认使用当前配置的baseUrl
     * @return 测试结果
     */
    suspend fun testNetworkConnection(
        context: Context,
        testUrl: String = NetworkConfig.getBaseUrl()
    ): NetworkTestResult = withContext(Dispatchers.IO) {
        
        LogManager.tag(LogConfig.Tags.NETWORK).i("开始网络连接测试")
        LogManager.tag(LogConfig.Tags.NETWORK).i("测试URL: $testUrl")
        
        val results = mutableListOf<ConnectionTestResult>()
        
        try {
            // 1. 测试直连
            val directResult = testDirectConnection(testUrl)
            results.add(directResult)
            
            // 2. 测试系统代理连接（如果有代理）
            if (ProxyManager.isProxyEnabled(context)) {
                val proxyResult = testProxyConnection(context, testUrl)
                results.add(proxyResult)
            }
            
            // 3. 测试当前配置的连接
            val currentResult = testCurrentConfiguration(context, testUrl)
            results.add(currentResult)
            
            NetworkTestResult(
                success = results.any { it.success },
                results = results,
                recommendation = generateRecommendation(results)
            )
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络连接测试失败")
            NetworkTestResult(
                success = false,
                results = results,
                error = e.message,
                recommendation = "网络测试过程中发生错误，请检查网络设置"
            )
        }
    }
    
    /**
     * 测试直连
     */
    private suspend fun testDirectConnection(testUrl: String): ConnectionTestResult = withContext(Dispatchers.IO) {
        LogManager.tag(LogConfig.Tags.NETWORK).d("测试直连...")
        
        try {
            val startTime = System.currentTimeMillis()
            val connection = URL(testUrl).openConnection(Proxy.NO_PROXY) as HttpURLConnection
            connection.apply {
                requestMethod = "GET"
                connectTimeout = 10000
                readTimeout = 10000
                setRequestProperty("User-Agent", "NetworkTest-Direct")
            }
            
            val responseCode = connection.responseCode
            val duration = System.currentTimeMillis() - startTime
            
            connection.disconnect()
            
            val success = responseCode in 200..299
            LogManager.tag(LogConfig.Tags.NETWORK).d("直连测试结果: $responseCode, 耗时: ${duration}ms")
            
            ConnectionTestResult(
                type = "直连",
                success = success,
                responseCode = responseCode,
                duration = duration,
                error = if (!success) "HTTP $responseCode" else null
            )
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "直连测试失败")
            ConnectionTestResult(
                type = "直连",
                success = false,
                responseCode = -1,
                duration = -1,
                error = e.message
            )
        }
    }
    
    /**
     * 测试代理连接
     */
    private suspend fun testProxyConnection(context: Context, testUrl: String): ConnectionTestResult = withContext(Dispatchers.IO) {
        LogManager.tag(LogConfig.Tags.NETWORK).d("测试代理连接...")
        
        try {
            val startTime = System.currentTimeMillis()
            val proxy = ProxyManager.getSystemProxy(context)
            val connection = URL(testUrl).openConnection(proxy) as HttpURLConnection
            connection.apply {
                requestMethod = "GET"
                connectTimeout = 10000
                readTimeout = 10000
                setRequestProperty("User-Agent", "NetworkTest-Proxy")
            }
            
            val responseCode = connection.responseCode
            val duration = System.currentTimeMillis() - startTime
            
            connection.disconnect()
            
            val success = responseCode in 200..299
            LogManager.tag(LogConfig.Tags.NETWORK).d("代理连接测试结果: $responseCode, 耗时: ${duration}ms")
            
            ConnectionTestResult(
                type = "系统代理",
                success = success,
                responseCode = responseCode,
                duration = duration,
                error = if (!success) "HTTP $responseCode" else null
            )
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "代理连接测试失败")
            ConnectionTestResult(
                type = "系统代理",
                success = false,
                responseCode = -1,
                duration = -1,
                error = e.message
            )
        }
    }
    
    /**
     * 测试当前配置的连接
     */
    private suspend fun testCurrentConfiguration(context: Context, testUrl: String): ConnectionTestResult = withContext(Dispatchers.IO) {
        LogManager.tag(LogConfig.Tags.NETWORK).d("测试当前配置...")
        
        try {
            val startTime = System.currentTimeMillis()
            val apiService = NetworkManager.getApiService(context)
            
            // 这里使用一个简单的GET请求来测试连接
            // 实际项目中可以调用一个轻量级的API接口
            val response = apiService.getInit()
            val duration = System.currentTimeMillis() - startTime
            
            LogManager.tag(LogConfig.Tags.NETWORK).d("当前配置测试结果: 成功, 耗时: ${duration}ms")
            
            ConnectionTestResult(
                type = "当前配置",
                success = true,
                responseCode = 200,
                duration = duration,
                error = null
            )
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "当前配置测试失败")
            ConnectionTestResult(
                type = "当前配置",
                success = false,
                responseCode = -1,
                duration = -1,
                error = e.message
            )
        }
    }
    
    /**
     * 生成建议
     */
    private fun generateRecommendation(results: List<ConnectionTestResult>): String {
        val directResult = results.find { it.type == "直连" }
        val proxyResult = results.find { it.type == "系统代理" }
        val currentResult = results.find { it.type == "当前配置" }
        
        return when {
            currentResult?.success == true -> "当前网络配置工作正常"
            directResult?.success == true && proxyResult?.success != true -> "建议使用直连模式，系统代理可能存在问题"
            proxyResult?.success == true && directResult?.success != true -> "建议使用系统代理，直连可能被阻止"
            directResult?.success == true && proxyResult?.success == true -> "直连和代理都可用，当前配置可能有其他问题"
            else -> "网络连接存在问题，请检查网络设置和服务器状态"
        }
    }
}

/**
 * 网络测试结果
 */
data class NetworkTestResult(
    val success: Boolean,
    val results: List<ConnectionTestResult>,
    val error: String? = null,
    val recommendation: String
)

/**
 * 连接测试结果
 */
data class ConnectionTestResult(
    val type: String,
    val success: Boolean,
    val responseCode: Int,
    val duration: Long,
    val error: String?
)
