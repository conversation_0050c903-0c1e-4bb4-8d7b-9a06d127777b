package com.yjsoft.roadtravel.basiclibrary.datastore.core

import androidx.datastore.preferences.core.MutablePreferences
import com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys
import com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.ui.activities.main.InitData
import com.yjsoft.roadtravel.ui.fragments.home.models.GeoData

import kotlinx.coroutines.flow.Flow

/**
 * DataStore数据访问层
 * 提供高级的数据访问接口和业务逻辑封装
 */
class DataStoreRepository {
    
    companion object {
        private const val TAG = "DataStoreRepository"
        
        @Volatile
        private var INSTANCE: DataStoreRepository? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(): DataStoreRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DataStoreRepository().also { INSTANCE = it }
            }
        }
    }
    
    // 用户相关操作
    
    /**
     * 设置用户信息
     */
    suspend fun setUserInfo(userId: String, userName: String, email: String) {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                preferences[CommonPreferenceKeys.USER_ID.key] = userId
                preferences[CommonPreferenceKeys.USER_NAME.key] = userName
                preferences[CommonPreferenceKeys.USER_EMAIL.key] = email
                preferences[CommonPreferenceKeys.IS_LOGGED_IN.key] = true
            }
            LogManager.d("$TAG 用户信息设置成功: $userName")
        } catch (e: Exception) {
            LogManager.e("$TAG 设置用户信息失败", e)
            throw e
        }
    }
    
    /**
     * 获取用户ID
     */
    suspend fun getUserId(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.USER_ID)
    }
    
    /**
     * 获取用户名
     */
    suspend fun getUserName(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.USER_NAME)
    }

    /**
     * 获取用户邮箱
     */
    suspend fun getUserEmail(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.USER_EMAIL)
    }
    
    /**
     * 检查是否已登录
     */
    suspend fun isLoggedIn(): Boolean {
        return DataStoreManager.getValue(CommonPreferenceKeys.IS_LOGGED_IN)
    }
    
    /**
     * 获取登录状态流
     */
    fun getLoginStatusFlow(): Flow<Boolean> {
        return DataStoreManager.getValueFlow(CommonPreferenceKeys.IS_LOGGED_IN)
    }
    
    /**
     * 用户登出
     */
    suspend fun logout() {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                preferences[CommonPreferenceKeys.USER_ID.key] = ""
                preferences[CommonPreferenceKeys.USER_NAME.key] = ""
                preferences[CommonPreferenceKeys.USER_EMAIL.key] = ""
                preferences[CommonPreferenceKeys.IS_LOGGED_IN.key] = false
            }
            LogManager.d("$TAG 用户登出成功")
        } catch (e: Exception) {
            LogManager.e("$TAG 用户登出失败", e)
            throw e
        }
    }
    
    // 应用设置相关操作
    
    /**
     * 设置首次启动标记
     */
    suspend fun setFirstLaunch(isFirst: Boolean) {
        DataStoreManager.setValue(CommonPreferenceKeys.IS_FIRST_LAUNCH, isFirst)
    }
    
    /**
     * 检查是否首次启动
     */
    suspend fun isFirstLaunch(): Boolean {
        return DataStoreManager.getValue(CommonPreferenceKeys.IS_FIRST_LAUNCH)
    }
    
    /**
     * 设置应用版本
     */
    suspend fun setAppVersion(version: String) {
        DataStoreManager.setValue(CommonPreferenceKeys.APP_VERSION, version)
    }
    
    /**
     * 获取应用版本
     */
    suspend fun getAppVersion(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.APP_VERSION)
    }
    
    /**
     * 更新最后更新时间
     */
    suspend fun updateLastUpdateTime() {
        DataStoreManager.setValue(CommonPreferenceKeys.LAST_UPDATE_TIME, System.currentTimeMillis())
    }
    
    /**
     * 获取最后更新时间
     */
    suspend fun getLastUpdateTime(): Long {
        return DataStoreManager.getValue(CommonPreferenceKeys.LAST_UPDATE_TIME)
    }
    
    // 主题和UI设置
    
    /**
     * 设置主题模式
     */
    suspend fun setThemeMode(mode: String) {
        if (mode in listOf("light", "dark", "system")) {
            DataStoreManager.setValue(CommonPreferenceKeys.THEME_MODE, mode)
            LogManager.d("$TAG 主题模式设置为: $mode")
        } else {
            LogManager.w(TAG, "无效的主题模式: $mode")
        }
    }
    
    /**
     * 获取主题模式
     */
    suspend fun getThemeMode(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.THEME_MODE)
    }
    
    /**
     * 获取主题模式流
     */
    fun getThemeModeFlow(): Flow<String> {
        return DataStoreManager.getValueFlow(CommonPreferenceKeys.THEME_MODE)
    }
    
    /**
     * 设置语言
     */
    suspend fun setLanguage(language: String) {
        DataStoreManager.setValue(CommonPreferenceKeys.LANGUAGE, language)
        LogManager.d("$TAG 语言设置为: $language")
    }
    
    /**
     * 获取语言设置
     */
    suspend fun getLanguage(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.LANGUAGE)
    }
    
    // 定位相关操作
    
    /**
     * 设置最后已知城市
     */
    suspend fun setLastKnownCity(city: String) {
        DataStoreManager.setValue(CommonPreferenceKeys.LAST_KNOWN_CITY, city)
        LogManager.d("$TAG 最后已知城市设置为: $city")
    }
    
    /**
     * 获取最后已知城市
     */
    suspend fun getLastKnownCity(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.LAST_KNOWN_CITY)
    }
    
    /**
     * 获取最后已知城市流
     */
    fun getLastKnownCityFlow(): Flow<String> {
        return DataStoreManager.getValueFlow(CommonPreferenceKeys.LAST_KNOWN_CITY)
    }
    
    /**
     * 设置定位权限状态
     */
    suspend fun setLocationPermissionGranted(granted: Boolean) {
        DataStoreManager.setValue(CommonPreferenceKeys.LOCATION_PERMISSION_GRANTED, granted)
    }
    
    /**
     * 检查定位权限是否已授予
     */
    suspend fun isLocationPermissionGranted(): Boolean {
        return DataStoreManager.getValue(CommonPreferenceKeys.LOCATION_PERMISSION_GRANTED)
    }
    
    /**
     * 设置自动定位开关
     */
    suspend fun setAutoLocationEnabled(enabled: Boolean) {
        DataStoreManager.setValue(CommonPreferenceKeys.AUTO_LOCATION_ENABLED, enabled)
    }
    
    /**
     * 检查是否启用自动定位
     */
    suspend fun isAutoLocationEnabled(): Boolean {
        return DataStoreManager.getValue(CommonPreferenceKeys.AUTO_LOCATION_ENABLED)
    }
    
    // 收藏城市管理
    
    /**
     * 添加收藏城市
     */
    suspend fun addFavoriteCity(city: String) {
        val currentFavorites = DataStoreManager.getValue(CommonPreferenceKeys.FAVORITE_CITIES)
        val newFavorites = currentFavorites.toMutableSet().apply { add(city) }
        DataStoreManager.setValue(CommonPreferenceKeys.FAVORITE_CITIES, newFavorites)
        LogManager.d("$TAG 添加收藏城市: $city")
    }
    
    /**
     * 移除收藏城市
     */
    suspend fun removeFavoriteCity(city: String) {
        val currentFavorites = DataStoreManager.getValue(CommonPreferenceKeys.FAVORITE_CITIES)
        val newFavorites = currentFavorites.toMutableSet().apply { remove(city) }
        DataStoreManager.setValue(CommonPreferenceKeys.FAVORITE_CITIES, newFavorites)
        LogManager.d("$TAG 移除收藏城市: $city")
    }
    
    /**
     * 获取收藏城市列表
     */
    suspend fun getFavoriteCities(): Set<String> {
        return DataStoreManager.getValue(CommonPreferenceKeys.FAVORITE_CITIES)
    }
    
    /**
     * 获取收藏城市流
     */
    fun getFavoriteCitiesFlow(): Flow<Set<String>> {
        return DataStoreManager.getValueFlow(CommonPreferenceKeys.FAVORITE_CITIES)
    }
    
    /**
     * 检查城市是否已收藏
     */
    suspend fun isCityFavorited(city: String): Boolean {
        return getFavoriteCities().contains(city)
    }
    
    // 通用操作
    
    /**
     * 获取自定义值
     */
    suspend fun <T> getValue(key: PreferenceKey<T>): T {
        return DataStoreManager.getValue(key)
    }
    
    /**
     * 设置自定义值
     */
    suspend fun <T> setValue(key: PreferenceKey<T>, value: T) {
        DataStoreManager.setValue(key, value)
    }
    
    /**
     * 获取自定义值流
     */
    fun <T> getValueFlow(key: PreferenceKey<T>): Flow<T> {
        return DataStoreManager.getValueFlow(key)
    }
    
    /**
     * 清除所有数据
     */
    suspend fun clearAllData() {
        DataStoreManager.clearAll()
        LogManager.d("$TAG 所有数据已清除")
    }
    
    // 初始化数据相关操作
    
    /**
     * 保存初始化数据
     */
    suspend fun saveInitData(initData: InitData) {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                preferences[CommonPreferenceKeys.INIT_ZONE_ID.key] = initData.zoneId
                preferences[CommonPreferenceKeys.INIT_ZONE_NAME.key] = initData.zoneName
                preferences[CommonPreferenceKeys.INIT_LAT.key] = initData.lat
                preferences[CommonPreferenceKeys.INIT_LNG.key] = initData.lng
                preferences[CommonPreferenceKeys.INIT_SERVICE_WX.key] = initData.serviceWx
                preferences[CommonPreferenceKeys.INIT_SERVICE_EMAIL.key] = initData.serviceEmail
                preferences[CommonPreferenceKeys.INIT_SHARE_TASK_ID.key] = initData.shareMiniTask.taskId
                preferences[CommonPreferenceKeys.INIT_SHARE_TASK_DESC.key] = initData.shareMiniTask.shortDesc
                preferences[CommonPreferenceKeys.INIT_SHARE_TASK_VALUE.key] = initData.shareMiniTask.value
                preferences[CommonPreferenceKeys.INIT_PLAN_COST_AMOUNT.key] = initData.planCostAmount
                preferences[CommonPreferenceKeys.INIT_BIND_INVITE_GIVE.key] = initData.bindInviteGive
                preferences[CommonPreferenceKeys.INIT_PLAN_PDF_COST_AMOUNT.key] = initData.planPdfCostAmount
                preferences[CommonPreferenceKeys.INIT_PACKAGE_BUY_SERVICE.key] = initData.packageBuyService
                preferences[CommonPreferenceKeys.INIT_AD_TYPE.key] = initData.adConfig.integralPop.type
                preferences[CommonPreferenceKeys.INIT_AD_ID.key] = initData.adConfig.integralPop.adId
                preferences[CommonPreferenceKeys.INIT_AD_AMOUNT.key] = initData.adConfig.integralPop.amount
            }
            LogManager.d("$TAG 初始化数据保存成功: ${initData.zoneName}")
        } catch (e: Exception) {
            LogManager.e("$TAG 保存初始化数据失败", e)
            throw e
        }
    }
    
    /**
     * 获取初始化区域ID
     */
    suspend fun getInitZoneId(): Int {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_ZONE_ID)
    }
    
    /**
     * 获取初始化区域名称
     */
    suspend fun getInitZoneName(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_ZONE_NAME)
    }
    
    /**
     * 获取初始化位置坐标
     */
    suspend fun getInitLocation(): Pair<Double, Double> {
        val lat = DataStoreManager.getValue(CommonPreferenceKeys.INIT_LAT)
        val lng = DataStoreManager.getValue(CommonPreferenceKeys.INIT_LNG)
        return Pair(lat, lng)
    }
    
    /**
     * 获取客服微信
     */
    suspend fun getServiceWx(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_SERVICE_WX)
    }
    
    /**
     * 获取客服邮箱
     */
    suspend fun getServiceEmail(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_SERVICE_EMAIL)
    }
    
    /**
     * 获取分享任务信息
     */
    suspend fun getShareTaskInfo(): Triple<Int, String, Int> {
        val taskId = DataStoreManager.getValue(CommonPreferenceKeys.INIT_SHARE_TASK_ID)
        val desc = DataStoreManager.getValue(CommonPreferenceKeys.INIT_SHARE_TASK_DESC)
        val value = DataStoreManager.getValue(CommonPreferenceKeys.INIT_SHARE_TASK_VALUE)
        return Triple(taskId, desc, value)
    }
    
    /**
     * 获取规划费用金额
     */
    suspend fun getPlanCostAmount(): Int {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_PLAN_COST_AMOUNT)
    }
    
    /**
     * 获取绑定邀请奖励
     */
    suspend fun getBindInviteGive(): Int {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_BIND_INVITE_GIVE)
    }
    
    /**
     * 获取规划PDF费用金额
     */
    suspend fun getPlanPdfCostAmount(): Int {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_PLAN_PDF_COST_AMOUNT)
    }
    
    /**
     * 获取套餐购买服务链接
     */
    suspend fun getPackageBuyService(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.INIT_PACKAGE_BUY_SERVICE)
    }
    
    /**
     * 获取广告配置信息
     */
    suspend fun getAdConfig(): Triple<String, String, Int> {
        val type = DataStoreManager.getValue(CommonPreferenceKeys.INIT_AD_TYPE)
        val adId = DataStoreManager.getValue(CommonPreferenceKeys.INIT_AD_ID)
        val amount = DataStoreManager.getValue(CommonPreferenceKeys.INIT_AD_AMOUNT)
        return Triple(type, adId, amount)
    }
    
    /**
     * 更新初始化位置信息
     */
    suspend fun updateInitLocationInfo(zoneName: String, lat: Double, lng: Double) {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                preferences[CommonPreferenceKeys.INIT_ZONE_NAME.key] = zoneName
                preferences[CommonPreferenceKeys.INIT_LAT.key] = lat
                preferences[CommonPreferenceKeys.INIT_LNG.key] = lng
            }
            LogManager.d("$TAG 初始化位置信息更新成功: $zoneName ($lat, $lng)")
        } catch (e: Exception) {
            LogManager.e("$TAG 更新初始化位置信息失败", e)
            throw e
        }
    }
    
    /**
     * 更新地理信息数据
     */
    suspend fun updateGeoInfo(geoData: GeoData) {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                // 更新同名字段
                preferences[CommonPreferenceKeys.INIT_LAT.key] = geoData.lat
                preferences[CommonPreferenceKeys.INIT_LNG.key] = geoData.lng
                preferences[CommonPreferenceKeys.INIT_ZONE_NAME.key] = geoData.city
                preferences[CommonPreferenceKeys.INIT_ZONE_ID.key] = geoData.zoneId
                
                // 保存地理信息特有的数据
                preferences[CommonPreferenceKeys.GEO_ADDRESS.key] = geoData.address
                preferences[CommonPreferenceKeys.GEO_CITY.key] = geoData.city
                preferences[CommonPreferenceKeys.GEO_ZONE_ID.key] = geoData.zoneId
            }
            LogManager.d("$TAG 地理信息更新成功: ${geoData.city} (${geoData.lat}, ${geoData.lng})")
        } catch (e: Exception) {
            LogManager.e("$TAG 更新地理信息失败", e)
            throw e
        }
    }
    
    /**
     * 获取地理信息地址
     */
    suspend fun getGeoAddress(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.GEO_ADDRESS)
    }
    
    /**
     * 获取地理信息城市
     */
    suspend fun getGeoCity(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.GEO_CITY)
    }
    
    /**
     * 获取地理信息区域ID
     */
    suspend fun getGeoZoneId(): Int {
        return DataStoreManager.getValue(CommonPreferenceKeys.GEO_ZONE_ID)
    }

    // 对话ID相关操作

    /**
     * 设置对话ID
     */
    suspend fun setConversionID(conversionId: String) {
        DataStoreManager.setValue(CommonPreferenceKeys.CONVERSION_ID, conversionId)
        LogManager.d("$TAG 对话ID设置为: $conversionId")
    }

    /**
     * 获取对话ID
     */
    suspend fun getConversionID(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.CONVERSION_ID)
    }

    /**
     * 获取对话ID流 - 监听对话ID变化
     */
    fun getConversionIDFlow(): Flow<String> {
        return DataStoreManager.getValueFlow(CommonPreferenceKeys.CONVERSION_ID)
    }

    // 微信用户信息相关操作
    
    /**
     * 保存微信用户信息
     */
    suspend fun saveWeChatUserInfo(
        openId: String,
        unionId: String?,
        nickname: String,
        avatar: String?,
        sex: String,
        province: String?,
        city: String?,
        country: String?,
        accessToken: String,
        refreshToken: String?,
        expiresIn: Int
    ) {
        try {
            val currentTime = System.currentTimeMillis()
            val expiresTime = currentTime + (expiresIn * 1000L) // 转换为毫秒
            
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                // 保存微信用户基本信息
                preferences[CommonPreferenceKeys.WECHAT_OPEN_ID.key] = openId
                preferences[CommonPreferenceKeys.WECHAT_UNION_ID.key] = unionId ?: ""
                preferences[CommonPreferenceKeys.WECHAT_NICKNAME.key] = nickname
                preferences[CommonPreferenceKeys.WECHAT_AVATAR.key] = avatar ?: ""
                preferences[CommonPreferenceKeys.WECHAT_SEX.key] = sex
                preferences[CommonPreferenceKeys.WECHAT_PROVINCE.key] = province ?: ""
                preferences[CommonPreferenceKeys.WECHAT_CITY.key] = city ?: ""
                preferences[CommonPreferenceKeys.WECHAT_COUNTRY.key] = country ?: ""
                
                // 保存微信令牌信息
                preferences[CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key] = accessToken
                preferences[CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key] = refreshToken ?: ""
                preferences[CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key] = expiresTime
                preferences[CommonPreferenceKeys.WECHAT_LOGIN_TIME.key] = currentTime
                
                // 更新通用用户信息
                preferences[CommonPreferenceKeys.USER_ID.key] = openId
                preferences[CommonPreferenceKeys.USER_NAME.key] = nickname
                preferences[CommonPreferenceKeys.IS_LOGGED_IN.key] = true
            }
            LogManager.d("$TAG 微信用户信息保存成功: $nickname ($openId)")
        } catch (e: Exception) {
            LogManager.e("$TAG 保存微信用户信息失败", e)
            throw e
        }
    }
    
    /**
     * 获取微信OpenID
     */
    suspend fun getWeChatOpenId(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_OPEN_ID)
    }
    
    /**
     * 获取微信UnionID
     */
    suspend fun getWeChatUnionId(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_UNION_ID)
    }
    
    /**
     * 获取微信昵称
     */
    suspend fun getWeChatNickname(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_NICKNAME)
    }
    
    /**
     * 获取微信头像URL
     */
    suspend fun getWeChatAvatar(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_AVATAR)
    }
    
    /**
     * 获取微信性别
     */
    suspend fun getWeChatSex(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_SEX)
    }
    
    /**
     * 获取微信省份
     */
    suspend fun getWeChatProvince(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_PROVINCE)
    }
    
    /**
     * 获取微信城市
     */
    suspend fun getWeChatCity(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_CITY)
    }
    
    /**
     * 获取微信国家
     */
    suspend fun getWeChatCountry(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_COUNTRY)
    }
    
    /**
     * 获取微信访问令牌
     */
    suspend fun getWeChatAccessToken(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_ACCESS_TOKEN)
    }
    
    /**
     * 获取微信刷新令牌
     */
    suspend fun getWeChatRefreshToken(): String {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_REFRESH_TOKEN)
    }
    
    /**
     * 获取微信令牌过期时间
     */
    suspend fun getWeChatTokenExpiresTime(): Long {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME)
    }
    
    /**
     * 获取微信登录时间
     */
    suspend fun getWeChatLoginTime(): Long {
        return DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_LOGIN_TIME)
    }
    
    /**
     * 检查微信令牌是否过期
     */
    suspend fun isWeChatTokenExpired(): Boolean {
        val expiresTime = getWeChatTokenExpiresTime()
        return if (expiresTime == 0L) {
            true // 如果没有过期时间，认为已过期
        } else {
            System.currentTimeMillis() >= expiresTime
        }
    }
    
    /**
     * 获取完整的微信用户信息
     */
    suspend fun getWeChatUserInfo(): WeChatUserInfoData? {
        return try {
            val openId = getWeChatOpenId()
            if (openId.isEmpty()) {
                null
            } else {
                WeChatUserInfoData(
                    openId = openId,
                    unionId = getWeChatUnionId().takeIf { it.isNotEmpty() },
                    nickname = getWeChatNickname(),
                    avatar = getWeChatAvatar().takeIf { it.isNotEmpty() },
                    sex = getWeChatSex(),
                    province = getWeChatProvince().takeIf { it.isNotEmpty() },
                    city = getWeChatCity().takeIf { it.isNotEmpty() },
                    country = getWeChatCountry().takeIf { it.isNotEmpty() },
                    accessToken = getWeChatAccessToken(),
                    refreshToken = getWeChatRefreshToken().takeIf { it.isNotEmpty() },
                    expiresTime = getWeChatTokenExpiresTime(),
                    loginTime = getWeChatLoginTime()
                )
            }
        } catch (e: Exception) {
            LogManager.e("$TAG 获取微信用户信息失败", e)
            null
        }
    }
    
    /**
     * 清除微信用户信息
     */
    suspend fun clearWeChatUserInfo() {
        try {
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                // 清除微信用户信息
                preferences.remove(CommonPreferenceKeys.WECHAT_OPEN_ID.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_UNION_ID.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_NICKNAME.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_AVATAR.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_SEX.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_PROVINCE.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_CITY.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_COUNTRY.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_LOGIN_TIME.key)
                
                // 清除通用用户信息
                preferences[CommonPreferenceKeys.USER_ID.key] = ""
                preferences[CommonPreferenceKeys.USER_NAME.key] = ""
                preferences[CommonPreferenceKeys.IS_LOGGED_IN.key] = false
            }
            LogManager.d("$TAG 微信用户信息清除成功")
        } catch (e: Exception) {
            LogManager.e("$TAG 清除微信用户信息失败", e)
            throw e
        }
    }
    
    /**
     * 更新微信访问令牌
     */
    suspend fun updateWeChatAccessToken(accessToken: String, refreshToken: String?, expiresIn: Int) {
        try {
            val currentTime = System.currentTimeMillis()
            val expiresTime = currentTime + (expiresIn * 1000L)
            
            DataStoreManager.batchOperation { preferences: MutablePreferences ->
                preferences[CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key] = accessToken
                if (refreshToken != null) {
                    preferences[CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key] = refreshToken
                }
                preferences[CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key] = expiresTime
            }
            LogManager.d("$TAG 微信访问令牌更新成功")
        } catch (e: Exception) {
            LogManager.e("$TAG 更新微信访问令牌失败", e)
            throw e
        }
    }
}

/**
 * 微信用户信息数据类
 * 用于从 DataStore 获取完整的微信用户信息
 */
data class WeChatUserInfoData(
    val openId: String,
    val unionId: String?,
    val nickname: String,
    val avatar: String?,
    val sex: String,
    val province: String?,
    val city: String?,
    val country: String?,
    val accessToken: String,
    val refreshToken: String?,
    val expiresTime: Long,
    val loginTime: Long
) {
    /**
     * 检查令牌是否过期
     */
    fun isTokenExpired(): Boolean {
        return System.currentTimeMillis() >= expiresTime
    }
    
    /**
     * 获取令牌剩余有效时间（秒）
     */
    fun getTokenRemainingTime(): Long {
        val remaining = (expiresTime - System.currentTimeMillis()) / 1000
        return if (remaining > 0) remaining else 0
    }
}
