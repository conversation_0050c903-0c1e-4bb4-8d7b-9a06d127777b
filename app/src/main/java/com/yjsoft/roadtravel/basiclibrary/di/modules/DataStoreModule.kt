package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据存储框架依赖注入模块
 * 
 * 功能：
 * - 提供DataStoreManager的依赖注入
 * - 提供DataStoreRepository的依赖注入
 * - 管理数据存储框架的生命周期
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object DataStoreModule {
    
    /**
     * 提供DataStoreManager实例
     */
    @Provides
    @Singleton
    fun provideDataStoreManager(
        @ApplicationContext context: Context
    ): DataStoreManager {
        return DataStoreManagerProvider.getInstance(context)
    }
    
    /**
     * 提供DataStoreRepository实例
     */
    @Provides
    @Singleton
    fun provideDataStoreRepository(): DataStoreRepository {
        return DataStoreRepositoryProvider.getInstance()
    }
    
    /**
     * DataStoreManager提供器
     */
    private object DataStoreManagerProvider {
        @Volatile
        private var instance: DataStoreManager? = null
        
        fun getInstance(context: Context): DataStoreManager {
            return instance ?: synchronized(this) {
                instance ?: createDataStoreManager(context).also { instance = it }
            }
        }
        
        private fun createDataStoreManager(context: Context): DataStoreManager {
            // 检查DataStoreManager是否已经通过手动方式初始化
            return if (DataStoreManager.isInitialized()) {
                // 如果已经初始化，直接返回现有实例
                DataStoreManager
            } else {
                // 如果未初始化，进行初始化
                DataStoreManager.apply {
                    init(context)
                }
            }
        }
    }
    
    /**
     * DataStoreRepository提供器
     */
    private object DataStoreRepositoryProvider {
        @Volatile
        private var instance: DataStoreRepository? = null
        
        fun getInstance(): DataStoreRepository {
            return instance ?: synchronized(this) {
                instance ?: createDataStoreRepository().also { instance = it }
            }
        }
        
        private fun createDataStoreRepository(): DataStoreRepository {
            // DataStoreRepository使用单例模式
            return DataStoreRepository.getInstance()
        }
    }
}

/**
 * DataStoreManager扩展函数
 * 用于检查是否已初始化
 */
private fun DataStoreManager.isInitialized(): Boolean {
    return try {
        // DataStoreManager是object，检查其内部的isInitialized字段
        // 通过反射访问私有字段来检查初始化状态
        val field = DataStoreManager::class.java.getDeclaredField("isInitialized")
        field.isAccessible = true
        field.getBoolean(DataStoreManager)
    } catch (e: Exception) {
        // 如果反射失败，尝试调用一个需要初始化的方法
        try {
            // 调用getAllData方法，如果未初始化会抛出异常
            kotlinx.coroutines.runBlocking {
                getAllData()
            }
            true
        } catch (ex: Exception) {
            false
        }
    }
}
