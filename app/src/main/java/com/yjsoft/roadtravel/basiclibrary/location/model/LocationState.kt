package com.yjsoft.roadtravel.basiclibrary.location.model

/**
 * 定位状态枚举
 */
enum class LocationStatus {
    /** 未初始化 */
    UNINITIALIZED,
    /** 初始化中 */
    INITIALIZING,
    /** 定位中 */
    LOCATING,
    /** 定位成功 */
    SUCCESS,
    /** 定位失败 */
    FAILED,
    /** 已停止 */
    STOPPED,
    /** 权限被拒绝 */
    PERMISSION_DENIED,
    /** GPS未开启 */
    GPS_DISABLED,
    /** 网络不可用 */
    NETWORK_UNAVAILABLE
}

/**
 * 定位状态数据类
 */
data class LocationState(
    /** 当前状态 */
    val status: LocationStatus = LocationStatus.UNINITIALIZED,
    /** 定位数据 */
    val locationData: LocationData? = null,
    /** 错误信息 */
    val error: LocationError? = null,
    /** 是否正在定位 */
    val isLocating: Boolean = false,
    /** 最后更新时间 */
    val lastUpdateTime: Long = 0L,
    /** 定位次数 */
    val locationCount: Int = 0
) {
    /**
     * 是否有有效的定位数据
     */
    val hasValidLocation: Boolean
        get() = locationData?.isValid == true
    
    /**
     * 是否可以开始定位
     */
    val canStartLocation: Boolean
        get() = status in listOf(
            LocationStatus.UNINITIALIZED,
            LocationStatus.STOPPED,
            LocationStatus.FAILED
        )
    
    /**
     * 是否需要权限
     */
    val needsPermission: Boolean
        get() = status == LocationStatus.PERMISSION_DENIED
    
    /**
     * 是否需要开启GPS
     */
    val needsGPS: Boolean
        get() = status == LocationStatus.GPS_DISABLED
    
    /**
     * 是否需要网络
     */
    val needsNetwork: Boolean
        get() = status == LocationStatus.NETWORK_UNAVAILABLE
    
    /**
     * 获取状态描述
     */
    val statusDescription: String
        get() = when (status) {
            LocationStatus.UNINITIALIZED -> "未初始化"
            LocationStatus.INITIALIZING -> "初始化中..."
            LocationStatus.LOCATING -> "定位中..."
            LocationStatus.SUCCESS -> "定位成功"
            LocationStatus.FAILED -> "定位失败"
            LocationStatus.STOPPED -> "已停止定位"
            LocationStatus.PERMISSION_DENIED -> "权限被拒绝"
            LocationStatus.GPS_DISABLED -> "GPS未开启"
            LocationStatus.NETWORK_UNAVAILABLE -> "网络不可用"
        }
    
    /**
     * 创建定位中状态
     */
    fun toLocating(): LocationState {
        return copy(
            status = LocationStatus.LOCATING,
            isLocating = true,
            error = null
        )
    }
    
    /**
     * 创建成功状态
     */
    fun toSuccess(locationData: LocationData): LocationState {
        return copy(
            status = LocationStatus.SUCCESS,
            locationData = locationData,
            isLocating = false,
            error = null,
            lastUpdateTime = System.currentTimeMillis(),
            locationCount = locationCount + 1
        )
    }
    
    /**
     * 创建失败状态
     */
    fun toFailed(error: LocationError): LocationState {
        return copy(
            status = LocationStatus.FAILED,
            error = error,
            isLocating = false
        )
    }
    
    /**
     * 创建停止状态
     */
    fun toStopped(): LocationState {
        return copy(
            status = LocationStatus.STOPPED,
            isLocating = false,
            error = null
        )
    }
    
    /**
     * 创建权限被拒绝状态
     */
    fun toPermissionDenied(): LocationState {
        return copy(
            status = LocationStatus.PERMISSION_DENIED,
            isLocating = false,
            error = LocationError.permissionDenied()
        )
    }
    
    /**
     * 创建GPS未开启状态
     */
    fun toGPSDisabled(): LocationState {
        return copy(
            status = LocationStatus.GPS_DISABLED,
            isLocating = false,
            error = LocationError.gpsDisabled()
        )
    }
    
    /**
     * 创建网络不可用状态
     */
    fun toNetworkUnavailable(): LocationState {
        return copy(
            status = LocationStatus.NETWORK_UNAVAILABLE,
            isLocating = false,
            error = LocationError.networkUnavailable()
        )
    }
    
    companion object {
        /**
         * 创建初始状态
         */
        fun initial(): LocationState {
            return LocationState()
        }
        
        /**
         * 创建初始化中状态
         */
        fun initializing(): LocationState {
            return LocationState(
                status = LocationStatus.INITIALIZING,
                isLocating = false
            )
        }
    }
}
