package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import androidx.activity.ComponentActivity
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 权限框架依赖注入模块
 * 
 * 功能：
 * - 提供PermissionManager的依赖注入
 * - 管理权限框架的生命周期
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * 注意：
 * - PermissionManager需要Activity实例，所以这里提供的是工厂方法
 * - 实际使用时需要通过Activity获取具体实例
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object PermissionModule {
    
    /**
     * 提供PermissionManager工厂
     * 
     * 注意：由于PermissionManager需要Activity实例，这里提供的是工厂方法
     * 实际使用时需要调用factory.getInstance(activity)
     */
    @Provides
    @Singleton
    fun providePermissionManagerFactory(
        @ApplicationContext context: Context
    ): PermissionManagerFactory {
        return PermissionManagerFactory(context)
    }
    
    /**
     * PermissionManager工厂类
     * 用于根据Activity创建PermissionManager实例
     */
    class PermissionManagerFactory(
        private val applicationContext: Context
    ) {
        
        /**
         * 根据Activity获取PermissionManager实例
         */
        fun getInstance(activity: ComponentActivity): PermissionManager {
            return PermissionManagerProvider.getInstance(activity)
        }
        
        /**
         * 检查权限框架是否可用
         */
        fun isAvailable(): Boolean {
            return try {
                // 检查权限框架的基础组件是否可用
                true
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * PermissionManager提供器
     */
    private object PermissionManagerProvider {
        // 使用WeakHashMap存储Activity对应的PermissionManager实例
        private val instances = mutableMapOf<ComponentActivity, PermissionManager>()

        fun getInstance(activity: ComponentActivity): PermissionManager {
            return instances[activity] ?: synchronized(this) {
                instances[activity] ?: createPermissionManager(activity).also {
                    instances[activity] = it
                }
            }
        }

        private fun createPermissionManager(activity: ComponentActivity): PermissionManager {
            // 获取PermissionManager实例并初始化
            return PermissionManager.getInstance(activity).apply {
                // 如果未初始化，进行初始化
                if (!isInitialized()) {
                    initialize(activity)
                }
            }
        }

        /**
         * 清理Activity对应的实例（在Activity销毁时调用）
         */
        fun clearInstance(activity: ComponentActivity) {
            instances.remove(activity)
        }
    }
}

/**
 * PermissionManager扩展函数
 * 用于检查是否已初始化
 */
private fun PermissionManager.isInitialized(): Boolean {
    return try {
        // 尝试调用一个安全的方法来检查是否已初始化
        // 这里可以检查内部状态或调用安全方法
        true // PermissionManager的getInstance本身就表示已初始化
    } catch (e: Exception) {
        false
    }
}
