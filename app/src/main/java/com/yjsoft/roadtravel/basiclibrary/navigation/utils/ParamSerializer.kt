package com.yjsoft.roadtravel.basiclibrary.navigation.utils

import android.content.Intent
import android.os.Bundle
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import com.yjsoft.roadtravel.basiclibrary.navigation.models.NavigationParams
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 导航参数序列化工具类
 * 负责复杂对象的序列化、反序列化和Intent参数处理
 */
object ParamSerializer {
    
    const val TAG = "ParamSerializer %s"
    private const val SERIALIZED_DATA_KEY = "nav_serialized_data"
    
    // 配置JSON序列化器
    val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        encodeDefaults = true
    }
    
    /**
     * 将对象序列化为JSON字符串
     */
    inline fun <reified T> serializeToJson(data: T): String? {
        return try {
            json.encodeToString(data)
        } catch (e: Exception) {
            LogManager.e(TAG, "序列化失败: ${T::class.simpleName}", e)
            null
        }
    }
    
    /**
     * 从JSON字符串反序列化对象
     */
    inline fun <reified T> deserializeFromJson(jsonString: String): T? {
        return try {
            json.decodeFromString<T>(jsonString)
        } catch (e: Exception) {
            LogManager.e(TAG, "反序列化失败: ${T::class.simpleName}", e)
            null
        }
    }
    
    /**
     * 将NavigationParams添加到Intent中
     */
    fun addParamsToIntent(intent: Intent, params: NavigationParams) {
        try {
            // 添加基础类型参数
            params.stringParams.forEach { (key, value) ->
                intent.putExtra(key, value)
            }
            params.intParams.forEach { (key, value) ->
                intent.putExtra(key, value)
            }
            params.booleanParams.forEach { (key, value) ->
                intent.putExtra(key, value)
            }
            params.longParams.forEach { (key, value) ->
                intent.putExtra(key, value)
            }
            params.doubleParams.forEach { (key, value) ->
                intent.putExtra(key, value)
            }
            
            // 序列化复杂的JSON参数
            if (params.jsonParams.isNotEmpty()) {
                val serializedJson = serializeToJson(params.jsonParams)
                serializedJson?.let {
                    intent.putExtra(SERIALIZED_DATA_KEY, it)
                }
            }
            
            LogManager.d(TAG, "参数已添加到Intent，共${params.getTotalParamsCount()}个")
        } catch (e: Exception) {
            LogManager.e(TAG, "添加参数到Intent失败", e)
        }
    }
    
    /**
     * 从Intent中提取NavigationParams
     */
    fun extractParamsFromIntent(intent: Intent): NavigationParams {
        return try {
            val builder = NavigationParams.builder()
            val extras = intent.extras ?: return NavigationParams.empty()
            
            // 提取各种类型的参数
            extras.keySet().forEach { key ->
                when {
                    key == SERIALIZED_DATA_KEY -> {
                        // 处理序列化的JSON数据
                        val jsonString = extras.getString(key)
                        jsonString?.let {
                            val jsonParams = deserializeFromJson<Map<String, kotlinx.serialization.json.JsonElement>>(it)
                            jsonParams?.forEach { (jsonKey, jsonValue) ->
                                builder.putJsonElement(jsonKey, jsonValue)
                            }
                        }
                    }
                    extras.get(key) is String -> {
                        builder.putString(key, extras.getString(key) ?: "")
                    }
                    extras.get(key) is Int -> {
                        builder.putInt(key, extras.getInt(key))
                    }
                    extras.get(key) is Boolean -> {
                        builder.putBoolean(key, extras.getBoolean(key))
                    }
                    extras.get(key) is Long -> {
                        builder.putLong(key, extras.getLong(key))
                    }
                    extras.get(key) is Double -> {
                        builder.putDouble(key, extras.getDouble(key))
                    }
                }
            }
            
            val params = builder.build()
            LogManager.d(TAG, "从Intent提取参数，共${params.getTotalParamsCount()}个")
            params
        } catch (e: Exception) {
            LogManager.e(TAG, "从Intent提取参数失败", e)
            NavigationParams.empty()
        }
    }
    
    /**
     * 将NavigationParams转换为Bundle
     */
    fun paramsToBundle(params: NavigationParams): Bundle {
        val bundle = Bundle()
        try {
            // 添加基础类型参数
            params.stringParams.forEach { (key, value) ->
                bundle.putString(key, value)
            }
            params.intParams.forEach { (key, value) ->
                bundle.putInt(key, value)
            }
            params.booleanParams.forEach { (key, value) ->
                bundle.putBoolean(key, value)
            }
            params.longParams.forEach { (key, value) ->
                bundle.putLong(key, value)
            }
            params.doubleParams.forEach { (key, value) ->
                bundle.putDouble(key, value)
            }
            
            // 序列化JSON参数
            if (params.jsonParams.isNotEmpty()) {
                val serializedJson = serializeToJson(params.jsonParams)
                serializedJson?.let {
                    bundle.putString(SERIALIZED_DATA_KEY, it)
                }
            }
            
            LogManager.d(TAG, "NavigationParams转换为Bundle完成")
        } catch (e: Exception) {
            LogManager.e(TAG, "NavigationParams转换为Bundle失败", e)
        }
        return bundle
    }
    
    /**
     * 从Bundle中提取NavigationParams
     */
    fun bundleToParams(bundle: Bundle): NavigationParams {
        return try {
            val builder = NavigationParams.builder()
            
            bundle.keySet().forEach { key ->
                when {
                    key == SERIALIZED_DATA_KEY -> {
                        val jsonString = bundle.getString(key)
                        jsonString?.let {
                            val jsonParams = deserializeFromJson<Map<String, kotlinx.serialization.json.JsonElement>>(it)
                            jsonParams?.forEach { (jsonKey, jsonValue) ->
                                builder.putJsonElement(jsonKey, jsonValue)
                            }
                        }
                    }
                    bundle.get(key) is String -> {
                        builder.putString(key, bundle.getString(key) ?: "")
                    }
                    bundle.get(key) is Int -> {
                        builder.putInt(key, bundle.getInt(key))
                    }
                    bundle.get(key) is Boolean -> {
                        builder.putBoolean(key, bundle.getBoolean(key))
                    }
                    bundle.get(key) is Long -> {
                        builder.putLong(key, bundle.getLong(key))
                    }
                    bundle.get(key) is Double -> {
                        builder.putDouble(key, bundle.getDouble(key))
                    }
                }
            }
            
            val params = builder.build()
            LogManager.d(TAG, "从Bundle提取参数完成")
            params
        } catch (e: Exception) {
            LogManager.e(TAG, "从Bundle提取参数失败", e)
            NavigationParams.empty()
        }
    }
    
    /**
     * URL编码参数
     */
    fun encodeUrlParams(params: Map<String, String>): String {
        return try {
            params.entries.joinToString("&") { (key, value) ->
                "${java.net.URLEncoder.encode(key, "UTF-8")}=${java.net.URLEncoder.encode(value, "UTF-8")}"
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "URL参数编码失败", e)
            ""
        }
    }
    
    /**
     * URL解码参数
     */
    fun decodeUrlParams(queryString: String): Map<String, String> {
        return try {
            if (queryString.isEmpty()) return emptyMap()
            
            queryString.split("&").mapNotNull { pair ->
                val parts = pair.split("=", limit = 2)
                if (parts.size == 2) {
                    val key = java.net.URLDecoder.decode(parts[0], "UTF-8")
                    val value = java.net.URLDecoder.decode(parts[1], "UTF-8")
                    key to value
                } else null
            }.toMap()
        } catch (e: Exception) {
            LogManager.e(TAG, "URL参数解码失败", e)
            emptyMap()
        }
    }
}

/**
 * NavigationParams扩展函数：获取总参数数量
 */
fun NavigationParams.getTotalParamsCount(): Int {
    return stringParams.size + intParams.size + booleanParams.size + 
           longParams.size + doubleParams.size + jsonParams.size
} 