package com.yjsoft.roadtravel.basiclibrary.network

import com.yjsoft.roadtravel.common.models.CollectPlanResponse
import com.yjsoft.roadtravel.common.models.UnCollectPlanResponse
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryResponse
import com.yjsoft.roadtravel.ui.activities.aichat.models.InitChatResponse
import com.yjsoft.roadtravel.ui.activities.login.models.LoginResponse
import com.yjsoft.roadtravel.ui.fragments.home.models.HomeApiResponse
import com.yjsoft.roadtravel.ui.fragments.home.models.GeoApiResponse
import com.yjsoft.roadtravel.ui.fragments.home.models.PlansApiResponse
import com.yjsoft.roadtravel.ui.activities.main.InitApiResponse
import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailResponse
import com.yjsoft.roadtravel.ui.activities.plan.models.PolylineResponse
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface ApiService {
    // 初始化接口
    @GET("api/v1/front/init")
    suspend fun getInit(): InitApiResponse

    // 首页接口
    @GET("api/v1/front/index")
    suspend fun getHomeIndex(): HomeApiResponse

    // 地理信息接口
    @GET("api/v1/front/geo")
    suspend fun getGeoInfo(
        @Query("query_lat") lat: Double,
        @Query("query_lng") lng: Double
    ): GeoApiResponse

    // 计划列表接口
    @GET("api/v1/front/plans")
    suspend fun getPlans(
        @Query("hot_tag") hotTag: String
    ): PlansApiResponse

    // 登录接口
    @POST("api/v1/front/login")
    suspend fun login(
        @Query("code") code: String
    ): LoginResponse

    //行程详情
    @GET("api/v1/front/plan/detail")
    suspend fun getPlanDetail(
        @Query("plan_id") planId: Int,
        @Query("ai_reqid") aiReqid: String? = null
    ): PlanDetailResponse

    //获取地图路线数据
    @GET("api/v1/front/plan/waymap_v2")
    suspend fun getPlanPolyline(
        @Query("plan_id") planId: Int,
        @Query("ai_reqid") aiReqid: String? = null
    ): PolylineResponse

    //收藏行程
    @POST("api/v1/front/plan/submit")
    suspend fun collectPlan(
        @Query("ai_reqid") aiReqid: String
    ): CollectPlanResponse

    //取消收藏
    @POST("api/v1/front/user/planremove")
    suspend fun unCollectPlan(
        @Query("plan_id") planId: Int
    ): UnCollectPlanResponse

    //获取用户收藏的行程列表
    @GET("api/v1/front/user/plans")
    suspend fun getFavoritePlans(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20
    ): PlansApiResponse

    //初始化对话接口
    @GET("api/v1/front/chat/index")
    suspend fun initChat(): InitChatResponse

    //获取对话历史
    @GET("api/v1/front/chat/detail")
    suspend fun getChatHistory(
        @Query("context_id") conversationId: String
    ): ChatHistoryResponse
}
