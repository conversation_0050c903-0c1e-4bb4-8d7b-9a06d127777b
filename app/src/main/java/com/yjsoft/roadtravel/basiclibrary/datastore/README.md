# DataStore 数据持久化框架

## 概述

本框架基于 Jetpack DataStore 构建，提供类型安全、易用的数据持久化解决方案。支持多种数据类型，集成 Compose，提供完整的错误处理和日志记录。

## 主要特性

- ✅ **类型安全**：基于 PreferenceKey 的类型安全数据访问
- ✅ **Compose 集成**：提供专门的 Composable 函数
- ✅ **预定义键**：常用配置项的预定义键
- ✅ **批量操作**：支持事务性的批量数据操作
- ✅ **错误处理**：完善的异常处理和降级策略
- ✅ **日志记录**：集成项目日志框架
- ✅ **高级功能**：Repository 模式的业务逻辑封装

## 快速开始

### 1. 初始化

在 Application 中初始化 DataStore：

```kotlin
class RoadTravelApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 初始化 DataStore
        DataStoreManager.init(this)
    }
}
```

### 2. 基本使用

#### 使用预定义键

```kotlin
// 在 Composable 中使用
@Composable
fun UserProfile() {
    val (userName, setUserName) = rememberDataStoreValue(CommonPreferenceKeys.USER_NAME)
    val (isLoggedIn, logout) = rememberLoginState()
    
    Column {
        Text("用户名: $userName")
        Text("登录状态: ${if (isLoggedIn) "已登录" else "未登录"}")
        
        Button(onClick = { 
            scope.launch { setUserName("新用户名") }
        }) {
            Text("更新用户名")
        }
    }
}
```

#### 在非 Compose 环境中使用

```kotlin
class UserRepository {
    private val dataStoreRepository = DataStoreRepository.getInstance()
    
    suspend fun saveUserInfo(name: String, email: String) {
        dataStoreRepository.setUserInfo("user123", name, email)
    }
    
    suspend fun getUserName(): String {
        return dataStoreRepository.getUserName()
    }
}
```

### 3. 自定义键

```kotlin
// 定义自定义键
val CUSTOM_SETTING = PreferenceKey.string(
    name = "custom_setting",
    defaultValue = "default_value",
    description = "自定义设置"
)

// 使用自定义键
@Composable
fun CustomSettings() {
    val (setting, setSetting) = rememberDataStoreValue(CUSTOM_SETTING)
    
    // UI 代码...
}
```

## 核心组件

### DataStoreManager

核心管理器，提供底层数据访问功能：

```kotlin
// 存储数据
DataStoreManager.setValue(key, value)

// 获取数据
val value = DataStoreManager.getValue(key)

// 获取数据流
val flow = DataStoreManager.getValueFlow(key)

// 批量操作
DataStoreManager.batchOperation { preferences ->
    preferences[key1] = value1
    preferences[key2] = value2
}
```

### DataStoreRepository

业务逻辑封装层，提供高级功能：

```kotlin
val repository = DataStoreRepository.getInstance()

// 用户管理
repository.setUserInfo("id", "name", "email")
repository.logout()

// 设置管理
repository.setThemeMode("dark")
repository.setLastKnownCity("北京市")

// 收藏管理
repository.addFavoriteCity("上海市")
repository.removeFavoriteCity("广州市")
```

### PreferenceKey

类型安全的键定义：

```kotlin
// 字符串类型
val STRING_KEY = PreferenceKey.string("key_name", "default_value")

// 整数类型
val INT_KEY = PreferenceKey.int("key_name", 0)

// 布尔类型
val BOOLEAN_KEY = PreferenceKey.boolean("key_name", false)

// 字符串集合
val STRING_SET_KEY = PreferenceKey.stringSet("key_name", emptySet())
```

## Compose 集成

### 基本函数

```kotlin
// 获取值和更新函数
val (value, setValue) = rememberDataStoreValue(key)

// 只读值
val value = rememberDataStoreValueAsState(key)

// 带默认值初始化
val (value, setValue) = rememberDataStoreValueWithDefault(key, initialValue)
```

### 专用函数

```kotlin
// 登录状态管理
val (isLoggedIn, logout) = rememberLoginState()

// 主题管理
val (themeMode, setThemeMode) = rememberThemeMode()

// 城市管理
val (city, setCity) = rememberLastKnownCity()

// 收藏管理
val (favorites, addFavorite, removeFavorite) = rememberFavoriteCities()
```

### Effect 函数

```kotlin
// 执行 DataStore 操作
DataStoreEffect(key = someValue) {
    repository.updateSomething()
}

// 批量操作
BatchDataStoreEffect(key = someValue) {
    listOf(
        { repository.operation1() },
        { repository.operation2() }
    )
}

// 条件操作
ConditionalDataStoreEffect(condition = shouldUpdate) {
    repository.updateIfNeeded()
}
```

## 预定义键

框架提供了常用的预定义键：

### 用户相关
- `USER_ID` - 用户ID
- `USER_NAME` - 用户名
- `USER_EMAIL` - 用户邮箱
- `IS_LOGGED_IN` - 登录状态

### 应用设置
- `IS_FIRST_LAUNCH` - 首次启动
- `APP_VERSION` - 应用版本
- `THEME_MODE` - 主题模式
- `LANGUAGE` - 语言设置

### 定位相关
- `LAST_KNOWN_CITY` - 最后已知城市
- `LOCATION_PERMISSION_GRANTED` - 定位权限状态
- `AUTO_LOCATION_ENABLED` - 自动定位开关

### 用户偏好
- `FAVORITE_CITIES` - 收藏城市
- `NOTIFICATION_ENABLED` - 通知开关
- `AUTO_BACKUP_ENABLED` - 自动备份开关

## 配置选项

通过 `DataStoreConfig` 可以配置各种参数：

```kotlin
// 基本配置
DataStoreConfig.DEFAULT_DATASTORE_NAME
DataStoreConfig.Migration.ENABLED

// 性能配置
DataStoreConfig.Performance.ENABLE_MEMORY_CACHE
DataStoreConfig.Performance.CACHE_EXPIRY_MS

// 错误处理
DataStoreConfig.ErrorHandling.MAX_RETRY_COUNT
DataStoreConfig.ErrorHandling.USE_DEFAULT_ON_ERROR
```

## 最佳实践

### 1. 键的命名
- 使用描述性的名称
- 遵循 snake_case 命名规范
- 添加有意义的描述

### 2. 错误处理
- 总是为异步操作添加 try-catch
- 使用框架提供的默认值机制
- 记录重要的错误信息

### 3. 性能优化
- 使用 Flow 进行响应式编程
- 避免在主线程进行同步操作
- 合理使用批量操作

### 4. 数据验证
- 在存储前验证数据有效性
- 使用类型安全的键定义
- 设置合理的默认值

## 示例代码

完整的使用示例请参考：
- `DataStoreExamples.kt` - 各种使用场景的示例
- `MainActivity.kt` - 实际项目中的集成示例

## 文件结构

```
datastore/
├── core/
│   ├── DataStoreManager.kt      # 核心管理器
│   └── DataStoreRepository.kt   # 业务逻辑层
├── model/
│   └── PreferenceKey.kt         # 键定义和预定义键
├── config/
│   └── DataStoreConfig.kt       # 配置管理
├── compose/
│   └── DataStoreCompose.kt      # Compose 集成
├── examples/
│   └── DataStoreExamples.kt     # 使用示例
└── README.md                    # 文档说明
```

## 注意事项

1. **初始化**：必须在使用前调用 `DataStoreManager.init()`
2. **线程安全**：所有操作都是线程安全的
3. **异步操作**：写操作是异步的，需要在协程中调用
4. **数据类型**：只支持 DataStore 原生支持的数据类型
5. **迁移**：从 SharedPreferences 迁移需要额外配置
