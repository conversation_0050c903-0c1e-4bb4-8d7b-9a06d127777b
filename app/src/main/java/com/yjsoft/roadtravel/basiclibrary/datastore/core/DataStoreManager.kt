package com.yjsoft.roadtravel.basiclibrary.datastore.core

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.MutablePreferences
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.preferencesDataStore
import com.yjsoft.roadtravel.basiclibrary.datastore.config.DataStoreConfig
import com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.io.IOException

/**
 * DataStore管理器
 * 统一管理应用的数据持久化，基于Jetpack DataStore
 */
object DataStoreManager {
    
    private const val TAG = "DataStoreManager"
    
    // 使用委托属性创建DataStore实例
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
        name = DataStoreConfig.DEFAULT_DATASTORE_NAME
    )
    
    private var isInitialized = false
    private lateinit var applicationContext: Context
    
    /**
     * 初始化DataStore管理器
     * @param context 应用上下文
     */
    fun init(context: Context) {
        if (isInitialized) {
            LogManager.w(TAG, "DataStoreManager已经初始化，跳过重复初始化")
            return
        }
        
        try {
            applicationContext = context.applicationContext
            isInitialized = true
            LogManager.d("$TAG DataStore管理器初始化成功")
        } catch (e: Exception) {
            LogManager.e("$TAG DataStore管理器初始化失败", e)
            throw e
        }
    }
    
    /**
     * 获取DataStore实例
     */
    private fun getDataStore(): DataStore<Preferences> {
        checkInitialized()
        return applicationContext.dataStore
    }
    
    /**
     * 检查是否已初始化
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("DataStoreManager未初始化，请先调用init()方法")
        }
    }
    
    /**
     * 存储数据
     * @param key 数据键
     * @param value 数据值
     */
    suspend fun <T> setValue(key: PreferenceKey<T>, value: T) {
        try {
            getDataStore().edit { preferences ->
                preferences[key.key] = value
            }
            LogManager.d("$TAG 数据存储成功: ${key.name} = $value")
        } catch (e: Exception) {
            LogManager.e("$TAG 数据存储失败: ${key.name}", e)
            throw e
        }
    }
    
    /**
     * 获取数据流
     * @param key 数据键
     * @return 数据流
     */
    fun <T> getValueFlow(key: PreferenceKey<T>): Flow<T> {
        return getDataStore().data
            .catch { exception ->
                if (exception is IOException) {
                    LogManager.w(TAG, "读取数据时发生IO异常，返回默认值: ${key.name}", exception)
                    emit(emptyPreferences())
                } else {
                    LogManager.e(TAG, "读取数据时发生未知异常: ${key.name}", exception)
                    throw exception
                }
            }
            .map { preferences ->
                preferences[key.key] ?: key.defaultValue
            }
    }
    
    /**
     * 获取数据值（挂起函数）
     * @param key 数据键
     * @return 数据值
     */
    suspend fun <T> getValue(key: PreferenceKey<T>): T {
        return try {
            getValueFlow(key).first()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取数据失败: ${key.name}", e)
            key.defaultValue
        }
    }
    
    /**
     * 删除数据
     * @param key 数据键
     */
    suspend fun <T> removeValue(key: PreferenceKey<T>) {
        try {
            getDataStore().edit { preferences ->
                preferences.remove(key.key)
            }
            LogManager.d("$TAG 数据删除成功: ${key.name}")
        } catch (e: Exception) {
            LogManager.e("$TAG 数据删除失败: ${key.name}", e)
            throw e
        }
    }
    
    /**
     * 清除所有数据
     */
    suspend fun clearAll() {
        try {
            getDataStore().edit { preferences ->
                preferences.clear()
            }
            LogManager.d("$TAG 所有数据清除成功")
        } catch (e: Exception) {
            LogManager.e("$TAG 清除所有数据失败", e)
            throw e
        }
    }
    
    /**
     * 检查数据是否存在
     * @param key 数据键
     * @return 是否存在
     */
    suspend fun <T> containsKey(key: PreferenceKey<T>): Boolean {
        return try {
            getDataStore().data.first().contains(key.key)
        } catch (e: Exception) {
            LogManager.e("$TAG 检查数据是否存在失败: ${key.name}", e)
            false
        }
    }
    
    /**
     * 获取所有数据的键值对
     * @return 所有数据的Map
     */
    suspend fun getAllData(): Map<String, Any?> {
        return try {
            val preferences = getDataStore().data.first()
            preferences.asMap().mapKeys { it.key.name }
        } catch (e: Exception) {
            LogManager.e("$TAG 获取所有数据失败", e)
            emptyMap()
        }
    }
    
    /**
     * 批量操作
     * @param operations 操作列表
     */
    suspend fun batchOperation(operations: suspend (MutablePreferences) -> Unit) {
        try {
            getDataStore().edit { preferences ->
                operations(preferences)
            }
            LogManager.d("$TAG 批量操作执行成功")
        } catch (e: Exception) {
            LogManager.e("$TAG 批量操作执行失败", e)
            throw e
        }
    }
    
    /**
     * 获取DataStore大小（键值对数量）
     */
    suspend fun getSize(): Int {
        return try {
            getDataStore().data.first().asMap().size
        } catch (e: Exception) {
            LogManager.e("$TAG 获取DataStore大小失败", e)
            0
        }
    }
}
