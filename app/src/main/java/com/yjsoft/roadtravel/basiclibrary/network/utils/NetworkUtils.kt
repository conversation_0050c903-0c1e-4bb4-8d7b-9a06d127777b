package com.yjsoft.roadtravel.basiclibrary.network.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import java.util.concurrent.ConcurrentHashMap

/**
 * 网络工具类
 * 提供网络状态检查、请求去重等实用功能
 */
object NetworkUtils {
    
    // 请求去重缓存
    private val requestCache = ConcurrentHashMap<String, Any>()
    
    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 获取网络类型
     */
    fun getNetworkType(context: Context): NetworkType {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE
            
            return when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.MOBILE
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                else -> NetworkType.OTHER
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo ?: return NetworkType.NONE
            
            return when (networkInfo.type) {
                ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                ConnectivityManager.TYPE_MOBILE -> NetworkType.MOBILE
                ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                else -> NetworkType.OTHER
            }
        }
    }
    
    /**
     * 监听网络状态变化
     */
    fun observeNetworkState(context: Context): Flow<NetworkState> = callbackFlow {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                trySend(NetworkState(true, getNetworkType(context)))
            }
            
            override fun onLost(network: Network) {
                trySend(NetworkState(false, NetworkType.NONE))
            }
            
            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                val isConnected = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                        networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                trySend(NetworkState(isConnected, getNetworkType(context)))
            }
        }
        
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, callback)
        
        // 发送当前状态
        trySend(NetworkState(isNetworkAvailable(context), getNetworkType(context)))
        
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.distinctUntilChanged()
    
    /**
     * 请求去重
     * @param key 请求的唯一标识
     * @param request 请求执行函数
     * @return 请求结果
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun <T> deduplicateRequest(key: String, request: suspend () -> T): T {
        // 检查是否已有相同请求在执行
        val existingRequest = requestCache[key]
        if (existingRequest != null) {
            return existingRequest as T
        }
        
        return try {
            val result = request()
            requestCache[key] = result as Any
            result
        } finally {
            // 请求完成后移除缓存
            requestCache.remove(key)
        }
    }
    
    /**
     * 清除请求缓存
     */
    fun clearRequestCache() {
        requestCache.clear()
    }
    
    /**
     * 生成请求缓存键
     */
    fun generateCacheKey(url: String, params: Map<String, Any>? = null): String {
        val paramsString = params?.entries?.sortedBy { it.key }
            ?.joinToString("&") { "${it.key}=${it.value}" } ?: ""
        return "$url?$paramsString".hashCode().toString()
    }
    
    /**
     * 判断是否为移动网络
     */
    fun isMobileNetwork(context: Context): Boolean {
        return getNetworkType(context) == NetworkType.MOBILE
    }
    
    /**
     * 判断是否为WiFi网络
     */
    fun isWifiNetwork(context: Context): Boolean {
        return getNetworkType(context) == NetworkType.WIFI
    }
    
    /**
     * 获取网络信息摘要
     */
    fun getNetworkInfo(context: Context): NetworkInfo {
        val isAvailable = isNetworkAvailable(context)
        val type = getNetworkType(context)
        
        return NetworkInfo(
            isAvailable = isAvailable,
            type = type,
            typeName = type.displayName,
            isMetered = isMeteredNetwork(context)
        )
    }
    
    /**
     * 判断是否为计费网络
     */
    private fun isMeteredNetwork(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return connectivityManager.isActiveNetworkMetered
    }
}

/**
 * 网络类型枚举
 */
enum class NetworkType(val displayName: String) {
    NONE("无网络"),
    WIFI("WiFi"),
    MOBILE("移动网络"),
    ETHERNET("以太网"),
    OTHER("其他")
}

/**
 * 网络状态数据类
 */
data class NetworkState(
    val isConnected: Boolean,
    val type: NetworkType
) {
    val isWifi: Boolean get() = type == NetworkType.WIFI
    val isMobile: Boolean get() = type == NetworkType.MOBILE
    val isEthernet: Boolean get() = type == NetworkType.ETHERNET
}

/**
 * 网络信息数据类
 */
data class NetworkInfo(
    val isAvailable: Boolean,
    val type: NetworkType,
    val typeName: String,
    val isMetered: Boolean
) {
    val isWifi: Boolean get() = type == NetworkType.WIFI
    val isMobile: Boolean get() = type == NetworkType.MOBILE
    val isEthernet: Boolean get() = type == NetworkType.ETHERNET
}

/**
 * 网络状态监听器
 */
interface NetworkStateListener {
    fun onNetworkAvailable(networkType: NetworkType)
    fun onNetworkLost()
    fun onNetworkChanged(networkState: NetworkState)
}

/**
 * 网络状态管理器
 */
class NetworkStateManager(private val context: Context) {
    
    private val listeners = mutableSetOf<NetworkStateListener>()
    private var currentState: NetworkState? = null
    
    /**
     * 添加网络状态监听器
     */
    fun addListener(listener: NetworkStateListener) {
        listeners.add(listener)
        
        // 如果已有当前状态，立即通知新监听器
        currentState?.let { state ->
            if (state.isConnected) {
                listener.onNetworkAvailable(state.type)
            } else {
                listener.onNetworkLost()
            }
        }
    }
    
    /**
     * 移除网络状态监听器
     */
    fun removeListener(listener: NetworkStateListener) {
        listeners.remove(listener)
    }
    
    /**
     * 开始监听网络状态
     */
    suspend fun startListening() {
        NetworkUtils.observeNetworkState(context).collect { state ->
            val previousState = currentState
            currentState = state
            
            // 通知所有监听器
            listeners.forEach { listener ->
                try {
                    when {
                        state.isConnected && (previousState?.isConnected != true) -> {
                            listener.onNetworkAvailable(state.type)
                        }
                        !state.isConnected && (previousState?.isConnected == true) -> {
                            listener.onNetworkLost()
                        }
                        state != previousState -> {
                            listener.onNetworkChanged(state)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    
    /**
     * 获取当前网络状态
     */
    fun getCurrentState(): NetworkState? = currentState
    
    /**
     * 清理资源
     */
    fun cleanup() {
        listeners.clear()
        currentState = null
    }
}
