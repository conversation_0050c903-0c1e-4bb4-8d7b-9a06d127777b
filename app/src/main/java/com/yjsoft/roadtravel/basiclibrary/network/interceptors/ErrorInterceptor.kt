package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.yjsoft.roadtravel.basiclibrary.network.models.ApiError
import com.yjsoft.roadtravel.basiclibrary.network.models.ApiResponse
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 错误处理拦截器
 * 统一处理网络错误、HTTP状态码错误和业务逻辑错误
 */
class ErrorInterceptor(
    private val gson: Gson = Gson(),
    private val errorHandler: ErrorHandler? = null,
    private val loginStatusHandler: LoginStatusHandler? = null
) : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        
        return try {
            val response = chain.proceed(request)
            handleResponse(response)
        } catch (e: Exception) {
            handleNetworkException(e)
            throw e
        }
    }
    
    /**
     * 处理响应
     */
    private fun handleResponse(response: Response): Response {
        return when {
            response.isSuccessful -> {
                // HTTP状态码成功，检查业务逻辑错误
                handleBusinessError(response)
            }
            else -> {
                // HTTP状态码错误
                handleHttpError(response)
            }
        }
    }
    
    /**
     * 处理业务逻辑错误
     */
    private fun handleBusinessError(response: Response): Response {
        val responseBody = response.body
        try {
            val bodyString = responseBody.string()
            val apiResponse = gson.fromJson(bodyString, ApiResponse::class.java)

            if (apiResponse != null && apiResponse.isFailure()) {
                // 检查是否为需要登录的业务码
                val isLoginRequired = loginStatusHandler?.handleLoginStatus(apiResponse.code, apiResponse.message) ?: false

                if (!isLoginRequired) {
                    // 普通业务逻辑错误
                    val apiError = ApiError.businessError(apiResponse.code, apiResponse.message)
                    errorHandler?.onBusinessError(apiError)
                }
            }

            // 重新创建ResponseBody，因为已经读取过了
            val newResponseBody = bodyString.toResponseBody(responseBody.contentType())
            return response.newBuilder()
                .body(newResponseBody)
                .build()

        } catch (e: JsonSyntaxException) {
            // JSON解析错误
            val apiError = ApiError.parseError("响应数据格式错误")
            errorHandler?.onParseError(apiError)
        } catch (e: Exception) {
            // 其他错误
            val apiError = ApiError.unknownError("处理响应时发生错误: ${e.message}")
            errorHandler?.onUnknownError(apiError)
        }
        
        return response
    }
    
    /**
     * 处理HTTP错误
     */
    private fun handleHttpError(response: Response): Response {
        val apiError = when (response.code) {
            400 -> ApiError.businessError(400, "请求参数错误")
            401 -> ApiError.businessError(401, "未授权访问")
            403 -> ApiError.businessError(403, "禁止访问")
            404 -> ApiError.businessError(404, "请求的资源不存在")
            405 -> ApiError.businessError(405, "请求方法不允许")
            408 -> ApiError.timeoutError("请求超时")
            429 -> ApiError.businessError(429, "请求过于频繁")
            500 -> ApiError.businessError(500, "服务器内部错误")
            502 -> ApiError.businessError(502, "网关错误")
            503 -> ApiError.businessError(503, "服务不可用")
            504 -> ApiError.timeoutError("网关超时")
            else -> ApiError.businessError(response.code, "HTTP错误: ${response.code}")
        }
        
        errorHandler?.onHttpError(apiError, response.code)
        return response
    }
    
    /**
     * 处理网络异常
     */
    private fun handleNetworkException(exception: Exception) {
        val apiError = when (exception) {
            is UnknownHostException -> {
                ApiError.networkError("网络连接失败，请检查网络设置")
            }
            is ConnectException -> {
                ApiError.networkError("无法连接到服务器")
            }
            is SocketTimeoutException -> {
                ApiError.timeoutError("网络请求超时")
            }
            is IOException -> {
                ApiError.networkError("网络IO错误: ${exception.message}")
            }
            else -> {
                ApiError.unknownError("网络请求异常: ${exception.message}")
            }
        }
        
        errorHandler?.onNetworkError(apiError)
    }
}

/**
 * 错误处理器接口
 */
interface ErrorHandler {
    /**
     * 网络错误处理
     */
    fun onNetworkError(error: ApiError) {}
    
    /**
     * HTTP错误处理
     */
    fun onHttpError(error: ApiError, httpCode: Int) {}
    
    /**
     * 业务逻辑错误处理
     */
    fun onBusinessError(error: ApiError) {}
    
    /**
     * 解析错误处理
     */
    fun onParseError(error: ApiError) {}
    
    /**
     * 未知错误处理
     */
    fun onUnknownError(error: ApiError) {}
}

/**
 * 默认错误处理器实现
 */
class DefaultErrorHandler : ErrorHandler {
    
    override fun onNetworkError(error: ApiError) {
        // 可以在这里添加网络错误的通用处理逻辑
        // 比如显示Toast、记录日志等
        logError("网络错误", error)
    }
    
    override fun onHttpError(error: ApiError, httpCode: Int) {
        // HTTP错误处理
        logError("HTTP错误($httpCode)", error)
    }
    
    override fun onBusinessError(error: ApiError) {
        // 业务逻辑错误处理
        logError("业务错误", error)
    }
    
    override fun onParseError(error: ApiError) {
        // 解析错误处理
        logError("解析错误", error)
    }
    
    override fun onUnknownError(error: ApiError) {
        // 未知错误处理
        logError("未知错误", error)
    }
    
    private fun logError(type: String, error: ApiError) {
        println("[$type] Code: ${error.code}, Message: ${error.message}")
        error.details?.let { println("Details: $it") }
    }
}
