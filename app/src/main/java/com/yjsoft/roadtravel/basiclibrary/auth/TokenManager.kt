package com.yjsoft.roadtravel.basiclibrary.auth

import android.content.Context
import androidx.datastore.preferences.core.edit
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * Token管理器
 * 
 * 统一管理应用中的认证Token，提供同步和异步访问接口
 * 使用DataStore作为底层存储，通过缓存机制优化性能
 * 
 * 功能：
 * - Token的读取、保存、清除
 * - Token过期检查
 * - 缓存机制减少DataStore访问
 * - 线程安全的同步访问接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object TokenManager {
    
    private const val TAG = "TokenManager"
    
    // Token缓存，减少DataStore访问频率
    private val tokenCache = ConcurrentHashMap<String, String>()
    private val tokenTimeCache = ConcurrentHashMap<String, Long>()
    
    // 缓存有效时间（5分钟）
    private const val CACHE_VALIDITY_DURATION = 5 * 60 * 1000L
    private var lastCacheUpdateTime = 0L
    
    // 是否已初始化
    private var isInitialized = false
    
    /**
     * 初始化TokenManager
     */
    fun initialize(context: Context) {
        if (!isInitialized) {
            try {
                // 初始化DataStore
                DataStoreManager.init(context)

                isInitialized = true
                LogManager.d(TAG, "TokenManager核心初始化成功")

                // 异步预加载token到缓存（避免阻塞主线程）
                asyncRefreshCache()

            } catch (e: Exception) {
                LogManager.e(TAG, "TokenManager初始化失败", e)
                throw e
            }
        }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 获取访问Token（同步方法）
     *
     * @return 访问Token，如果不存在则返回空字符串
     */
    fun getAccessToken(): String {
        checkInitialized()

        return try {
            // 检查缓存是否有效
            if (isCacheValid()) {
                tokenCache[KEY_ACCESS_TOKEN] ?: ""
            } else {
                // 缓存失效，优先尝试异步刷新，如果缓存为空则同步加载
                if (tokenCache.isEmpty()) {
                    LogManager.d(TAG, "缓存为空，执行同步加载")
                    refreshCache()
                } else {
                    LogManager.d(TAG, "缓存失效，触发异步刷新")
                    asyncRefreshCache()
                }
                tokenCache[KEY_ACCESS_TOKEN] ?: ""
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取访问Token失败", e)
            ""
        }
    }
    
    /**
     * 获取刷新Token（同步方法）
     *
     * @return 刷新Token，如果不存在则返回空字符串
     */
    fun getRefreshToken(): String {
        checkInitialized()

        return try {
            if (isCacheValid()) {
                tokenCache[KEY_REFRESH_TOKEN] ?: ""
            } else {
                // 缓存失效，优先尝试异步刷新，如果缓存为空则同步加载
                if (tokenCache.isEmpty()) {
                    LogManager.d(TAG, "缓存为空，执行同步加载")
                    refreshCache()
                } else {
                    LogManager.d(TAG, "缓存失效，触发异步刷新")
                    asyncRefreshCache()
                }
                tokenCache[KEY_REFRESH_TOKEN] ?: ""
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取刷新Token失败", e)
            ""
        }
    }
    
    /**
     * 检查Token是否过期
     * 
     * @return true表示已过期，false表示未过期
     */
    fun isTokenExpired(): Boolean {
        checkInitialized()
        
                 return try {
             val expiresTime = if (isCacheValid()) {
                 tokenTimeCache[KEY_EXPIRES_TIME] ?: 0L
             } else {
                 refreshCache()
                 tokenTimeCache[KEY_EXPIRES_TIME] ?: 0L
             }
             
             val currentTime = System.currentTimeMillis()
             currentTime >= expiresTime
         } catch (e: Exception) {
             LogManager.e(TAG, "检查Token过期状态失败", e)
             true // 出错时认为已过期，促使重新登录
         }
    }
    
    /**
     * 保存Token信息（异步方法）
     * 
     * @param accessToken 访问Token
     * @param refreshToken 刷新Token
     * @param expiresIn Token有效期（秒）
     */
    suspend fun saveTokens(accessToken: String, refreshToken: String?, expiresIn: Int) {
        checkInitialized()
        
        try {
            val currentTime = System.currentTimeMillis()
            val expiresTime = currentTime + (expiresIn * 1000L)
            
            // 保存到DataStore
            DataStoreManager.batchOperation { preferences ->
                preferences[CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key] = accessToken
                preferences[CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key] = refreshToken ?: ""
                preferences[CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key] = expiresTime
                preferences[CommonPreferenceKeys.WECHAT_LOGIN_TIME.key] = currentTime
            }
            
            // 更新缓存
            updateCache(accessToken, refreshToken ?: "", expiresTime, currentTime)
            
            LogManager.d(TAG, "Token保存成功")
        } catch (e: Exception) {
            LogManager.e(TAG, "保存Token失败", e)
            throw e
        }
    }
    
    /**
     * 更新访问Token（异步方法）
     * 
     * @param accessToken 新的访问Token
     * @param refreshToken 新的刷新Token（可选）
     * @param expiresIn 有效期（秒）
     */
    suspend fun updateTokens(accessToken: String, refreshToken: String?, expiresIn: Int) {
        checkInitialized()
        
        try {
            val currentTime = System.currentTimeMillis()
            val expiresTime = currentTime + (expiresIn * 1000L)
            
            DataStoreManager.batchOperation { preferences ->
                preferences[CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key] = accessToken
                if (refreshToken != null) {
                    preferences[CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key] = refreshToken
                }
                preferences[CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key] = expiresTime
            }
            
            // 更新缓存
            updateCache(accessToken, refreshToken ?: getRefreshToken(), expiresTime, currentTime)
            
            LogManager.d(TAG, "Token更新成功")
        } catch (e: Exception) {
            LogManager.e(TAG, "更新Token失败", e)
            throw e
        }
    }
    
    /**
     * 清除所有Token信息（异步方法）
     */
    suspend fun clearTokens() {
        checkInitialized()
        
        try {
            DataStoreManager.batchOperation { preferences ->
                preferences.remove(CommonPreferenceKeys.WECHAT_ACCESS_TOKEN.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_REFRESH_TOKEN.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME.key)
                preferences.remove(CommonPreferenceKeys.WECHAT_LOGIN_TIME.key)
            }
            
            // 清除缓存
            clearCache()
            
            LogManager.d(TAG, "Token清除成功")
        } catch (e: Exception) {
            LogManager.e(TAG, "清除Token失败", e)
            throw e
        }
    }
    
    /**
     * 获取Token信息摘要（用于调试）
     */
    fun getTokenSummary(): String {
        checkInitialized()
        
        return try {
            val accessToken = getAccessToken()
            val refreshToken = getRefreshToken()
            val isExpired = isTokenExpired()
            
            "AccessToken: ${if (accessToken.isNotEmpty()) "已设置(${accessToken.take(10)}...)" else "未设置"}, " +
            "RefreshToken: ${if (refreshToken.isNotEmpty()) "已设置" else "未设置"}, " +
            "过期状态: ${if (isExpired) "已过期" else "有效"}"
        } catch (e: Exception) {
            "获取Token信息失败: ${e.message}"
        }
    }
    
    // ================================ 私有方法 ================================
    
    /**
     * 检查是否已初始化
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("TokenManager未初始化，请先调用initialize()方法")
        }
    }
    
    /**
     * 异步刷新缓存（从DataStore加载数据）
     * 避免阻塞主线程
     */
    private fun asyncRefreshCache() {
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            try {
                val accessToken = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_ACCESS_TOKEN)
                val refreshToken = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_REFRESH_TOKEN)
                val expiresTime = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME)
                val loginTime = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_LOGIN_TIME)

                updateCache(accessToken, refreshToken, expiresTime, loginTime)
                LogManager.d(TAG, "异步缓存刷新完成")
            } catch (e: Exception) {
                LogManager.e(TAG, "异步刷新缓存失败", e)
            }
        }
    }

    /**
     * 同步刷新缓存（从DataStore加载数据）
     * 仅在必要时使用，避免阻塞主线程
     */
    private fun refreshCache() {
        try {
            runBlocking {
                val accessToken = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_ACCESS_TOKEN)
                val refreshToken = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_REFRESH_TOKEN)
                val expiresTime = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_TOKEN_EXPIRES_TIME)
                val loginTime = DataStoreManager.getValue(CommonPreferenceKeys.WECHAT_LOGIN_TIME)

                updateCache(accessToken, refreshToken, expiresTime, loginTime)
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "刷新缓存失败", e)
        }
    }
    
    /**
     * 更新缓存
     */
    private fun updateCache(accessToken: String, refreshToken: String, expiresTime: Long, loginTime: Long) {
        tokenCache[KEY_ACCESS_TOKEN] = accessToken
        tokenCache[KEY_REFRESH_TOKEN] = refreshToken
        tokenTimeCache[KEY_EXPIRES_TIME] = expiresTime
        tokenTimeCache[KEY_LOGIN_TIME] = loginTime
        lastCacheUpdateTime = System.currentTimeMillis()
    }
    
    /**
     * 清除缓存
     */
    private fun clearCache() {
        tokenCache.clear()
        tokenTimeCache.clear()
        lastCacheUpdateTime = 0L
    }
    
    /**
     * 检查缓存是否有效
     */
    private fun isCacheValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        return (currentTime - lastCacheUpdateTime) < CACHE_VALIDITY_DURATION
    }
    
    // 常量定义
    private const val KEY_ACCESS_TOKEN = "access_token"
    private const val KEY_REFRESH_TOKEN = "refresh_token"
    private const val KEY_EXPIRES_TIME = "expires_time"
    private const val KEY_LOGIN_TIME = "login_time"
} 