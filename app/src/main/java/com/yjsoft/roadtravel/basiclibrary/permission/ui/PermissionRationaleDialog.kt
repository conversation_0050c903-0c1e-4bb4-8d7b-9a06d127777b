package com.yjsoft.roadtravel.basiclibrary.permission.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionMessages
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger

/**
 * 权限说明对话框
 * 用于向用户解释为什么需要某个权限
 */
@Composable
fun PermissionRationaleDialog(
    permissions: List<String>,
    title: String = "权限申请",
    message: String? = null,
    icon: ImageVector = Icons.Default.Info,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    // 记录对话框显示
    LaunchedEffect(permissions) {
        PermissionLogger.logRationaleDialogShown(permissions)
    }
    
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 图标
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 标题
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 消息内容
                val displayMessage = message ?: generatePermissionMessage(permissions)
                Text(
                    text = displayMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = {
                            PermissionLogger.logRationaleDialogResult(permissions, false)
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }
                    
                    // 确认按钮
                    Button(
                        onClick = {
                            PermissionLogger.logRationaleDialogResult(permissions, true)
                            onConfirm()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("确定")
                    }
                }
            }
        }
    }
}

/**
 * 单个权限说明对话框
 */
@Composable
fun SinglePermissionRationaleDialog(
    permission: String,
    title: String? = null,
    message: String? = null,
    icon: ImageVector = Icons.Default.Info,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    val permissionMessage = PermissionMessages.getMessage(permission)
    
    PermissionRationaleDialog(
        permissions = listOf(permission),
        title = title ?: permissionMessage.title,
        message = message ?: permissionMessage.rationale,
        icon = icon,
        onConfirm = onConfirm,
        onDismiss = onDismiss,
        onDismissRequest = onDismissRequest
    )
}

/**
 * 权限组说明对话框
 */
@Composable
fun PermissionGroupRationaleDialog(
    groupName: String,
    permissions: List<String>,
    title: String? = null,
    message: String? = null,
    icon: ImageVector = Icons.Default.Info,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    val groupMessage = PermissionMessages.getGroupMessage(groupName)
    
    PermissionRationaleDialog(
        permissions = permissions,
        title = title ?: groupMessage.title,
        message = message ?: groupMessage.rationale,
        icon = icon,
        onConfirm = onConfirm,
        onDismiss = onDismiss,
        onDismissRequest = onDismissRequest
    )
}

/**
 * 生成权限说明消息
 */
private fun generatePermissionMessage(permissions: List<String>): String {
    return when {
        permissions.size == 1 -> {
            val permission = permissions.first()
            PermissionMessages.getMessage(permission).rationale
        }
        permissions.size <= 3 -> {
            val messages = permissions.map { permission ->
                val message = PermissionMessages.getMessage(permission)
                "• ${message.title}: ${message.rationale}"
            }
            "应用需要以下权限：\n\n${messages.joinToString("\n\n")}"
        }
        else -> {
            "应用需要 ${permissions.size} 个权限来正常工作，这些权限将帮助应用为您提供更好的服务。"
        }
    }
}

/**
 * 权限说明对话框状态
 */
@Stable
class PermissionRationaleDialogState {
    private var _isVisible by mutableStateOf(false)
    private var _permissions by mutableStateOf<List<String>>(emptyList())
    private var _title by mutableStateOf("")
    private var _message by mutableStateOf("")
    private var _onConfirm by mutableStateOf<(() -> Unit)?>(null)
    private var _onDismiss by mutableStateOf<(() -> Unit)?>(null)
    
    val isVisible: Boolean get() = _isVisible
    val permissions: List<String> get() = _permissions
    val title: String get() = _title
    val message: String get() = _message
    
    /**
     * 显示对话框
     */
    fun show(
        permissions: List<String>,
        title: String = "权限申请",
        message: String? = null,
        onConfirm: () -> Unit,
        onDismiss: () -> Unit
    ) {
        _permissions = permissions
        _title = title
        _message = message ?: generatePermissionMessage(permissions)
        _onConfirm = onConfirm
        _onDismiss = onDismiss
        _isVisible = true
    }
    
    /**
     * 隐藏对话框
     */
    fun hide() {
        _isVisible = false
    }
    
    /**
     * 确认操作
     */
    fun confirm() {
        _onConfirm?.invoke()
        hide()
    }
    
    /**
     * 取消操作
     */
    fun dismiss() {
        _onDismiss?.invoke()
        hide()
    }
}

/**
 * 记住权限说明对话框状态
 */
@Composable
fun rememberPermissionRationaleDialogState(): PermissionRationaleDialogState {
    return remember { PermissionRationaleDialogState() }
}

/**
 * 权限说明对话框组件（使用状态管理）
 */
@Composable
fun PermissionRationaleDialogWithState(
    state: PermissionRationaleDialogState,
    icon: ImageVector = Icons.Default.Info
) {
    if (state.isVisible) {
        PermissionRationaleDialog(
            permissions = state.permissions,
            title = state.title,
            message = state.message,
            icon = icon,
            onConfirm = { state.confirm() },
            onDismiss = { state.dismiss() }
        )
    }
}
