package com.yjsoft.roadtravel.basiclibrary.payment.repository

import com.yjsoft.roadtravel.basiclibrary.network.models.ApiResponse
import retrofit2.http.*

/**
 * 支付API服务接口
 * 定义支付相关的网络请求接口
 */
interface PaymentApiService {
    
    /**
     * 创建支付订单
     * @param request 支付订单请求
     * @return 支付订单响应
     */
    @POST("payment/orders")
    suspend fun createPaymentOrder(@Body request: CreatePaymentOrderRequest): ApiResponse<PaymentOrderResponse>
    
    /**
     * 查询支付订单状态
     * @param orderId 订单ID
     * @return 订单状态响应
     */
    @GET("payment/orders/{orderId}")
    suspend fun getPaymentOrderStatus(@Path("orderId") orderId: String): ApiResponse<PaymentOrderStatusResponse>
    
    /**
     * 支付结果通知
     * @param request 支付结果通知请求
     * @return 通知响应
     */
    @POST("payment/notify")
    suspend fun notifyPaymentResult(@Body request: PaymentNotifyRequest): ApiResponse<PaymentNotifyResponse>
    
    /**
     * 申请退款
     * @param request 退款请求
     * @return 退款响应
     */
    @POST("payment/refund")
    suspend fun requestRefund(@Body request: RefundRequest): ApiResponse<RefundResponse>
    
    /**
     * 查询退款状态
     * @param refundId 退款ID
     * @return 退款状态响应
     */
    @GET("payment/refund/{refundId}")
    suspend fun getRefundStatus(@Path("refundId") refundId: String): ApiResponse<RefundStatusResponse>
}

/**
 * 创建支付订单请求
 */
data class CreatePaymentOrderRequest(
    val orderId: String,
    val amount: String,
    val title: String,
    val description: String? = null,
    val paymentType: String,
    val notifyUrl: String? = null,
    val returnUrl: String? = null,
    val extraParams: Map<String, String> = emptyMap()
)

/**
 * 支付订单响应
 */
data class PaymentOrderResponse(
    val orderId: String,
    val paymentParams: String,
    val expireTime: Long,
    val paymentType: String
)

/**
 * 支付订单状态响应
 */
data class PaymentOrderStatusResponse(
    val orderId: String,
    val status: String,
    val amount: String,
    val paymentType: String,
    val transactionId: String? = null,
    val payTime: Long? = null,
    val failReason: String? = null
)

/**
 * 支付结果通知请求
 */
data class PaymentNotifyRequest(
    val orderId: String,
    val transactionId: String,
    val amount: String,
    val paymentType: String,
    val status: String,
    val payTime: Long,
    val extraData: Map<String, String> = emptyMap()
)

/**
 * 支付结果通知响应
 */
data class PaymentNotifyResponse(
    val success: Boolean,
    val message: String
)

/**
 * 退款请求
 */
data class RefundRequest(
    val orderId: String,
    val refundId: String,
    val refundAmount: String,
    val refundReason: String,
    val notifyUrl: String? = null
)

/**
 * 退款响应
 */
data class RefundResponse(
    val refundId: String,
    val status: String,
    val refundAmount: String,
    val refundTime: Long? = null
)

/**
 * 退款状态响应
 */
data class RefundStatusResponse(
    val refundId: String,
    val orderId: String,
    val status: String,
    val refundAmount: String,
    val refundTime: Long? = null,
    val failReason: String? = null
)

/**
 * 支付订单状态枚举
 */
enum class PaymentOrderStatus {
    PENDING,    // 待支付
    PAID,       // 已支付
    CANCELLED,  // 已取消
    FAILED,     // 支付失败
    REFUNDED    // 已退款
}

/**
 * 退款状态枚举
 */
enum class RefundStatus {
    PENDING,    // 退款中
    SUCCESS,    // 退款成功
    FAILED      // 退款失败
}
