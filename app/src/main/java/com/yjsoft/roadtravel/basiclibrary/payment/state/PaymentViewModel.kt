package com.yjsoft.roadtravel.basiclibrary.payment.state

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentCallback
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.repository.PaymentRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 支付ViewModel
 * 管理支付相关的UI状态和业务逻辑
 */
class PaymentViewModel : ViewModel() {
    
    companion object {
        private const val TAG = "PaymentViewModel %s"
    }
    
    // 支付状态
    private val _paymentState = MutableStateFlow(PaymentState.idle())
    val paymentState: StateFlow<PaymentState> = _paymentState.asStateFlow()
    
    // 可用的支付方式
    private val _availablePaymentTypes = MutableStateFlow<List<PaymentType>>(emptyList())
    val availablePaymentTypes: StateFlow<List<PaymentType>> = _availablePaymentTypes.asStateFlow()
    
    // 支付结果
    private val _paymentResult = MutableStateFlow<PaymentResult?>(null)
    val paymentResult: StateFlow<PaymentResult?> = _paymentResult.asStateFlow()
    
    // 支付仓库
    private val paymentRepository = PaymentRepository.getInstance()
    
    /**
     * 初始化ViewModel
     */
    fun init(context: Context) {
        viewModelScope.launch {
            try {
                // 初始化支付仓库
                paymentRepository.init(context)
                
                // 加载可用的支付方式
                loadAvailablePaymentTypes(context)
                
                LogManager.d(TAG, "PaymentViewModel初始化完成")
            } catch (e: Exception) {
                LogManager.e(TAG, "PaymentViewModel初始化失败", e)
                updatePaymentState(
                    PaymentState.error(
                        PaymentResult.Error(
                            orderId = "",
                            paymentType = PaymentType.ALIPAY,
                            errorCode = "INIT_ERROR",
                            errorMessage = "支付模块初始化失败: ${e.message}",
                            cause = e
                        )
                    )
                )
            }
        }
    }
    
    /**
     * 加载可用的支付方式
     */
    private suspend fun loadAvailablePaymentTypes(context: Context) {
        try {
            val types = PaymentManager.getAvailablePaymentTypes(context)
            _availablePaymentTypes.value = types
            LogManager.d(TAG, "可用支付方式: ${types.map { it.name }}")
        } catch (e: Exception) {
            LogManager.e(TAG, "加载可用支付方式失败", e)
            _availablePaymentTypes.value = emptyList()
        }
    }
    
    /**
     * 开始支付
     */
    fun startPayment(
        context: Context,
        paymentType: PaymentType,
        request: PaymentRequest,
        callback: PaymentCallback? = null
    ) {
        viewModelScope.launch {
            try {
                LogManager.d(TAG, "开始支付 - 类型: ${paymentType.name}, 订单: ${request.orderId}")
                
                // 更新状态为准备中
                updatePaymentState(
                    PaymentState.loading(
                        paymentType = paymentType,
                        orderId = request.orderId,
                        message = "准备支付..."
                    )
                )
                
                // 通知支付开始
                callback?.onPaymentStart(request.orderId, paymentType)
                
                // 执行支付
                val result = PaymentManager.pay(context, paymentType, request)
                
                // 更新支付结果
                _paymentResult.value = result
                updatePaymentStateByResult(result)
                
                // 通知支付结果
                when (result) {
                    is PaymentResult.Success -> callback?.onPaymentSuccess(result)
                    is PaymentResult.Cancel -> callback?.onPaymentCancel(result)
                    is PaymentResult.Error -> callback?.onPaymentError(result)
                    is PaymentResult.Processing -> callback?.onPaymentProcessing(result)
                }
                
                // 通知服务器支付结果
                notifyPaymentResult(request.orderId, result)
                
            } catch (e: Exception) {
                LogManager.e(TAG, "支付过程异常", e)
                val errorResult = PaymentResult.Error(
                    orderId = request.orderId,
                    paymentType = paymentType,
                    errorCode = "PAYMENT_EXCEPTION",
                    errorMessage = e.message ?: "支付过程发生异常",
                    cause = e
                )
                
                _paymentResult.value = errorResult
                updatePaymentState(PaymentState.error(errorResult))
                callback?.onPaymentError(errorResult)
            }
        }
    }
    
    /**
     * 检查支付方式是否可用
     */
    fun checkPaymentTypeAvailable(context: Context, paymentType: PaymentType) {
        viewModelScope.launch {
            try {
                val isAvailable = PaymentManager.isPaymentTypeAvailable(context, paymentType)
                LogManager.d(TAG, "支付方式 ${paymentType.name} 可用性: $isAvailable")
            } catch (e: Exception) {
                LogManager.e(TAG, "检查支付方式可用性失败", e)
            }
        }
    }

    /**
     * 开始支付（lambda版本）
     */
    fun startPayment(
        context: Context,
        paymentType: PaymentType,
        request: PaymentRequest,
        onResult: (PaymentResult) -> Unit
    ) {
        val callback = object : PaymentCallback {
            override fun onPaymentSuccess(result: PaymentResult.Success) {
                onResult(result)
            }

            override fun onPaymentCancel(result: PaymentResult.Cancel) {
                onResult(result)
            }

            override fun onPaymentError(result: PaymentResult.Error) {
                onResult(result)
            }

            override fun onPaymentProcessing(result: PaymentResult.Processing) {
                onResult(result)
            }
        }

        startPayment(context, paymentType, request, callback)
    }

    /**
     * 重置支付状态
     */
    fun resetPaymentState() {
        _paymentState.value = PaymentState.idle()
        _paymentResult.value = null
        LogManager.d(TAG, "支付状态已重置")
    }
    
    /**
     * 重试支付
     */
    fun retryPayment(
        context: Context,
        callback: PaymentCallback? = null
    ) {
        val currentState = _paymentState.value
        if (currentState.canRetry && 
            currentState.paymentType != null && 
            currentState.orderId != null) {
            
            // 重新构建支付请求
            val request = PaymentRequest.simple(
                orderId = currentState.orderId,
                amount = currentState.amount?.toDoubleOrNull() ?: 0.0,
                title = "重试支付"
            )
            
            startPayment(context, currentState.paymentType, request, callback)
        } else {
            LogManager.w(TAG, "当前状态不支持重试支付")
        }
    }
    
    /**
     * 通知服务器支付结果
     */
    private suspend fun notifyPaymentResult(orderId: String, result: PaymentResult) {
        try {
            val notifyResult = paymentRepository.notifyPaymentResult(orderId, result)
            if (notifyResult.isSuccess) {
                LogManager.d(TAG, "支付结果通知成功: $orderId")
            } else {
                LogManager.w(TAG, "支付结果通知失败: ${notifyResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "通知支付结果异常", e)
        }
    }
    
    /**
     * 更新支付状态
     */
    private fun updatePaymentState(state: PaymentState) {
        _paymentState.value = state
        LogManager.d(TAG, "支付状态更新: ${state.status}")
    }
    
    /**
     * 根据支付结果更新状态
     */
    private fun updatePaymentStateByResult(result: PaymentResult) {
        when (result) {
            is PaymentResult.Success -> {
                updatePaymentState(PaymentState.success(result))
            }
            is PaymentResult.Cancel -> {
                updatePaymentState(PaymentState.cancelled(result))
            }
            is PaymentResult.Error -> {
                updatePaymentState(PaymentState.error(result))
            }
            is PaymentResult.Processing -> {
                updatePaymentState(PaymentState.processing(result))
            }
        }
    }
    
    /**
     * 获取当前支付状态
     */
    fun getCurrentPaymentState(): PaymentState = _paymentState.value
    
    /**
     * 获取最后的支付结果
     */
    fun getLastPaymentResult(): PaymentResult? = _paymentResult.value
    
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        LogManager.d(TAG, "PaymentViewModel资源已清理")
    }
}
