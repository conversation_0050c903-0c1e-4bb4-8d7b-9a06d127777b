package com.yjsoft.roadtravel.basiclibrary.speech.models

import kotlinx.serialization.Serializable

/**
 * 语音识别状态枚举
 */
enum class SpeechRecognitionState {
    IDLE,           // 空闲状态
    INITIALIZING,   // 初始化中
    LISTENING,      // 监听中（准备录音）
    RECORDING,      // 录音中
    PROCESSING,     // 处理中（识别中）
    SUCCESS,        // 识别成功
    ERROR           // 错误状态
}

/**
 * 语音识别结果数据类
 */
@Serializable
data class SpeechRecognitionResult(
    val text: String = "",                    // 识别出的文本
    val confidence: Float = 0f,               // 置信度 (0-1)
    val isFinal: Boolean = false,             // 是否为最终结果
    val timestamp: Long = System.currentTimeMillis(), // 时间戳
    val duration: Long = 0L,                  // 录音时长(ms)
    val errorCode: Int = 0,                   // 错误码，0表示成功
    val errorMessage: String = ""             // 错误信息
) {
    /**
     * 是否成功
     */
    val isSuccess: Boolean
        get() = errorCode == 0 && text.isNotEmpty()
    
    /**
     * 是否有错误
     */
    val hasError: Boolean
        get() = errorCode != 0
}

/**
 * 语音识别配置参数
 */
@Serializable
data class SpeechRecognitionParams(
    val language: String = "zh",              // 语言代码
    val sampleRate: Int = 16000,              // 采样率
    val format: String = "pcm",               // 音频格式
    val channel: Int = 1,                     // 声道数
    val devPid: Int = 1537,                   // 识别模式
    val maxSpeechTimeS: Int = 60,             // 最长录音时间(秒)
    val minSpeechTimeS: Int = 1,              // 最短录音时间(秒)
    val vadEndpointTimeout: Int = 2000,       // VAD静音检测超时(ms)
    val vadBeginningTimeout: Int = 5000,      // VAD开始录音超时(ms)
    val enableVad: Boolean = false,            // 是否启用VAD（已添加模型文件）
    val enablePunctuation: Boolean = true,    // 是否启用标点符号
    val enableNumberConvert: Boolean = true   // 是否启用数字转换
)

/**
 * 音频录制状态
 */
data class AudioRecordingState(
    val isRecording: Boolean = false,         // 是否正在录音
    val duration: Long = 0L,                  // 录音时长(ms)
    val volume: Float = 0f,                   // 音量级别 (0-1)
    val maxDuration: Long = 60000L,           // 最大录音时长(ms)
    val filePath: String = ""                 // 录音文件路径
) {
    /**
     * 录音进度百分比
     */
    val progress: Float
        get() = if (maxDuration > 0) (duration.toFloat() / maxDuration).coerceIn(0f, 1f) else 0f
    
    /**
     * 是否接近最大时长
     */
    val isNearMaxDuration: Boolean
        get() = progress > 0.9f
}

/**
 * 语音识别错误信息
 */
data class SpeechRecognitionError(
    val code: Int,                            // 错误码
    val message: String,                      // 错误信息
    val cause: Throwable? = null,             // 异常原因
    val timestamp: Long = System.currentTimeMillis() // 错误发生时间
) {
    /**
     * 是否为网络错误
     */
    val isNetworkError: Boolean
        get() = code == -1001
    
    /**
     * 是否为权限错误
     */
    val isPermissionError: Boolean
        get() = code == -1002
    
    /**
     * 是否为录音错误
     */
    val isAudioError: Boolean
        get() = code == -1003
    
    /**
     * 是否为识别失败
     */
    val isRecognitionError: Boolean
        get() = code == -1004
    
    /**
     * 是否为超时错误
     */
    val isTimeoutError: Boolean
        get() = code == -1005
}

/**
 * 语音识别事件
 */
sealed class SpeechRecognitionEvent {
    object StartListening : SpeechRecognitionEvent()
    object StopListening : SpeechRecognitionEvent()
    object StartRecording : SpeechRecognitionEvent()
    object StopRecording : SpeechRecognitionEvent()
    data class VolumeChanged(val volume: Float) : SpeechRecognitionEvent()
    data class PartialResult(val text: String) : SpeechRecognitionEvent()
    data class FinalResult(val result: SpeechRecognitionResult) : SpeechRecognitionEvent()
    data class Error(val error: SpeechRecognitionError) : SpeechRecognitionEvent()
    object Reset : SpeechRecognitionEvent()
}

/**
 * 语音识别回调接口
 */
interface SpeechRecognitionCallback {
    /**
     * 开始录音
     */
    fun onStartRecording() {}
    
    /**
     * 停止录音
     */
    fun onStopRecording() {}
    
    /**
     * 音量变化
     */
    fun onVolumeChanged(volume: Float) {}
    
    /**
     * 部分识别结果
     */
    fun onPartialResult(text: String) {}
    
    /**
     * 最终识别结果
     */
    fun onFinalResult(result: SpeechRecognitionResult) {}
    
    /**
     * 识别错误
     */
    fun onError(error: SpeechRecognitionError) {}
    
    /**
     * 状态变化
     */
    fun onStateChanged(state: SpeechRecognitionState) {}
}
