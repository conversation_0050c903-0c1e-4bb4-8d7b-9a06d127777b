package com.yjsoft.roadtravel.basiclibrary.mvvm.di

import com.yjsoft.roadtravel.basiclibrary.mvvm.examples.ExampleRepository
import com.yjsoft.roadtravel.basiclibrary.mvvm.utils.MVVMUtils
import com.yjsoft.roadtravel.basiclibrary.mvvm.utils.StateUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * MVVM模块的Hilt依赖注入配置
 * 
 * 功能：
 * - 提供MVVM架构相关的依赖注入配置
 * - 管理Repository的单例实例
 * - 提供工具类的依赖注入
 * - 配置MVVM相关的作用域
 * 
 * 使用方式：
 * 此模块会自动被Hilt扫描和安装，无需手动配置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object MVVMModule {
    
    /**
     * 提供示例Repository
     * 
     * 注意：这是示例用途，实际项目中应该根据具体业务创建对应的Repository
     */
    @Provides
    @Singleton
    fun provideExampleRepository(): ExampleRepository {
        return ExampleRepository()
    }
    
    /**
     * 提供MVVM工具类
     * 
     * 虽然MVVMUtils是object，但为了保持依赖注入的一致性，
     * 这里提供一个访问入口
     */
    @Provides
    @Singleton
    fun provideMVVMUtils(): MVVMUtils {
        return MVVMUtils
    }
    
    /**
     * 提供状态工具类
     * 
     * 虽然StateUtils是object，但为了保持依赖注入的一致性，
     * 这里提供一个访问入口
     */
    @Provides
    @Singleton
    fun provideStateUtils(): StateUtils {
        return StateUtils
    }
    
    /**
     * 提供简单缓存实例
     * 
     * 为常用的缓存场景提供预配置的缓存实例
     */
    @Provides
    @Singleton
    @MVVMCache
    fun provideSimpleCache(): MVVMUtils.SimpleCache<String, Any> {
        return MVVMUtils.SimpleCache(maxSize = 100)
    }
    
    /**
     * 提供状态缓存实例
     * 
     * 为状态管理提供专用的缓存实例
     */
    @Provides
    @Singleton
    @StateCache
    fun provideStateCache(): StateUtils.StateCache<String, Any> {
        return StateUtils.StateCache()
    }
    
    /**
     * 提供状态历史管理器
     * 
     * 为状态历史记录提供管理器实例
     */
    @Provides
    @Singleton
    fun provideStateHistory(): StateUtils.StateHistory<Any> {
        return StateUtils.StateHistory(maxSize = 20)
    }
}

/**
 * MVVM缓存限定符
 * 用于区分不同类型的缓存实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MVVMCache

/**
 * 状态缓存限定符
 * 用于区分状态相关的缓存实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class StateCache
