package com.yjsoft.roadtravel.basiclibrary.permission.viewmodel

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yjsoft.roadtravel.basiclibrary.permission.core.MultiplePermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequest
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionResult
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionStatus
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionChecker
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlin.collections.filter
import kotlin.collections.toMutableMap

/**
 * 权限状态持有者
 * 管理权限状态的响应式数据流
 */
class PermissionStateHolder(
    private val context: Context,
    private val permissionManager: PermissionManager
) {
    
    companion object {
        private const val TAG = "PermissionStateHolder"
    }
    
    // 权限状态流
    private val _permissionStates = MutableStateFlow<Map<String, PermissionState>>(emptyMap())
    val permissionStates: StateFlow<Map<String, PermissionState>> = _permissionStates.asStateFlow()
    
    // 权限请求状态流
    private val _isRequesting = MutableStateFlow(false)
    val isRequesting: StateFlow<Boolean> = _isRequesting.asStateFlow()
    
    // 最后的权限请求结果
    private val _lastResult = MutableStateFlow<PermissionResult?>(null)
    val lastResult: StateFlow<PermissionResult?> = _lastResult.asStateFlow()
    
    // 活动的权限请求
    private val _activeRequests = MutableStateFlow<List<PermissionRequest>>(emptyList())
    val activeRequests: StateFlow<List<PermissionRequest>> = _activeRequests.asStateFlow()
    
    /**
     * 检查单个权限状态
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun checkPermission(permission: String, activity: ComponentActivity? = null): StateFlow<PermissionState> {
        return flow {
            val state = PermissionChecker.getPermissionState(context, permission, activity)
            emit(state)
        }.stateIn(
            scope = kotlinx.coroutines.GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PermissionState(permission, PermissionStatus.UNKNOWN)
        )
    }
    
    /**
     * 检查多个权限状态
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun checkPermissions(permissions: List<String>, activity: ComponentActivity? = null): StateFlow<MultiplePermissionState> {
        return flow {
            val state = PermissionChecker.getPermissionsState(context, permissions, activity)
            emit(state)
        }.stateIn(
            scope = kotlinx.coroutines.GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = MultiplePermissionState(emptyMap())
        )
    }
    
    /**
     * 请求权限
     */
    suspend fun requestPermissions(request: PermissionRequest): PermissionResult {
        PermissionLogger.logPermissionRequestStart(
            permissions = request.permissions,
            requestId = request.requestId,
            source = request.source
        )
        
        _isRequesting.value = true
        _activeRequests.value = _activeRequests.value + request
        
        return try {
            val result = permissionManager.requestPermissions(request)
            
            _lastResult.value = result
            
            // 记录结果
            when (result) {
                is PermissionResult.Granted -> {
                    PermissionLogger.logPermissionRequestResult(
                        permissions = result.permissions,
                        grantedPermissions = result.permissions,
                        deniedPermissions = emptyList(),
                        requestId = result.requestId
                    )
                }
                is PermissionResult.Denied -> {
                    PermissionLogger.logPermissionRequestResult(
                        permissions = result.allPermissions,
                        grantedPermissions = result.grantedPermissions,
                        deniedPermissions = result.deniedPermissions,
                        requestId = result.requestId
                    )
                }
                is PermissionResult.Cancelled -> {
                    PermissionLogger.logPermissionRequestCancelled(
                        permissions = result.permissions,
                        requestId = result.requestId,
                        reason = result.reason
                    )
                }
                is PermissionResult.Error -> {
                    PermissionLogger.logPermissionRequestError(
                        permissions = result.permissions,
                        requestId = result.requestId,
                        error = result.error
                    )
                }
                is PermissionResult.Timeout -> {
                    PermissionLogger.logPermissionRequestTimeout(
                        permissions = result.permissions,
                        requestId = result.requestId,
                        timeoutMillis = result.timeoutMillis
                    )
                }
            }
            
            // 更新权限状态
            refreshPermissionStates(request.permissions)
            
            result
        } finally {
            _isRequesting.value = false
            _activeRequests.value = _activeRequests.value.filter { it.requestId != request.requestId }
        }
    }
    
    /**
     * 刷新权限状态
     */
    fun refreshPermissionStates(permissions: List<String>? = null) {
        val targetPermissions = permissions ?: _permissionStates.value.keys.toList()
        if (targetPermissions.isEmpty()) return
        
        val currentStates = _permissionStates.value.toMutableMap()
        
        targetPermissions.forEach { permission ->
            val newState = PermissionChecker.getPermissionState(context, permission)
            val oldState = currentStates[permission]
            
            if (oldState?.status != newState.status) {
                PermissionLogger.logPermissionStateChange(
                    permission = permission,
                    oldStatus = oldState?.status?.name ?: "UNKNOWN",
                    newStatus = newState.status.name
                )
            }
            
            currentStates[permission] = newState
        }
        
        _permissionStates.value = currentStates
    }
    
    /**
     * 获取权限状态
     */
    fun getPermissionState(permission: String): PermissionState? {
        return _permissionStates.value[permission]
    }
    
    /**
     * 检查权限是否已授予
     */
    fun isPermissionGranted(permission: String): Boolean {
        return getPermissionState(permission)?.isGranted 
            ?: PermissionChecker.isPermissionGranted(context, permission)
    }
    
    /**
     * 检查多个权限是否都已授予
     */
    fun arePermissionsGranted(permissions: List<String>): Boolean {
        return permissions.all { isPermissionGranted(it) }
    }
    
    /**
     * 获取被拒绝的权限
     */
    fun getDeniedPermissions(permissions: List<String>): List<String> {
        return permissions.filter { !isPermissionGranted(it) }
    }
    
    /**
     * 获取已授予的权限
     */
    fun getGrantedPermissions(permissions: List<String>): List<String> {
        return permissions.filter { isPermissionGranted(it) }
    }
    
    /**
     * 清除权限状态
     */
    fun clearPermissionStates() {
        _permissionStates.value = emptyMap()
    }
    
    /**
     * 清除请求结果
     */
    fun clearLastResult() {
        _lastResult.value = null
    }
    
    /**
     * 监听权限状态变化
     */
    fun observePermissionChanges(
        permissions: List<String>,
        lifecycleOwner: LifecycleOwner,
        onPermissionChanged: (String, PermissionState) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            permissionStates.collect { states ->
                permissions.forEach { permission ->
                    states[permission]?.let { state ->
                        onPermissionChanged(permission, state)
                    }
                }
            }
        }
    }
    
    /**
     * 监听权限请求结果
     */
    fun observePermissionResults(
        lifecycleOwner: LifecycleOwner,
        onResult: (PermissionResult) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            lastResult.filterNotNull().collect { result ->
                onResult(result)
            }
        }
    }
    
    /**
     * 创建权限状态的组合流
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun combinePermissionStates(permissions: List<String>): StateFlow<MultiplePermissionState> {
        return permissionStates.map { states ->
            val filteredStates = permissions.associateWith { permission ->
                states[permission] ?: PermissionChecker.getPermissionState(context, permission)
            }
            MultiplePermissionState(filteredStates)
        }.stateIn(
            scope = kotlinx.coroutines.GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = MultiplePermissionState(emptyMap())
        )
    }
}
