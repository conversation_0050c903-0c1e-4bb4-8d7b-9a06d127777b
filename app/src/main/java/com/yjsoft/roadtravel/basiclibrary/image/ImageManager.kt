package com.yjsoft.roadtravel.basiclibrary.image

import android.content.Context
import coil3.ImageLoader
import coil3.SingletonImageLoader
import com.yjsoft.roadtravel.basiclibrary.image.config.ImageCacheConfig
import com.yjsoft.roadtravel.basiclibrary.image.config.ImageConfig
import com.yjsoft.roadtravel.basiclibrary.image.loader.ImageLoaderFactory
import com.yjsoft.roadtravel.basiclibrary.image.loader.ImageLoaderInstance
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import okhttp3.OkHttpClient

/**
 * 图片加载管理器
 * 统一管理应用的图片加载功能，类似于NetworkManager的设计模式
 */
object ImageManager {
    
    @Volatile
    private var isInitialized = false
    
    @Volatile
    private var currentConfig: ImageConfig.ImageLoadConfig? = null
    
    @Volatile
    private var imageLoaderInstance: ImageLoaderInstance? = null

    @Volatile
    private var cachedOkHttpClient: OkHttpClient? = null
    
    /**
     * 初始化图片加载框架
     * @param context 应用上下文
     * @param config 图片加载配置，如果为null则使用默认配置
     * @param okHttpClient 自定义OkHttpClient，如果为null则尝试使用NetworkManager的客户端
     */
    fun init(
        context: Context,
        config: ImageConfig.ImageLoadConfig? = null,
        okHttpClient: OkHttpClient? = null
    ) {
        if (isInitialized) {
            LogManager.tag(LogConfig.Tags.IMAGE).w("ImageManager已经初始化，跳过重复初始化")
            return
        }
        
        try {
            LogManager.tag(LogConfig.Tags.IMAGE).d("开始初始化图片加载框架")
            
            // 1. 设置图片加载配置
            val targetConfig = config ?: getDefaultConfig(context, okHttpClient)
            ImageConfig.setConfig(targetConfig)
            currentConfig = targetConfig
            LogManager.tag(LogConfig.Tags.IMAGE).i("图片加载配置设置完成: ${targetConfig.environment.name}")
            
            // 2. 获取OkHttpClient
            val httpClient = getOkHttpClient(context, okHttpClient)
            if (httpClient != null) {
                LogManager.tag(LogConfig.Tags.IMAGE).d("使用自定义OkHttpClient")
            } else {
                LogManager.tag(LogConfig.Tags.IMAGE).d("使用默认网络配置")
            }
            
            // 3. 初始化ImageLoaderInstance
            imageLoaderInstance = ImageLoaderInstance.getInstance(context, targetConfig, httpClient)
            
            // 4. 配置Coil单例ImageLoader
            configureSingletonImageLoader(context, targetConfig, httpClient)
            
            // 5. 预热ImageLoader（可选）
            if (shouldPreInitialize()) {
                preInitializeImageLoader()
            }
            
            isInitialized = true
            LogManager.tag(LogConfig.Tags.IMAGE).i("图片加载框架初始化完成")
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "图片加载框架初始化失败")
            throw e
        }
    }
    
    /**
     * 获取ImageLoader实例
     */
    fun getImageLoader(context: Context): ImageLoader {
        if (!isInitialized) {
            LogManager.tag(LogConfig.Tags.IMAGE).w("图片加载框架未初始化，使用默认配置")
            init(context)
        }
        
        return imageLoaderInstance?.getImageLoader() ?: SingletonImageLoader.get(context)
    }
    
    /**
     * 获取指定类型的ImageLoader
     */
    fun getImageLoader(
        context: Context,
        type: ImageLoaderFactory.LoaderType
    ): ImageLoader {
        val okHttpClient = getOkHttpClient(context, null)
        return ImageLoaderFactory.createByType(type, context, okHttpClient)
    }
    
    /**
     * 获取ImageLoaderInstance
     */
    fun getImageLoaderInstance(context: Context): ImageLoaderInstance {
        if (!isInitialized) {
            LogManager.tag(LogConfig.Tags.IMAGE).w("图片加载框架未初始化，使用默认配置")
            init(context)
        }
        
        return imageLoaderInstance ?: ImageLoaderInstance.getInstance(context)
    }
    
    /**
     * 清理所有缓存
     */
    fun clearAllCache() {
        imageLoaderInstance?.clearCache()
        LogManager.tag(LogConfig.Tags.IMAGE).i("所有图片缓存已清理")
    }
    
    /**
     * 清理内存缓存
     */
    fun clearMemoryCache() {
        imageLoaderInstance?.clearMemoryCache()
        LogManager.tag(LogConfig.Tags.IMAGE).i("图片内存缓存已清理")
    }
    
    /**
     * 清理磁盘缓存
     */
    fun clearDiskCache() {
        imageLoaderInstance?.clearDiskCache()
        LogManager.tag(LogConfig.Tags.IMAGE).i("图片磁盘缓存已清理")
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): ImageCacheConfig.CacheStats? {
        return imageLoaderInstance?.getCacheStats()
    }
    
    /**
     * 获取缓存使用报告
     */
    fun getCacheUsageReport(): String {
        return imageLoaderInstance?.getCacheUsageReport() ?: "图片加载框架未初始化"
    }
    
    /**
     * 检查缓存健康状态
     */
    fun checkCacheHealth(context: Context): Boolean {
        return imageLoaderInstance?.checkCacheHealth() ?: false
    }
    
    /**
     * 优化缓存配置
     */
    fun optimizeCacheConfig(context: Context) {
        try {
            val optimizedConfig = ImageCacheConfig.optimizeCacheConfig(context)
            ImageConfig.setConfig(optimizedConfig)
            currentConfig = optimizedConfig
            
            // 重置ImageLoader实例以应用新配置
            ImageLoaderInstance.reset()
            imageLoaderInstance = ImageLoaderInstance.getInstance(context, optimizedConfig, getOkHttpClient(context, null))
            
            LogManager.tag(LogConfig.Tags.IMAGE).i("缓存配置已优化")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "缓存配置优化失败")
        }
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): ImageConfig.ImageLoadConfig? = currentConfig
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 获取框架状态信息
     * @param context 应用上下文，用于检查缓存健康状态
     */
    fun getStatus(context: Context): ImageStatus {
        return ImageStatus(
            isInitialized = isInitialized,
            currentConfig = currentConfig,
            cacheStats = getCacheStats(),
            cacheHealthy = if (isInitialized) checkCacheHealth(context) else false
        )
    }
    
    /**
     * 获取默认配置
     */
    private fun getDefaultConfig(context: Context, okHttpClient: OkHttpClient?): ImageConfig.ImageLoadConfig {
        return if (isDebugBuild(context)) {
            ImageConfig.createDebugConfig(okHttpClient)
        } else {
            ImageConfig.createReleaseConfig(okHttpClient)
        }
    }
    
    /**
     * 获取OkHttpClient
     * @param context 应用上下文
     * @param customClient 自定义OkHttpClient
     */
    private fun getOkHttpClient(context: Context, customClient: OkHttpClient?): OkHttpClient? {
        return customClient ?: cachedOkHttpClient ?: try {
            // 尝试从NetworkManager获取OkHttpClient
            if (NetworkManager.isInitialized()) {
                val httpClient = NetworkManager.getRetrofitInstance(context).getOkHttpClient()
                cachedOkHttpClient = httpClient // 缓存以避免重复获取
                LogManager.tag(LogConfig.Tags.IMAGE).d("从NetworkManager获取OkHttpClient成功")
                httpClient
            } else {
                LogManager.tag(LogConfig.Tags.IMAGE).d("NetworkManager未初始化，使用默认网络配置")
                null
            }
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "无法获取NetworkManager的OkHttpClient，使用默认配置")
            null
        }
    }
    
    /**
     * 配置Coil单例ImageLoader
     */
    private fun configureSingletonImageLoader(
        context: Context,
        config: ImageConfig.ImageLoadConfig,
        okHttpClient: OkHttpClient?
    ) {
        try {
            SingletonImageLoader.setSafe {
                ImageLoaderFactory.createDefault(context, okHttpClient)
            }
            LogManager.tag(LogConfig.Tags.IMAGE).d("Coil单例ImageLoader配置完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "Coil单例ImageLoader配置失败")
        }
    }
    
    /**
     * 是否应该预初始化
     */
    private fun shouldPreInitialize(): Boolean {
        return currentConfig?.environment == ImageConfig.Environment.DEBUG
    }
    
    /**
     * 预初始化ImageLoader
     */
    private fun preInitializeImageLoader() {
        try {
            LogManager.tag(LogConfig.Tags.IMAGE).d("开始预初始化ImageLoader")
            imageLoaderInstance?.warmUp()
            LogManager.tag(LogConfig.Tags.IMAGE).d("ImageLoader预初始化完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "ImageLoader预初始化失败，将使用懒加载")
        }
    }
    
    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(context: Context): Boolean {
        return try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            LogManager.tag(LogConfig.Tags.IMAGE).i("ImageManager正在清理资源")
            imageLoaderInstance?.cleanup()
            ImageLoaderInstance.reset()
            isInitialized = false
            currentConfig = null
            imageLoaderInstance = null
            cachedOkHttpClient = null
            LogManager.tag(LogConfig.Tags.IMAGE).i("ImageManager资源清理完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "ImageManager资源清理失败")
        }
    }
    
    /**
     * 图片加载框架状态信息
     */
    data class ImageStatus(
        val isInitialized: Boolean,
        val currentConfig: ImageConfig.ImageLoadConfig?,
        val cacheStats: ImageCacheConfig.CacheStats?,
        val cacheHealthy: Boolean
    )
}
