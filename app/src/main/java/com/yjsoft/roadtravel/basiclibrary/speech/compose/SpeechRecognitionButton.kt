package com.yjsoft.roadtravel.basiclibrary.speech.compose

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionState
import kotlinx.coroutines.launch

/**
 * 语音识别按钮组件
 * 提供完整的语音识别UI交互
 */
@Composable
fun SpeechRecognitionButton(
    modifier: Modifier = Modifier,
    onResult: (String) -> Unit = {},
    onError: (String) -> Unit = {},
    enabled: Boolean = true,
    size: androidx.compose.ui.unit.Dp = 64.dp,
    colors: SpeechButtonColors = SpeechButtonDefaults.colors()
) {
    val context = LocalContext.current
    val speechManager = remember { SpeechRecognitionManager.getInstance(context) }
    val coroutineScope = rememberCoroutineScope()
    
    // 收集状态
    val state by speechManager.state.collectAsStateWithLifecycle()
    val result by speechManager.result.collectAsStateWithLifecycle()
    val audioState by speechManager.audioState.collectAsStateWithLifecycle()
    val error by speechManager.error.collectAsStateWithLifecycle(initialValue = null)
    
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = if (state == SpeechRecognitionState.RECORDING) 1.1f else 1.0f,
        animationSpec = tween(300),
        label = "scale"
    )
    
    val infiniteTransition = rememberInfiniteTransition(label = "infinite")
    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )
    
    // 处理结果
    LaunchedEffect(result) {
        if (result.isSuccess && result.isFinal) {
            onResult(result.text)
        }
    }
    
    // 处理错误
    LaunchedEffect(error) {
        error?.let {
            onError(it.message)
        }
    }
    
    // 初始化SDK
    LaunchedEffect(Unit) {
        if (!speechManager.isSDKInitialized()) {
            speechManager.initialize()
        }
    }
    
    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        // 脉冲背景（录音时显示）
        if (state == SpeechRecognitionState.RECORDING) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .scale(1.5f)
                    .clip(CircleShape)
                    .background(
                        colors.recordingColor.copy(alpha = pulseAlpha)
                    )
            )
        }
        
        // 主按钮
        FloatingActionButton(
            onClick = {
                coroutineScope.launch {
                    handleButtonClick(speechManager, state)
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .scale(scale),
            containerColor = getButtonColor(state, colors),
            contentColor = getContentColor(state, colors),
            elevation = FloatingActionButtonDefaults.elevation(
                defaultElevation = if (state == SpeechRecognitionState.RECORDING) 8.dp else 6.dp
            )
        ) {
            Icon(
                imageVector = getButtonIcon(state),
                contentDescription = getContentDescription(state),
                modifier = Modifier.size(size * 0.4f)
            )
        }
    }
}

/**
 * 音量指示器组件
 */
@Composable
fun VolumeIndicator(
    volume: Float,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    maxBars: Int = 10
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalAlignment = Alignment.Bottom
    ) {
        repeat(maxBars) { index ->
            val barHeight = (volume * maxBars).toInt()
            val isActive = index < barHeight
            
            Box(
                modifier = Modifier
                    .width(4.dp)
                    .height(if (isActive) (8 + index * 2).dp else 4.dp)
                    .background(
                        color = if (isActive) color else color.copy(alpha = 0.3f),
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(2.dp)
                    )
            )
        }
    }
}

/**
 * 语音识别状态指示器
 */
@Composable
fun SpeechStateIndicator(
    state: SpeechRecognitionState,
    modifier: Modifier = Modifier
) {
    val stateText = when (state) {
        SpeechRecognitionState.IDLE -> "点击开始"
        SpeechRecognitionState.INITIALIZING -> "初始化中..."
        SpeechRecognitionState.LISTENING -> "准备录音..."
        SpeechRecognitionState.RECORDING -> "正在录音"
        SpeechRecognitionState.PROCESSING -> "识别中..."
        SpeechRecognitionState.SUCCESS -> "识别完成"
        SpeechRecognitionState.ERROR -> "识别失败"
    }
    
    val stateColor = when (state) {
        SpeechRecognitionState.IDLE -> MaterialTheme.colorScheme.onSurface
        SpeechRecognitionState.INITIALIZING -> MaterialTheme.colorScheme.primary
        SpeechRecognitionState.LISTENING -> MaterialTheme.colorScheme.primary
        SpeechRecognitionState.RECORDING -> MaterialTheme.colorScheme.error
        SpeechRecognitionState.PROCESSING -> MaterialTheme.colorScheme.primary
        SpeechRecognitionState.SUCCESS -> Color(0xFF4CAF50)
        SpeechRecognitionState.ERROR -> MaterialTheme.colorScheme.error
    }
    
    Text(
        text = stateText,
        modifier = modifier,
        color = stateColor,
        fontSize = 12.sp,
        fontWeight = FontWeight.Medium
    )
}

/**
 * 处理按钮点击
 */
private suspend fun handleButtonClick(
    speechManager: SpeechRecognitionManager,
    currentState: SpeechRecognitionState
) {
    when (currentState) {
        SpeechRecognitionState.IDLE -> {
            if (speechManager.hasRecordPermission()) {
                speechManager.startRecognition()
            }
        }
        SpeechRecognitionState.RECORDING -> {
            speechManager.stopRecognition()
        }
        SpeechRecognitionState.PROCESSING -> {
            speechManager.cancelRecognition()
        }
        else -> {
            speechManager.reset()
        }
    }
}

/**
 * 获取按钮图标
 */
private fun getButtonIcon(state: SpeechRecognitionState): ImageVector {
    return when (state) {
        SpeechRecognitionState.IDLE -> Icons.Default.Mic
        SpeechRecognitionState.INITIALIZING -> Icons.Default.Mic
        SpeechRecognitionState.LISTENING -> Icons.Default.Mic
        SpeechRecognitionState.RECORDING -> Icons.Default.Stop
        SpeechRecognitionState.PROCESSING -> Icons.Default.MicOff
        SpeechRecognitionState.SUCCESS -> Icons.Default.Mic
        SpeechRecognitionState.ERROR -> Icons.Default.Mic
    }
}

/**
 * 获取按钮颜色
 */
@Composable
private fun getButtonColor(state: SpeechRecognitionState, colors: SpeechButtonColors): Color {
    return when (state) {
        SpeechRecognitionState.IDLE -> colors.idleColor
        SpeechRecognitionState.INITIALIZING -> colors.processingColor
        SpeechRecognitionState.LISTENING -> colors.listeningColor
        SpeechRecognitionState.RECORDING -> colors.recordingColor
        SpeechRecognitionState.PROCESSING -> colors.processingColor
        SpeechRecognitionState.SUCCESS -> colors.successColor
        SpeechRecognitionState.ERROR -> colors.errorColor
    }
}

/**
 * 获取内容颜色
 */
@Composable
private fun getContentColor(state: SpeechRecognitionState, colors: SpeechButtonColors): Color {
    return when (state) {
        SpeechRecognitionState.RECORDING -> Color.White
        else -> colors.contentColor
    }
}

/**
 * 获取内容描述
 */
private fun getContentDescription(state: SpeechRecognitionState): String {
    return when (state) {
        SpeechRecognitionState.IDLE -> "开始语音识别"
        SpeechRecognitionState.RECORDING -> "停止录音"
        SpeechRecognitionState.PROCESSING -> "取消识别"
        else -> "语音识别按钮"
    }
}
