package com.yjsoft.roadtravel.basiclibrary.permission.core

/**
 * 权限请求结果
 * 封装权限请求的结果信息
 */
sealed class PermissionResult {

    /**
     * 请求ID
     */
    abstract val requestId: String

    /**
     * 时间戳
     */
    abstract val timestamp: Long

    /**
     * 涉及的权限列表
     */
    abstract val permissions: List<String>

    /**
     * 是否成功（所有权限都已授予）
     */
    val isSuccess: Boolean
        get() = this is Granted

    /**
     * 是否失败
     */
    val isFailure: Boolean
        get() = !isSuccess

    /**
     * 权限请求成功（所有权限都已授予）
     */
    data class Granted(
        override val permissions: List<String>,
        override val requestId: String,
        override val timestamp: Long = System.currentTimeMillis()
    ) : PermissionResult() {
        
        /**
         * 是否为单个权限
         */
        val isSinglePermission: Boolean
            get() = permissions.size == 1
        
        /**
         * 获取第一个权限（用于单个权限场景）
         */
        val firstPermission: String?
            get() = permissions.firstOrNull()
    }
    
    /**
     * 权限请求被拒绝
     */
    data class Denied(
        val deniedPermissions: List<String>,
        val grantedPermissions: List<String> = emptyList(),
        val permanentlyDeniedPermissions: List<String> = emptyList(),
        override val requestId: String,
        override val timestamp: Long = System.currentTimeMillis()
    ) : PermissionResult() {
        
        /**
         * 所有权限都被拒绝
         */
        val allDenied: Boolean
            get() = grantedPermissions.isEmpty()
        
        /**
         * 部分权限被拒绝
         */
        val partiallyDenied: Boolean
            get() = grantedPermissions.isNotEmpty() && deniedPermissions.isNotEmpty()
        
        /**
         * 是否有权限被永久拒绝
         */
        val hasPermanentlyDenied: Boolean
            get() = permanentlyDeniedPermissions.isNotEmpty()
        
        /**
         * 获取所有请求的权限
         */
        override val permissions: List<String>
            get() = (grantedPermissions + deniedPermissions + permanentlyDeniedPermissions).distinct()

        /**
         * 获取所有请求的权限（别名，保持向后兼容）
         */
        val allPermissions: List<String>
            get() = permissions
    }

    /**
     * 权限请求被取消（用户取消了权限申请流程）
     */
    data class Cancelled(
        override val permissions: List<String>,
        override val requestId: String,
        val reason: String? = null,
        override val timestamp: Long = System.currentTimeMillis()
    ) : PermissionResult()
    
    /**
     * 权限请求出错
     */
    data class Error(
        override val permissions: List<String>,
        override val requestId: String,
        val error: Throwable,
        override val timestamp: Long = System.currentTimeMillis()
    ) : PermissionResult() {
        
        /**
         * 错误消息
         */
        val errorMessage: String
            get() = error.message ?: "未知错误"
    }
    
    /**
     * 权限请求超时
     */
    data class Timeout(
        override val permissions: List<String>,
        override val requestId: String,
        val timeoutMillis: Long,
        override val timestamp: Long = System.currentTimeMillis()
    ) : PermissionResult()
    

}

/**
 * 权限结果回调接口
 */
interface PermissionResultCallback {
    
    /**
     * 权限授予回调
     */
    fun onPermissionGranted(result: PermissionResult.Granted) {}
    
    /**
     * 权限拒绝回调
     */
    fun onPermissionDenied(result: PermissionResult.Denied) {}
    
    /**
     * 权限请求取消回调
     */
    fun onPermissionCancelled(result: PermissionResult.Cancelled) {}
    
    /**
     * 权限请求错误回调
     */
    fun onPermissionError(result: PermissionResult.Error) {}
    
    /**
     * 权限请求超时回调
     */
    fun onPermissionTimeout(result: PermissionResult.Timeout) {}
    
    /**
     * 权限结果回调（通用）
     */
    fun onPermissionResult(result: PermissionResult) {
        when (result) {
            is PermissionResult.Granted -> onPermissionGranted(result)
            is PermissionResult.Denied -> onPermissionDenied(result)
            is PermissionResult.Cancelled -> onPermissionCancelled(result)
            is PermissionResult.Error -> onPermissionError(result)
            is PermissionResult.Timeout -> onPermissionTimeout(result)
        }
    }
}

/**
 * 简化的权限结果回调
 */
fun interface SimplePermissionCallback {
    fun onResult(granted: Boolean, permissions: List<String>)
}

/**
 * 权限结果扩展函数
 */

/**
 * 执行成功时的操作
 */
inline fun PermissionResult.onSuccess(action: (PermissionResult.Granted) -> Unit): PermissionResult {
    if (this is PermissionResult.Granted) {
        action(this)
    }
    return this
}

/**
 * 执行失败时的操作
 */
inline fun PermissionResult.onFailure(action: (PermissionResult) -> Unit): PermissionResult {
    if (isFailure) {
        action(this)
    }
    return this
}

/**
 * 执行拒绝时的操作
 */
inline fun PermissionResult.onDenied(action: (PermissionResult.Denied) -> Unit): PermissionResult {
    if (this is PermissionResult.Denied) {
        action(this)
    }
    return this
}

/**
 * 执行错误时的操作
 */
inline fun PermissionResult.onError(action: (PermissionResult.Error) -> Unit): PermissionResult {
    if (this is PermissionResult.Error) {
        action(this)
    }
    return this
}
