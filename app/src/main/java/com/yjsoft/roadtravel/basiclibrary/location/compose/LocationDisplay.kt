package com.yjsoft.roadtravel.basiclibrary.location.compose

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Place
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.outlined.Place
import androidx.compose.material.icons.sharp.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationState
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationStatus
import java.text.SimpleDateFormat
import java.util.*

/**
 * 定位状态指示器
 * 显示当前定位状态
 */
@Composable
fun LocationStatusIndicator(
    locationState: LocationState,
    modifier: Modifier = Modifier,
    showLabel: Boolean = true
) {
    val (color, icon, statusText) = when (locationState.status) {
        LocationStatus.SUCCESS -> Triple(
            MaterialTheme.colorScheme.primary,
            Icons.Default.LocationOn,
            "定位成功"
        )
        LocationStatus.LOCATING -> Triple(
            MaterialTheme.colorScheme.secondary,
            Icons.Default.Place,
            "定位中..."
        )
        LocationStatus.FAILED -> Triple(
            MaterialTheme.colorScheme.error,
            Icons.Outlined.Place,
            "定位失败"
        )
        LocationStatus.PERMISSION_DENIED -> Triple(
            MaterialTheme.colorScheme.error,
            Icons.Outlined.Place,
            "权限被拒绝"
        )
        LocationStatus.GPS_DISABLED -> Triple(
            MaterialTheme.colorScheme.tertiary,
            Icons.Outlined.Place,
            "GPS未开启"
        )
        LocationStatus.NETWORK_UNAVAILABLE -> Triple(
            MaterialTheme.colorScheme.tertiary,
            Icons.Sharp.Warning,
            "网络不可用"
        )
        else -> Triple(
            MaterialTheme.colorScheme.outline,
            Icons.Default.LocationOn,
            "未知状态"
        )
    }
    
    // 动画颜色
    val animatedColor by animateColorAsState(
        targetValue = color,
        animationSpec = tween(300),
        label = "location_status_color"
    )
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 状态图标
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
                .background(animatedColor.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = statusText,
                modifier = Modifier.size(16.dp),
                tint = animatedColor
            )
        }
        
        if (showLabel) {
            Text(
                text = statusText,
                style = MaterialTheme.typography.bodyMedium,
                color = animatedColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 定位信息卡片
 * 显示详细的定位信息
 */
@Composable
fun LocationInfoCard(
    locationData: LocationData?,
    locationState: LocationState,
    modifier: Modifier = Modifier,
    onRetryClick: (() -> Unit)? = null
) {

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 状态指示器
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                LocationStatusIndicator(
                    locationState = locationState,
                    showLabel = true
                )
                
                // 重试按钮
                if (locationState.status == LocationStatus.FAILED && onRetryClick != null) {
                    IconButton(onClick = onRetryClick) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "重试",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            if (locationData != null && locationData.isValid) {
                Spacer(modifier = Modifier.height(12.dp))
                
                // 地址信息
                Text(
                    text = locationData.fullAddress,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 坐标信息
                Text(
                    text = "坐标: ${LocationUtils.formatCoordinate(locationData.latitude, locationData.longitude)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 精度信息
                Text(
                    text = "精度: ${LocationUtils.formatAccuracy(locationData.accuracy)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 定位类型
                Text(
                    text = "定位方式: ${locationData.locationType.description}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 更新时间
                if (locationData.timestamp > 0) {
                    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                    val timeText = timeFormat.format(Date(locationData.timestamp))
                    Text(
                        text = "更新时间: $timeText",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else if (locationState.error != null) {
                Spacer(modifier = Modifier.height(8.dp))
                
                // 错误信息
                Text(
                    text = locationState.error.userFriendlyMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * 简洁的定位信息显示
 */
@Composable
fun LocationInfoCompact(
    locationData: LocationData?,
    locationState: LocationState,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        LocationStatusIndicator(
            locationState = locationState,
            showLabel = false
        )
        
        Column {
            if (locationData != null && locationData.isValid) {
                Text(
                    text = locationData.shortAddress,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = LocationUtils.formatAccuracy(locationData.accuracy),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                Text(
                    text = locationState.statusDescription,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 定位精度指示器
 */
@Composable
fun LocationAccuracyIndicator(
    accuracy: Float,
    modifier: Modifier = Modifier
) {
    val (color, description) = when {
        accuracy <= 10 -> Pair(MaterialTheme.colorScheme.primary, "高精度")
        accuracy <= 50 -> Pair(MaterialTheme.colorScheme.secondary, "中等精度")
        accuracy <= 100 -> Pair(MaterialTheme.colorScheme.tertiary, "低精度")
        else -> Pair(MaterialTheme.colorScheme.error, "精度较差")
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(color)
        )
        
        Text(
            text = "$description (${accuracy.toInt()}m)",
            style = MaterialTheme.typography.bodySmall,
            color = color
        )
    }
}

/**
 * 定位加载指示器
 */
@Composable
fun LocationLoadingIndicator(
    isLocating: Boolean,
    modifier: Modifier = Modifier
) {
    if (isLocating) {
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "正在定位...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
