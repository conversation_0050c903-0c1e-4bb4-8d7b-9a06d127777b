package com.yjsoft.roadtravel.basiclibrary.auth.wechat

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * 微信登录服务
 * 整合微信登录的完整流程
 * 使用单例模式，不依赖Hilt注入
 */
class WeChatLoginService private constructor() {

    companion object {
        private const val TAG = "WeChatLoginService %s"

        @Volatile
        private var INSTANCE: WeChatLoginService? = null

        /**
         * 获取单例实例
         */
        fun getInstance(): WeChatLoginService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WeChatLoginService().also { INSTANCE = it }
            }
        }
    }

    // 微信登录管理器实例
    private val weChatLoginManager: WeChatLoginManager by lazy {
        WeChatLoginManager.getInstance()
    }

    private val weChatApiService: WeChatApiService by lazy {
        Retrofit.Builder()
            .baseUrl(WeChatConfig.API_BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(WeChatApiService::class.java)
    }

    /**
     * 初始化微信登录服务
     */
    fun initialize(context: Context): Boolean {
        return weChatLoginManager.initialize(context)
    }

    /**
     * 检查微信是否已安装
     */
    fun isWeChatInstalled(): Boolean {
        return weChatLoginManager.isWeChatInstalled()
    }

    /**
     * 执行完整的微信登录流程
     * @param context 应用上下文，用于获取网络服务
     */
    suspend fun performLogin(context: Context): WeChatLoginServiceResult =
        withContext(Dispatchers.IO) {
            try {
                LogManager.d(TAG, "开始微信登录流程")

                // 获取API服务实例
                val apiService = NetworkManager.getApiService(context)

                // 第一步：发起微信授权
                val loginResult = weChatLoginManager.login()
                when (loginResult) {
                    is WeChatLoginResult.Success -> {
                        LogManager.d(
                            TAG,
                            "微信授权成功，开始调用服务端登录接口，code=${loginResult.code}"
                        )

                        // 调用本地服务端登录接口
                        try {
                            val loginResponse =
                                apiService.login(loginResult.code)

                            if (loginResponse.code == 0) {
                                LogManager.d(TAG, "服务端登录成功 ${loginResponse.data}")

                                // 将服务端返回的数据转换为微信用户信息
                                val userInfo = WeChatUserInfo(
                                    openId = loginResponse.data.openId,
                                    nickname = loginResponse.data.nickname,
                                    avatar = loginResponse.data.avatar,
                                    sex = "1", // 服务端未返回性别信息，使用默认值
                                    province = "", // 服务端未返回省份信息
                                    city = "", // 服务端未返回城市信息
                                    country = "", // 服务端未返回国家信息
                                    unionId = loginResponse.data.userId // 服务端未返回unionId,使用服务端的userId来代替
                                )

                                return@withContext WeChatLoginServiceResult.Success(
                                    userInfo = userInfo,
                                    accessToken = loginResponse.data.token,
                                    refreshToken = null, // 服务端未返回refreshToken
                                    expiresIn = 7200 // 默认2小时过期
                                )
                            } else {
                                LogManager.w(TAG, "服务端登录失败: ${loginResponse.msg}")
                                return@withContext WeChatLoginServiceResult.Error("登录失败: ${loginResponse.msg}")
                            }
                        } catch (e: Exception) {
                            LogManager.e(TAG, "调用服务端登录接口异常", e)
                            return@withContext WeChatLoginServiceResult.Error("网络异常: ${e.message}")
                        }
                    }

                    is WeChatLoginResult.Cancelled -> {
                        LogManager.d(TAG, "用户取消微信登录")
                        WeChatLoginServiceResult.Cancelled
                    }

                    is WeChatLoginResult.Denied -> {
                        LogManager.d(TAG, "用户拒绝微信授权")
                        WeChatLoginServiceResult.Denied
                    }

                    is WeChatLoginResult.Error -> {
                        LogManager.w(TAG, "微信登录失败: ${loginResult.message}")
                        WeChatLoginServiceResult.Error(loginResult.message)
                    }
                }

            } catch (e: Exception) {
                LogManager.e(TAG, "微信登录流程异常", e)
                WeChatLoginServiceResult.Error("登录异常: ${e.message}")
            }
        }

    /**
     * 通过code获取access_token
     */
    private suspend fun getAccessToken(code: String): WeChatTokenResult {
        return try {
            val response = weChatApiService.getAccessToken(
                appId = WeChatConfig.APP_ID,
                secret = "",
                code = code
            )

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.isSuccess()) {
                    WeChatTokenResult.Success(
                        accessToken = body.access_token!!,
                        refreshToken = body.refresh_token,
                        expiresIn = body.expires_in ?: 7200,
                        openId = body.openid!!,
                        unionId = body.unionid
                    )
                } else {
                    WeChatTokenResult.Error(body?.getErrorMessage() ?: "获取token失败")
                }
            } else {
                WeChatTokenResult.Error("网络请求失败: ${response.code()}")
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取access_token异常", e)
            WeChatTokenResult.Error("网络异常: ${e.message}")
        }
    }

    /**
     * 获取用户信息
     */
    private suspend fun getUserInfo(accessToken: String, openId: String): WeChatUserInfoResult {
        return try {
            val response = weChatApiService.getUserInfo(
                accessToken = accessToken,
                openId = openId
            )

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.isSuccess()) {
                    val userInfo = WeChatUserInfo.fromResponse(body)
                    if (userInfo != null) {
                        WeChatUserInfoResult.Success(userInfo)
                    } else {
                        WeChatUserInfoResult.Error("用户信息解析失败")
                    }
                } else {
                    WeChatUserInfoResult.Error(body?.getErrorMessage() ?: "获取用户信息失败")
                }
            } else {
                WeChatUserInfoResult.Error("网络请求失败: ${response.code()}")
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取用户信息异常", e)
            WeChatUserInfoResult.Error("网络异常: ${e.message}")
        }
    }

    /**
     * 处理微信登录回调
     */
    fun handleLoginCallback(code: String?, state: String?, errCode: Int) {
        weChatLoginManager.handleLoginCallback(code, state, errCode)
    }
}

/**
 * 微信Token结果
 */
sealed class WeChatTokenResult {
    data class Success(
        val accessToken: String,
        val refreshToken: String?,
        val expiresIn: Int,
        val openId: String,
        val unionId: String?
    ) : WeChatTokenResult()

    data class Error(val message: String) : WeChatTokenResult()
}

/**
 * 微信用户信息结果
 */
sealed class WeChatUserInfoResult {
    data class Success(val userInfo: WeChatUserInfo) : WeChatUserInfoResult()
    data class Error(val message: String) : WeChatUserInfoResult()
}

/**
 * 微信登录服务结果
 */
sealed class WeChatLoginServiceResult {
    data class Success(
        val userInfo: WeChatUserInfo,
        val accessToken: String,
        val refreshToken: String?,
        val expiresIn: Int
    ) : WeChatLoginServiceResult()

    object Cancelled : WeChatLoginServiceResult()
    object Denied : WeChatLoginServiceResult()
    data class Error(val message: String) : WeChatLoginServiceResult()
}
