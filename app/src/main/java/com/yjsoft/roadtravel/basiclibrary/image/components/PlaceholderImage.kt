package com.yjsoft.roadtravel.basiclibrary.image.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BrokenImage
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.ImageNotSupported
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 占位符图片组件
 * 用于在图片加载失败或无图片时显示占位内容
 */
@Composable
fun PlaceholderImage(
    modifier: Modifier = Modifier,
    icon: ImageVector = Icons.Default.Image,
    text: String? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    iconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    shape: Shape = RoundedCornerShape(8.dp),
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    iconSize: Dp = 48.dp,
    textSize: TextUnit = 14.sp
) {
    Box(
        modifier = modifier
            .clip(shape)
            .background(backgroundColor)
            .then(
                if (borderWidth > 0.dp) {
                    Modifier.border(borderWidth, borderColor, shape)
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(16.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(iconSize)
            )
            
            text?.let { displayText ->
                Text(
                    text = displayText,
                    color = textColor,
                    fontSize = textSize,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

/**
 * 加载中占位符
 */
@Composable
fun LoadingPlaceholder(
    modifier: Modifier = Modifier,
    text: String = "加载中...",
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    iconColor: Color = MaterialTheme.colorScheme.primary,
    textColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    shape: Shape = RoundedCornerShape(8.dp),
    iconSize: Dp = 48.dp,
    textSize: TextUnit = 14.sp
) {
    PlaceholderImage(
        modifier = modifier,
        icon = Icons.Default.Image,
        text = text,
        backgroundColor = backgroundColor,
        iconColor = iconColor,
        textColor = textColor,
        shape = shape,
        iconSize = iconSize,
        textSize = textSize
    )
}

/**
 * 错误占位符
 */
@Composable
fun ErrorPlaceholder(
    modifier: Modifier = Modifier,
    text: String = "加载失败",
    backgroundColor: Color = MaterialTheme.colorScheme.errorContainer,
    iconColor: Color = MaterialTheme.colorScheme.error,
    textColor: Color = MaterialTheme.colorScheme.onErrorContainer,
    shape: Shape = RoundedCornerShape(8.dp),
    iconSize: Dp = 48.dp,
    textSize: TextUnit = 14.sp
) {
    PlaceholderImage(
        modifier = modifier,
        icon = Icons.Default.BrokenImage,
        text = text,
        backgroundColor = backgroundColor,
        iconColor = iconColor,
        textColor = textColor,
        shape = shape,
        iconSize = iconSize,
        textSize = textSize
    )
}

/**
 * 空状态占位符
 */
@Composable
fun EmptyPlaceholder(
    modifier: Modifier = Modifier,
    text: String = "暂无图片",
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    iconColor: Color = MaterialTheme.colorScheme.outline,
    textColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    shape: Shape = RoundedCornerShape(8.dp),
    iconSize: Dp = 48.dp,
    textSize: TextUnit = 14.sp
) {
    PlaceholderImage(
        modifier = modifier,
        icon = Icons.Default.ImageNotSupported,
        text = text,
        backgroundColor = backgroundColor,
        iconColor = iconColor,
        textColor = textColor,
        shape = shape,
        iconSize = iconSize,
        textSize = textSize
    )
}

/**
 * 方形占位符
 */
@Composable
fun SquarePlaceholder(
    size: Dp,
    modifier: Modifier = Modifier,
    icon: ImageVector = Icons.Default.Image,
    text: String? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    iconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    cornerRadius: Dp = 8.dp,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent
) {
    PlaceholderImage(
        modifier = modifier.size(size),
        icon = icon,
        text = text,
        backgroundColor = backgroundColor,
        iconColor = iconColor,
        textColor = textColor,
        shape = RoundedCornerShape(cornerRadius),
        borderWidth = borderWidth,
        borderColor = borderColor,
        iconSize = size * 0.3f,
        textSize = (size.value / 6).sp
    )
}

/**
 * 圆形占位符
 */
@Composable
fun CirclePlaceholder(
    size: Dp,
    modifier: Modifier = Modifier,
    icon: ImageVector = Icons.Default.Image,
    text: String? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    iconColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent
) {
    PlaceholderImage(
        modifier = modifier.size(size),
        icon = icon,
        text = text,
        backgroundColor = backgroundColor,
        iconColor = iconColor,
        textColor = textColor,
        shape = CircleShape,
        borderWidth = borderWidth,
        borderColor = borderColor,
        iconSize = size * 0.4f,
        textSize = (size.value / 5).sp
    )
}

/**
 * 占位符类型枚举
 */
enum class PlaceholderType {
    LOADING,    // 加载中
    ERROR,      // 错误
    EMPTY,      // 空状态
    CUSTOM      // 自定义
}

/**
 * 占位符尺寸预设
 */
object PlaceholderSize {
    val Small = 64.dp
    val Medium = 120.dp
    val Large = 200.dp
    val ExtraLarge = 300.dp
}
