package com.yjsoft.roadtravel.basiclibrary.payment.config

/**
 * 支付框架常量定义
 * 包含支付相关的常量值、错误码等
 */
object PaymentConstants {
    
    /**
     * 支付结果码
     */
    object ResultCode {
        // 通用结果码
        const val SUCCESS = "SUCCESS"
        const val CANCEL = "CANCEL"
        const val FAILED = "FAILED"
        const val UNKNOWN = "UNKNOWN"
        const val TIMEOUT = "TIMEOUT"
        const val NETWORK_ERROR = "NETWORK_ERROR"
        
        // 支付宝结果码
        object Alipay {
            const val SUCCESS = "9000"
            const val PROCESSING = "8000"
            const val CANCEL = "6001"
            const val NETWORK_ERROR = "6002"
            const val UNKNOWN = "6004"
        }
        
        // 微信支付结果码
        object WeChat {
            const val SUCCESS = "0"
            const val CANCEL = "-2"
            const val ERROR = "-1"
        }
        
        // 云闪付结果码
        object UnionPay {
            const val SUCCESS = "00"
            const val CANCEL = "01"
            const val FAILED = "02"
            const val PROCESSING = "03"
        }
    }
    
    /**
     * 支付参数键名
     */
    object ParamKey {
        // 通用参数
        const val ORDER_ID = "order_id"
        const val AMOUNT = "amount"
        const val TITLE = "title"
        const val DESCRIPTION = "description"
        const val NOTIFY_URL = "notify_url"
        const val RETURN_URL = "return_url"
        const val TIMESTAMP = "timestamp"
        const val SIGN = "sign"
        
        // 支付宝参数
        object Alipay {
            const val APP_ID = "app_id"
            const val METHOD = "method"
            const val CHARSET = "charset"
            const val SIGN_TYPE = "sign_type"
            const val VERSION = "version"
            const val BIZ_CONTENT = "biz_content"
        }
        
        // 微信支付参数
        object WeChat {
            const val APP_ID = "appid"
            const val PARTNER_ID = "partnerid"
            const val PREPAY_ID = "prepayid"
            const val PACKAGE = "package"
            const val NONCE_STR = "noncestr"
            const val TIMESTAMP = "timestamp"
            const val SIGN = "sign"
        }
        
        // 云闪付参数
        object UnionPay {
            const val TN = "tn"
            const val MODE = "mode"
        }
    }
    
    /**
     * 支付超时配置
     */
    object Timeout {
        const val DEFAULT_TIMEOUT_MS = 30_000L
        const val NETWORK_TIMEOUT_MS = 15_000L
        const val SDK_TIMEOUT_MS = 60_000L
    }
    
    /**
     * 支付安全配置
     */
    object Security {
        const val SIGN_TYPE_RSA2 = "RSA2"
        const val SIGN_TYPE_RSA = "RSA"
        const val SIGN_TYPE_MD5 = "MD5"
        const val CHARSET_UTF8 = "UTF-8"
    }
    
    /**
     * 支付SDK版本
     */
    object SdkVersion {
        const val ALIPAY_SDK = "15.8.18"
        const val WECHAT_SDK = "6.8.26"
        const val UNIONPAY_SDK = "3.5.12"
    }
    
    /**
     * 支付环境配置
     */
    object Environment {
        // 支付宝环境
        const val ALIPAY_SANDBOX = "https://openapi.alipaydev.com/gateway.do"
        const val ALIPAY_PRODUCTION = "https://openapi.alipay.com/gateway.do"
        
        // 微信支付环境
        const val WECHAT_SANDBOX = "https://api.mch.weixin.qq.com/sandboxnew/"
        const val WECHAT_PRODUCTION = "https://api.mch.weixin.qq.com/"
        
        // 云闪付环境
        const val UNIONPAY_SANDBOX = "01"
        const val UNIONPAY_PRODUCTION = "00"
    }
}
