package com.yjsoft.roadtravel.basiclibrary.datastore.model

import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey

/**
 * 类型安全的偏好设置键
 * 提供类型安全的数据存储键定义
 */
data class PreferenceKey<T>(
    val key: Preferences.Key<T>,
    val name: String,
    val defaultValue: T,
    val description: String = ""
) {
    companion object {
        /**
         * 创建字符串类型的键
         */
        fun string(
            name: String,
            defaultValue: String = "",
            description: String = ""
        ): PreferenceKey<String> {
            return PreferenceKey(
                key = stringPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建整数类型的键
         */
        fun int(
            name: String,
            defaultValue: Int = 0,
            description: String = ""
        ): PreferenceKey<Int> {
            return PreferenceKey(
                key = intPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建长整数类型的键
         */
        fun long(
            name: String,
            defaultValue: Long = 0L,
            description: String = ""
        ): PreferenceKey<Long> {
            return PreferenceKey(
                key = longPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建浮点数类型的键
         */
        fun float(
            name: String,
            defaultValue: Float = 0f,
            description: String = ""
        ): PreferenceKey<Float> {
            return PreferenceKey(
                key = floatPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建双精度浮点数类型的键
         */
        fun double(
            name: String,
            defaultValue: Double = 0.0,
            description: String = ""
        ): PreferenceKey<Double> {
            return PreferenceKey(
                key = doublePreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建布尔类型的键
         */
        fun boolean(
            name: String,
            defaultValue: Boolean = false,
            description: String = ""
        ): PreferenceKey<Boolean> {
            return PreferenceKey(
                key = booleanPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
        
        /**
         * 创建字符串集合类型的键
         */
        fun stringSet(
            name: String,
            defaultValue: Set<String> = emptySet(),
            description: String = ""
        ): PreferenceKey<Set<String>> {
            return PreferenceKey(
                key = stringSetPreferencesKey(name),
                name = name,
                defaultValue = defaultValue,
                description = description
            )
        }
    }
    
    override fun toString(): String {
        return "PreferenceKey(name='$name', defaultValue=$defaultValue, description='$description')"
    }
}

/**
 * 预定义的常用偏好设置键
 * 包含应用中常用的配置项
 */
object CommonPreferenceKeys {
    
    // 用户相关
    val USER_ID = PreferenceKey.string(
        name = "user_id",
        defaultValue = "",
        description = "用户ID"
    )
    
    val USER_NAME = PreferenceKey.string(
        name = "user_name",
        defaultValue = "",
        description = "用户名"
    )
    
    val USER_EMAIL = PreferenceKey.string(
        name = "user_email",
        defaultValue = "",
        description = "用户邮箱"
    )
    
    val IS_LOGGED_IN = PreferenceKey.boolean(
        name = "is_logged_in",
        defaultValue = false,
        description = "是否已登录"
    )
    
    // 应用设置
    val IS_FIRST_LAUNCH = PreferenceKey.boolean(
        name = "is_first_launch",
        defaultValue = true,
        description = "是否首次启动"
    )
    
    val APP_VERSION = PreferenceKey.string(
        name = "app_version",
        defaultValue = "",
        description = "应用版本"
    )
    
    val LAST_UPDATE_TIME = PreferenceKey.long(
        name = "last_update_time",
        defaultValue = 0L,
        description = "最后更新时间"
    )
    
    // 主题和UI设置
    val THEME_MODE = PreferenceKey.string(
        name = "theme_mode",
        defaultValue = "system",
        description = "主题模式：light/dark/system"
    )
    
    val LANGUAGE = PreferenceKey.string(
        name = "language",
        defaultValue = "system",
        description = "语言设置"
    )
    
    // 定位相关
    val LAST_KNOWN_CITY = PreferenceKey.string(
        name = "last_known_city",
        defaultValue = "上海市",
        description = "最后已知城市"
    )
    
    val LOCATION_PERMISSION_GRANTED = PreferenceKey.boolean(
        name = "location_permission_granted",
        defaultValue = false,
        description = "定位权限是否已授予"
    )
    
    val AUTO_LOCATION_ENABLED = PreferenceKey.boolean(
        name = "auto_location_enabled",
        defaultValue = true,
        description = "是否启用自动定位"
    )
    
    // 网络和缓存设置
    val CACHE_SIZE_MB = PreferenceKey.int(
        name = "cache_size_mb",
        defaultValue = 100,
        description = "缓存大小（MB）"
    )
    
    val NETWORK_TIMEOUT_SECONDS = PreferenceKey.int(
        name = "network_timeout_seconds",
        defaultValue = 30,
        description = "网络超时时间（秒）"
    )
    
    // 调试和日志设置
    val DEBUG_MODE_ENABLED = PreferenceKey.boolean(
        name = "debug_mode_enabled",
        defaultValue = false,
        description = "是否启用调试模式"
    )
    
    val LOG_LEVEL = PreferenceKey.string(
        name = "log_level",
        defaultValue = "INFO",
        description = "日志级别"
    )
    
    // 用户偏好
    val FAVORITE_CITIES = PreferenceKey.stringSet(
        name = "favorite_cities",
        defaultValue = emptySet(),
        description = "收藏的城市列表"
    )
    
    val NOTIFICATION_ENABLED = PreferenceKey.boolean(
        name = "notification_enabled",
        defaultValue = true,
        description = "是否启用通知"
    )
    
    val AUTO_BACKUP_ENABLED = PreferenceKey.boolean(
        name = "auto_backup_enabled",
        defaultValue = false,
        description = "是否启用自动备份"
    )
    
    // 初始化数据相关
    val INIT_ZONE_ID = PreferenceKey.int(
        name = "init_zone_id",
        defaultValue = 7,
        description = "初始化区域ID"
    )
    
    val INIT_ZONE_NAME = PreferenceKey.string(
        name = "init_zone_name",
        defaultValue = "上海市",
        description = "初始化区域名称"
    )
    
    val INIT_LAT = PreferenceKey.double(
        name = "init_lat",
        defaultValue = 31.231706,
        description = "初始化纬度"
    )
    
    val INIT_LNG = PreferenceKey.double(
        name = "init_lng",
        defaultValue = 121.472644,
        description = "初始化经度"
    )
    
    val INIT_SERVICE_WX = PreferenceKey.string(
        name = "init_service_wx",
        defaultValue = "",
        description = "客服微信"
    )
    
    val INIT_SERVICE_EMAIL = PreferenceKey.string(
        name = "init_service_email",
        defaultValue = "",
        description = "客服邮箱"
    )
    
    val INIT_SHARE_TASK_ID = PreferenceKey.int(
        name = "init_share_task_id",
        defaultValue = 0,
        description = "分享任务ID"
    )
    
    val INIT_SHARE_TASK_DESC = PreferenceKey.string(
        name = "init_share_task_desc",
        defaultValue = "",
        description = "分享任务描述"
    )
    
    val INIT_SHARE_TASK_VALUE = PreferenceKey.int(
        name = "init_share_task_value",
        defaultValue = 0,
        description = "分享任务积分值"
    )
    
    val INIT_PLAN_COST_AMOUNT = PreferenceKey.int(
        name = "init_plan_cost_amount",
        defaultValue = 100,
        description = "规划费用金额"
    )
    
    val INIT_BIND_INVITE_GIVE = PreferenceKey.int(
        name = "init_bind_invite_give",
        defaultValue = 10,
        description = "绑定邀请奖励"
    )
    
    val INIT_PLAN_PDF_COST_AMOUNT = PreferenceKey.int(
        name = "init_plan_pdf_cost_amount",
        defaultValue = 100,
        description = "规划PDF费用金额"
    )
    
    val INIT_PACKAGE_BUY_SERVICE = PreferenceKey.string(
        name = "init_package_buy_service",
        defaultValue = "",
        description = "套餐购买服务链接"
    )
    
    val INIT_AD_TYPE = PreferenceKey.string(
        name = "init_ad_type",
        defaultValue = "",
        description = "广告类型"
    )
    
    val INIT_AD_ID = PreferenceKey.string(
        name = "init_ad_id",
        defaultValue = "",
        description = "广告ID"
    )
    
    val INIT_AD_AMOUNT = PreferenceKey.int(
        name = "init_ad_amount",
        defaultValue = 0,
        description = "广告积分金额"
    )
    
    // 地理信息数据相关
    val GEO_ADDRESS = PreferenceKey.string(
        name = "geo_address",
        defaultValue = "",
        description = "详细地址"
    )
    
    val GEO_CITY = PreferenceKey.string(
        name = "geo_city",
        defaultValue = "",
        description = "城市名称"
    )
    
    val GEO_ZONE_ID = PreferenceKey.int(
        name = "geo_zone_id",
        defaultValue = 0,
        description = "地理区域ID"
    )

    val CONVERSION_ID = PreferenceKey.string(
        name = "conversion_id",
        defaultValue = "",
        description = "最近一次的对话Id"
    )
    
    // 微信用户信息相关
    val WECHAT_OPEN_ID = PreferenceKey.string(
        name = "wechat_open_id",
        defaultValue = "",
        description = "微信OpenID"
    )
    
    val WECHAT_UNION_ID = PreferenceKey.string(
        name = "wechat_union_id",
        defaultValue = "",
        description = "微信UnionID"
    )
    
    val WECHAT_NICKNAME = PreferenceKey.string(
        name = "wechat_nickname",
        defaultValue = "",
        description = "微信昵称"
    )
    
    val WECHAT_AVATAR = PreferenceKey.string(
        name = "wechat_avatar",
        defaultValue = "",
        description = "微信头像URL"
    )
    
    val WECHAT_SEX = PreferenceKey.string(
        name = "wechat_sex",
        defaultValue = "未知",
        description = "微信性别"
    )
    
    val WECHAT_PROVINCE = PreferenceKey.string(
        name = "wechat_province",
        defaultValue = "",
        description = "微信省份"
    )
    
    val WECHAT_CITY = PreferenceKey.string(
        name = "wechat_city",
        defaultValue = "",
        description = "微信城市"
    )
    
    val WECHAT_COUNTRY = PreferenceKey.string(
        name = "wechat_country",
        defaultValue = "",
        description = "微信国家"
    )
    
    val WECHAT_ACCESS_TOKEN = PreferenceKey.string(
        name = "wechat_access_token",
        defaultValue = "",
        description = "微信访问令牌"
    )
    
    val WECHAT_REFRESH_TOKEN = PreferenceKey.string(
        name = "wechat_refresh_token",
        defaultValue = "",
        description = "微信刷新令牌"
    )
    
    val WECHAT_TOKEN_EXPIRES_TIME = PreferenceKey.long(
        name = "wechat_token_expires_time",
        defaultValue = 0L,
        description = "微信令牌过期时间戳"
    )
    
    val WECHAT_LOGIN_TIME = PreferenceKey.long(
        name = "wechat_login_time",
        defaultValue = 0L,
        description = "微信登录时间戳"
    )
}
