package com.yjsoft.roadtravel.basiclibrary.map.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Map
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.amap.api.maps.MapView
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.LatLng
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.*

/**
 * 简化的地图组件，集成高德地图
 */
@Composable
fun SimpleAMapView(
    modifier: Modifier = Modifier,
    latitude: Double,
    longitude: Double,
    zoom: Float = 15f,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    // 地图状态
    var mapView by remember { mutableStateOf<MapView?>(null) }
    var aMap by remember { mutableStateOf<AMap?>(null) }
    Box(modifier = modifier) {
        // 显示真实地图
        AndroidView(
            factory = { ctx ->
                MapView(ctx).apply {
                    onCreate(null)
                    mapView = this
                    // 直接获取地图对象
                    val map = this.map
                    aMap = map
                    // 配置地图
                    setupSimpleMap(map, latitude, longitude, zoom)
                }
            },
            modifier = Modifier
                .fillMaxSize(),
            update = { view ->
                // 更新地图位置
                aMap?.let { map ->
                    val latLng = LatLng(latitude, longitude)
                    val cameraUpdate = CameraUpdateFactory.newLatLngZoom(latLng, zoom)
                    map.animateCamera(cameraUpdate)
                }
            }
        )
    }

    // 处理生命周期
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            mapView?.let { view ->
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        LogManager.d("SimpleAMapView", "地图恢复")
                        view.onResume()
                    }

                    Lifecycle.Event.ON_PAUSE -> {
                        LogManager.d("SimpleAMapView", "地图暂停")
                        view.onPause()
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        LogManager.d("SimpleAMapView", "地图销毁")
                        view.onDestroy()
                    }

                    else -> {}
                }
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            mapView?.onDestroy()
        }
    }
}

/**
 * 配置简单地图设置
 */
private fun setupSimpleMap(
    aMap: AMap,
    latitude: Double,
    longitude: Double,
    zoom: Float
) {
    try {
        // 设置地图类型为普通地图
        aMap.mapType = AMap.MAP_TYPE_NORMAL

        // 设置UI控件
        aMap.uiSettings.isZoomControlsEnabled = false
        aMap.uiSettings.isRotateGesturesEnabled = true
        aMap.uiSettings.isScaleControlsEnabled = false
        aMap.uiSettings.isCompassEnabled = false

        // 禁用我的位置显示（简化版本）
        aMap.isMyLocationEnabled = false

        // 移动到指定位置
        val latLng = LatLng(latitude, longitude)
        aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, zoom))

        LogManager.d("SimpleAMapView", "地图配置完成: ($latitude, $longitude), zoom: $zoom")
    } catch (e: Exception) {
        LogManager.e("SimpleAMapView", "地图配置失败", e)
    }
}



