package com.yjsoft.roadtravel.basiclibrary.navigation.models

import kotlinx.serialization.Serializable

/**
 * 导航路由数据模型
 * 使用密封类定义所有可能的导航目标
 */
@Serializable
sealed class NavigationRoute {
    
    /**
     * 主页面
     */
    @Serializable
    data object Main : NavigationRoute()
    
    /**
     * 支付演示页面
     */
    @Serializable
    data object PaymentDemo : NavigationRoute()
    
    /**
     * WebView页面
     * @param url 要加载的URL
     * @param title 页面标题，可选
     */
    @Serializable
    data class WebView(
        val url: String,
        val title: String? = null
    ) : NavigationRoute()
    
    /**
     * 带参数的路由基类
     */
    @Serializable
    abstract class ParameterizedRoute : NavigationRoute() {
        abstract fun buildRoute(): String
    }
    
    /**
     * 自定义页面路由
     * @param route 路由字符串
     * @param params 附加参数
     */
    @Serializable
    data class Custom(
        val route: String,
        val params: Map<String, String> = emptyMap()
    ) : ParameterizedRoute() {
        override fun buildRoute(): String {
            return if (params.isEmpty()) {
                route
            } else {
                val queryString = params.entries.joinToString("&") { "${it.key}=${it.value}" }
                "$route?$queryString"
            }
        }
    }
}

/**
 * 路由扩展函数
 */
fun NavigationRoute.toRouteString(): String {
    return when (this) {
        is NavigationRoute.Main -> "main"
        is NavigationRoute.PaymentDemo -> "payment_demo"
        is NavigationRoute.WebView -> "webview/${url}${title?.let { "/$it" } ?: ""}"
        is NavigationRoute.ParameterizedRoute -> buildRoute()
    }
} 