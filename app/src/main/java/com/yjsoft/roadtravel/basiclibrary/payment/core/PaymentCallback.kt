package com.yjsoft.roadtravel.basiclibrary.payment.core

import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType

/**
 * 支付回调接口
 * 用于处理支付过程中的各种回调事件
 */
interface PaymentCallback {
    
    /**
     * 支付开始
     * @param orderId 订单ID
     * @param paymentType 支付类型
     */
    fun onPaymentStart(orderId: String, paymentType: PaymentType) {}
    
    /**
     * 支付成功
     * @param result 支付结果
     */
    fun onPaymentSuccess(result: PaymentResult.Success)
    
    /**
     * 支付取消
     * @param result 取消结果
     */
    fun onPaymentCancel(result: PaymentResult.Cancel)
    
    /**
     * 支付失败
     * @param result 错误结果
     */
    fun onPaymentError(result: PaymentResult.Error)
    
    /**
     * 支付处理中
     * @param result 处理中结果
     */
    fun onPaymentProcessing(result: PaymentResult.Processing) {}
}

/**
 * 简化的支付回调接口
 * 只关心最终结果的回调
 */
fun interface SimplePaymentCallback {
    /**
     * 支付完成回调
     * @param result 支付结果
     */
    fun onPaymentComplete(result: PaymentResult)
}

/**
 * 支付回调适配器
 * 提供默认实现，子类只需重写感兴趣的方法
 */
abstract class PaymentCallbackAdapter : PaymentCallback {
    
    override fun onPaymentSuccess(result: PaymentResult.Success) {
        // 默认实现，子类可重写
    }
    
    override fun onPaymentCancel(result: PaymentResult.Cancel) {
        // 默认实现，子类可重写
    }
    
    override fun onPaymentError(result: PaymentResult.Error) {
        // 默认实现，子类可重写
    }
}

/**
 * 支付回调构建器
 * 使用DSL风格构建支付回调
 */
class PaymentCallbackBuilder {
    private var onStart: ((String, PaymentType) -> Unit)? = null
    private var onSuccess: ((PaymentResult.Success) -> Unit)? = null
    private var onCancel: ((PaymentResult.Cancel) -> Unit)? = null
    private var onError: ((PaymentResult.Error) -> Unit)? = null
    private var onProcessing: ((PaymentResult.Processing) -> Unit)? = null
    
    /**
     * 设置支付开始回调
     */
    fun onStart(callback: (orderId: String, paymentType: PaymentType) -> Unit) {
        this.onStart = callback
    }
    
    /**
     * 设置支付成功回调
     */
    fun onSuccess(callback: (result: PaymentResult.Success) -> Unit) {
        this.onSuccess = callback
    }
    
    /**
     * 设置支付取消回调
     */
    fun onCancel(callback: (result: PaymentResult.Cancel) -> Unit) {
        this.onCancel = callback
    }
    
    /**
     * 设置支付失败回调
     */
    fun onError(callback: (result: PaymentResult.Error) -> Unit) {
        this.onError = callback
    }
    
    /**
     * 设置支付处理中回调
     */
    fun onProcessing(callback: (result: PaymentResult.Processing) -> Unit) {
        this.onProcessing = callback
    }
    
    /**
     * 构建支付回调
     */
    fun build(): PaymentCallback {
        return object : PaymentCallback {
            override fun onPaymentStart(orderId: String, paymentType: PaymentType) {
                onStart?.invoke(orderId, paymentType)
            }
            
            override fun onPaymentSuccess(result: PaymentResult.Success) {
                onSuccess?.invoke(result)
            }
            
            override fun onPaymentCancel(result: PaymentResult.Cancel) {
                onCancel?.invoke(result)
            }
            
            override fun onPaymentError(result: PaymentResult.Error) {
                onError?.invoke(result)
            }
            
            override fun onPaymentProcessing(result: PaymentResult.Processing) {
                onProcessing?.invoke(result)
            }
        }
    }
}

/**
 * DSL函数，用于构建支付回调
 */
fun paymentCallback(builder: PaymentCallbackBuilder.() -> Unit): PaymentCallback {
    return PaymentCallbackBuilder().apply(builder).build()
}
