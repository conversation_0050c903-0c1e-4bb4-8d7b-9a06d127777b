package com.yjsoft.roadtravel.basiclibrary.payment.models

/**
 * 支付结果密封类
 * 表示支付操作的各种可能结果
 */
sealed class PaymentResult {
    
    /**
     * 支付成功
     * @param orderId 订单ID
     * @param transactionId 交易流水号
     * @param amount 支付金额
     * @param paymentType 支付方式
     * @param timestamp 支付完成时间戳
     * @param extraData 额外数据
     */
    data class Success(
        val orderId: String,
        val transactionId: String,
        val amount: String,
        val paymentType: PaymentType,
        val timestamp: Long = System.currentTimeMillis(),
        val extraData: Map<String, String> = emptyMap()
    ) : PaymentResult() {
        
        /**
         * 获取格式化的支付时间
         */
        fun getFormattedTime(): String {
            return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date(timestamp))
        }
    }
    
    /**
     * 用户取消支付
     * @param orderId 订单ID
     * @param paymentType 支付方式
     * @param reason 取消原因
     */
    data class Cancel(
        val orderId: String,
        val paymentType: PaymentType,
        val reason: String? = null
    ) : PaymentResult()
    
    /**
     * 支付失败
     * @param orderId 订单ID
     * @param paymentType 支付方式
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param cause 异常原因
     */
    data class Error(
        val orderId: String,
        val paymentType: PaymentType,
        val errorCode: String,
        val errorMessage: String,
        val cause: Throwable? = null
    ) : PaymentResult() {
        
        /**
         * 是否为网络错误
         */
        fun isNetworkError(): Boolean {
            return errorCode.contains("NETWORK", ignoreCase = true) ||
                   errorMessage.contains("网络", ignoreCase = true) ||
                   errorMessage.contains("network", ignoreCase = true)
        }
        
        /**
         * 是否为超时错误
         */
        fun isTimeoutError(): Boolean {
            return errorCode.contains("TIMEOUT", ignoreCase = true) ||
                   errorMessage.contains("超时", ignoreCase = true) ||
                   errorMessage.contains("timeout", ignoreCase = true)
        }
        
        /**
         * 获取用户友好的错误信息
         */
        fun getUserFriendlyMessage(): String {
            return when {
                isNetworkError() -> "网络连接异常，请检查网络后重试"
                isTimeoutError() -> "支付超时，请重新尝试"
                errorMessage.isNotBlank() -> errorMessage
                else -> "支付失败，请稍后重试"
            }
        }
    }
    
    /**
     * 支付处理中（某些支付方式可能需要异步处理）
     * @param orderId 订单ID
     * @param paymentType 支付方式
     * @param message 处理信息
     */
    data class Processing(
        val orderId: String,
        val paymentType: PaymentType,
        val message: String = "支付处理中，请稍候..."
    ) : PaymentResult()
    
    companion object {
        /**
         * 创建成功结果
         */
        fun success(
            orderId: String,
            transactionId: String,
            amount: String,
            paymentType: PaymentType,
            extraData: Map<String, String> = emptyMap()
        ): Success {
            return Success(
                orderId = orderId,
                transactionId = transactionId,
                amount = amount,
                paymentType = paymentType,
                extraData = extraData
            )
        }
        
        /**
         * 创建取消结果
         */
        fun cancel(
            orderId: String,
            paymentType: PaymentType,
            reason: String? = null
        ): Cancel {
            return Cancel(
                orderId = orderId,
                paymentType = paymentType,
                reason = reason
            )
        }
        
        /**
         * 创建错误结果
         */
        fun error(
            orderId: String,
            paymentType: PaymentType,
            errorCode: String,
            errorMessage: String,
            cause: Throwable? = null
        ): Error {
            return Error(
                orderId = orderId,
                paymentType = paymentType,
                errorCode = errorCode,
                errorMessage = errorMessage,
                cause = cause
            )
        }
        
        /**
         * 创建处理中结果
         */
        fun processing(
            orderId: String,
            paymentType: PaymentType,
            message: String = "支付处理中，请稍候..."
        ): Processing {
            return Processing(
                orderId = orderId,
                paymentType = paymentType,
                message = message
            )
        }
    }
}
