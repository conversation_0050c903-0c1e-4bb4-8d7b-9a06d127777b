package com.yjsoft.roadtravel.basiclibrary.speech.core

import android.Manifest
import android.content.Context
import android.media.AudioRecord
import androidx.annotation.RequiresPermission
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.baidu.speech.EventListener
import com.baidu.speech.EventManager
import com.baidu.speech.EventManagerFactory
import com.baidu.speech.asr.SpeechConstant
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.speech.config.SpeechConfig
import com.yjsoft.roadtravel.basiclibrary.speech.models.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 百度语音识别管理器
 * 提供完整的语音识别功能，包括录音、识别、状态管理等
 *
 * 注意：使用弱引用持有Context以避免内存泄漏
 */
class SpeechRecognitionManager private constructor() : DefaultLifecycleObserver, EventListener {

    companion object {
        private const val TAG = "SpeechRecognitionManager"

        @Volatile
        private var INSTANCE: SpeechRecognitionManager? = null

        /**
         * 获取语音识别管理器实例
         * 使用ApplicationContext避免内存泄漏
         */
        fun getInstance(context: Context): SpeechRecognitionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SpeechRecognitionManager().also {
                    INSTANCE = it
                    // 初始化时设置Context
                    it.setContext(context.applicationContext)
                }
            }
        }

        /**
         * 清除实例（用于测试或特殊情况）
         */
        @JvmStatic
        fun clearInstance() {
            synchronized(this) {
                INSTANCE?.release()
                INSTANCE = null
            }
        }
    }

    // 使用弱引用持有ApplicationContext，避免内存泄漏
    private var contextRef: WeakReference<Context>? = null

    /**
     * 设置Context（仅在初始化时调用）
     */
    private fun setContext(context: Context) {
        contextRef = WeakReference(context)
    }

    // 协程作用域
    private val managerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 百度语音识别事件管理器
    private var eventManager: EventManager? = null

    // 录音相关
    private var audioRecord: AudioRecord? = null
    private var recordingJob: Job? = null
    private val isRecording = AtomicBoolean(false)
    private var tempAudioFile: File? = null

    // 状态管理
    private val _state = MutableStateFlow(SpeechRecognitionState.IDLE)
    val state: StateFlow<SpeechRecognitionState> = _state.asStateFlow()

    private val _result = MutableStateFlow(SpeechRecognitionResult())
    val result: StateFlow<SpeechRecognitionResult> = _result.asStateFlow()

    private val _audioState = MutableStateFlow(AudioRecordingState())
    val audioState: StateFlow<AudioRecordingState> = _audioState.asStateFlow()

    private val _error = MutableSharedFlow<SpeechRecognitionError>()
    val error: SharedFlow<SpeechRecognitionError> = _error.asSharedFlow()

    // 回调列表
    private val callbacks = mutableSetOf<SpeechRecognitionCallback>()

    // 初始化状态
    private val isInitialized = AtomicBoolean(false)

    /**
     * 初始化语音识别SDK
     */
    suspend fun initialize(): Boolean = suspendCoroutine { continuation ->
        if (isInitialized.get()) {
            continuation.resume(true)
            return@suspendCoroutine
        }

        try {
            LogManager.d(TAG, "开始初始化百度语音识别SDK")
            _state.value = SpeechRecognitionState.INITIALIZING

            // 获取Context
            val context = contextRef?.get() ?: run {
                LogManager.e(TAG, "Context已被回收，无法初始化")
                continuation.resume(false)
                return@suspendCoroutine
            }

            // 创建事件管理器
            eventManager = EventManagerFactory.create(context, "asr")
            eventManager?.registerListener(this)

            // 发送初始化参数
            val initParams = createInitParams()
            eventManager?.send("asr.init", initParams, null, 0, 0)

            // 模拟初始化完成（实际应该在onEvent中处理）
            managerScope.launch {
                delay(1000) // 等待初始化完成
                isInitialized.set(true)
                _state.value = SpeechRecognitionState.IDLE
                LogManager.d(TAG, "百度语音识别SDK初始化完成")
                continuation.resume(true)
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "初始化百度语音识别SDK失败", e)
            _state.value = SpeechRecognitionState.ERROR
            val error = SpeechRecognitionError(
                code = SpeechConfig.ErrorCodes.ERROR_SDK_NOT_INITIALIZED,
                message = "SDK初始化失败: ${e.message}",
                cause = e
            )
            managerScope.launch {
                _error.emit(error)
            }
            continuation.resume(false)
        }
    }

    /**
     * 开始语音识别
     */
    fun startRecognition(params: SpeechRecognitionParams = SpeechRecognitionParams()): Boolean {
        if (!isInitialized.get()) {
            LogManager.w(TAG, "SDK未初始化，无法开始识别")
            return false
        }

        if (_state.value != SpeechRecognitionState.IDLE) {
            LogManager.w(TAG, "当前状态不允许开始识别: ${_state.value}")
            return false
        }

        try {
            LogManager.d(TAG, "开始语音识别")
            _state.value = SpeechRecognitionState.LISTENING

            // 重置结果
            _result.value = SpeechRecognitionResult()
            _audioState.value = AudioRecordingState()

            // 创建识别参数
            val recognitionParams = createRecognitionParams(params)

            // 发送开始识别命令
            eventManager?.send("asr.start", recognitionParams, null, 0, 0)

            // 通知回调
            callbacks.forEach { it.onStateChanged(_state.value) }

            return true

        } catch (e: Exception) {
            LogManager.e(TAG, "开始语音识别失败", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "开始识别失败: ${e.message}",
                e
            )
            return false
        }
    }

    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        try {
            LogManager.d(TAG, "停止语音识别")

            // 停止录音
            stopRecording()

            // 发送停止识别命令
            eventManager?.send("asr.stop", null, null, 0, 0)

            _state.value = SpeechRecognitionState.PROCESSING
            callbacks.forEach { it.onStateChanged(_state.value) }

        } catch (e: Exception) {
            LogManager.e(TAG, "停止语音识别失败", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "停止识别失败: ${e.message}",
                e
            )
        }
    }

    /**
     * 取消语音识别
     */
    fun cancelRecognition() {
        try {
            LogManager.d(TAG, "取消语音识别")

            // 停止录音
            stopRecording()

            // 发送取消识别命令
            eventManager?.send("asr.cancel", null, null, 0, 0)

            _state.value = SpeechRecognitionState.IDLE
            callbacks.forEach { it.onStateChanged(_state.value) }

        } catch (e: Exception) {
            LogManager.e(TAG, "取消语音识别失败", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "取消识别失败: ${e.message}",
                e
            )
        }
    }

    /**
     * 添加回调监听器
     */
    fun addCallback(callback: SpeechRecognitionCallback) {
        callbacks.add(callback)
    }

    /**
     * 移除回调监听器
     */
    fun removeCallback(callback: SpeechRecognitionCallback) {
        callbacks.remove(callback)
    }

    /**
     * 清除所有回调监听器
     */
    fun clearCallbacks() {
        callbacks.clear()
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            LogManager.d(TAG, "释放语音识别资源")

            // 停止录音
            stopRecording()

            // 取消协程
            managerScope.cancel()

            // 释放事件管理器
            eventManager?.send("asr.release", null, null, 0, 0)
            eventManager?.unregisterListener(this)
            eventManager = null

            // 清理临时文件
            cleanupTempFiles()

            // 重置状态
            isInitialized.set(false)
            _state.value = SpeechRecognitionState.IDLE

            // 清除回调
            callbacks.clear()

        } catch (e: Exception) {
            LogManager.e(TAG, "释放资源失败", e)
        }
    }

    // 生命周期回调
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        release()
    }

    /**
     * 百度语音识别事件监听器
     */
    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    override fun onEvent(
        name: String?,
        params: String?,
        data: ByteArray?,
        offset: Int,
        length: Int
    ) {
        try {
            LogManager.d(TAG, "收到事件: $name, 参数: $params")

            when (name) {
                "asr.ready" -> {
                    // SDK准备就绪，可以开始录音
                    _state.value = SpeechRecognitionState.RECORDING
                    startRecording()
                    callbacks.forEach { it.onStartRecording() }
                }

                "asr.begin" -> {
                    // 开始录音
                    LogManager.d(TAG, "开始录音")
                    callbacks.forEach { it.onStartRecording() }
                }

                "asr.end" -> {
                    // 录音结束
                    LogManager.d(TAG, "录音结束")
                    stopRecording()
                    _state.value = SpeechRecognitionState.PROCESSING
                    callbacks.forEach { it.onStopRecording() }
                }

                "asr.partial" -> {
                    // 部分识别结果
                    params?.let { parsePartialResult(it) }
                }

                "asr.finish" -> {
                    // 识别完成
                    params?.let { parseFinalResult(it) }
                }

                "asr.error" -> {
                    // 识别错误
                    params?.let { parseError(it) }
                }

                "asr.volume" -> {
                    // 音量变化
                    params?.let { parseVolume(it) }
                }
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "处理事件失败: $name", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "事件处理失败: ${e.message}",
                e
            )
        }
    }

    /**
     * 创建初始化参数
     */
    private fun createInitParams(): String {
        val params = JSONObject().apply {
            // 百度语音识别SDK认证参数
            put(SpeechConstant.APP_ID, SpeechConfig.BaiduSpeech.APP_ID)
            put(SpeechConstant.APP_KEY, SpeechConfig.BaiduSpeech.API_KEY)
            put(SpeechConstant.SECRET, SpeechConfig.BaiduSpeech.SECRET_KEY)
            // VAD配置
            put("vad", "touch")
        }
        return params.toString()
    }

    /**
     * 创建识别参数
     */
    private fun createRecognitionParams(params: SpeechRecognitionParams): String {
        val jsonParams = JSONObject().apply {
            // 认证参数
            put(SpeechConstant.APP_ID, SpeechConfig.BaiduSpeech.APP_ID)
            put(SpeechConstant.APP_KEY, SpeechConfig.BaiduSpeech.API_KEY)
            put(SpeechConstant.SECRET, SpeechConfig.BaiduSpeech.SECRET_KEY)

            // 识别参数
            put("accept-audio-data", false)
            put("pid", params.devPid)
            put("language", params.language)
            put("sample", params.sampleRate)
            put("channel", params.channel)
            put("token", "")  // 如果使用token认证
            put("cuid", SpeechConfig.BaiduSpeech.CUID)
            put("max-speech-s", params.maxSpeechTimeS)
            put("min-speech-s", params.minSpeechTimeS)

            // VAD配置 - 使用手动控制
            put("accept-audio-volume", false)
            put("vad", "touch")  // 正确的关闭VAD参数
            put("enable-vad", false)

            // 其他参数
            put("enable-punctuation", params.enablePunctuation)
            put("enable-number-convert", params.enableNumberConvert)
        }
        return jsonParams.toString()
    }

    /**
     * 开始录音
     */
    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    private fun startRecording() {
        if (isRecording.get()) {
            LogManager.w(TAG, "已经在录音中")
            return
        }

        try {
            // 创建临时音频文件
            createTempAudioFile()

            // 初始化AudioRecord
            val bufferSize = AudioRecord.getMinBufferSize(
                SpeechConfig.BaiduSpeech.SAMPLE_RATE,
                SpeechConfig.Recording.CHANNEL_CONFIG,
                SpeechConfig.Recording.AUDIO_FORMAT
            ) * SpeechConfig.Recording.BUFFER_SIZE_MULTIPLIER

            audioRecord = AudioRecord(
                SpeechConfig.Recording.AUDIO_SOURCE,
                SpeechConfig.BaiduSpeech.SAMPLE_RATE,
                SpeechConfig.Recording.CHANNEL_CONFIG,
                SpeechConfig.Recording.AUDIO_FORMAT,
                bufferSize
            )

            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                throw IllegalStateException("AudioRecord初始化失败")
            }

            // 开始录音
            audioRecord?.startRecording()
            isRecording.set(true)

            // 更新录音状态
            _audioState.value = _audioState.value.copy(
                isRecording = true,
                filePath = tempAudioFile?.absolutePath ?: ""
            )

            // 启动录音协程
            startRecordingCoroutine(bufferSize)

            LogManager.d(TAG, "开始录音成功")

        } catch (e: Exception) {
            LogManager.e(TAG, "开始录音失败", e)
            handleError(SpeechConfig.ErrorCodes.ERROR_AUDIO_RECORD, "录音失败: ${e.message}", e)
        }
    }

    /**
     * 停止录音
     */
    private fun stopRecording() {
        if (!isRecording.get()) {
            return
        }

        try {
            isRecording.set(false)
            recordingJob?.cancel()

            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            _audioState.value = _audioState.value.copy(isRecording = false)

            LogManager.d(TAG, "停止录音成功")

        } catch (e: Exception) {
            LogManager.e(TAG, "停止录音失败", e)
        }
    }

    /**
     * 启动录音协程
     */
    private fun startRecordingCoroutine(bufferSize: Int) {
        recordingJob = managerScope.launch(Dispatchers.IO) {
            val buffer = ByteArray(bufferSize)
            var totalBytes = 0L
            val startTime = System.currentTimeMillis()
            var fileOutputStream: FileOutputStream? = null

            try {
                tempAudioFile?.let { file ->
                    fileOutputStream = FileOutputStream(file)
                }

                while (isRecording.get() && isActive) {
                    val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0

                    if (bytesRead > 0) {
                        // 写入文件
                        fileOutputStream?.write(buffer, 0, bytesRead)
                        totalBytes += bytesRead

                        // 计算音量
                        val volume = calculateVolume(buffer, bytesRead)

                        // 更新状态
                        val currentTime = System.currentTimeMillis()
                        val duration = currentTime - startTime

                        withContext(Dispatchers.Main) {
                            _audioState.value = _audioState.value.copy(
                                duration = duration,
                                volume = volume
                            )

                            // 通知音量变化
                            callbacks.forEach { it.onVolumeChanged(volume) }
                        }

                        // 检查是否超过最大录音时长
                        if (duration >= SpeechConfig.BaiduSpeech.MAX_SPEECH_TIME_S * 1000) {
                            LogManager.d(TAG, "达到最大录音时长，自动停止")
                            stopRecognition()
                            break
                        }
                    }

                    delay(SpeechConfig.UI.VOLUME_UPDATE_INTERVAL_MS)
                }

            } catch (e: Exception) {
                // 区分正常停止和真正的异常
                when {
                    e is kotlinx.coroutines.CancellationException -> {
                        LogManager.d(TAG, "录音协程被正常取消（用户停止录音）")
                        // 正常取消，不需要报错
                    }
                    e.message?.contains("Broken pipe") == true -> {
                        LogManager.d(TAG, "录音连接中断（正常，用户停止录音）")
                        // 连接中断是正常的，不需要报错
                    }
                    else -> {
                        LogManager.e(TAG, "录音协程异常", e)
                        withContext(Dispatchers.Main) {
                            handleError(
                                SpeechConfig.ErrorCodes.ERROR_AUDIO_RECORD,
                                "录音异常: ${e.message}",
                                e
                            )
                        }
                    }
                }
            } finally {
                fileOutputStream?.close()
            }
        }
    }

    /**
     * 计算音量级别
     */
    private fun calculateVolume(buffer: ByteArray, length: Int): Float {
        var sum = 0.0
        for (i in 0 until length step 2) {
            if (i + 1 < length) {
                val sample = (buffer[i].toInt() and 0xFF) or (buffer[i + 1].toInt() shl 8)
                sum += sample * sample
            }
        }

        val rms = kotlin.math.sqrt(sum / (length / 2))
        val volume = (rms / 32768.0).coerceIn(0.0, 1.0)
        return volume.toFloat()
    }

    /**
     * 创建临时音频文件
     */
    private fun createTempAudioFile() {
        try {
            val context = contextRef?.get() ?: run {
                LogManager.e(TAG, "Context已被回收，无法创建临时文件")
                return
            }

            val tempDir = File(context.cacheDir, "speech_temp")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            tempAudioFile = File.createTempFile(
                SpeechConfig.Recording.TEMP_AUDIO_FILE_PREFIX,
                SpeechConfig.Recording.TEMP_AUDIO_FILE_SUFFIX,
                tempDir
            )

            LogManager.d(TAG, "创建临时音频文件: ${tempAudioFile?.absolutePath}")

        } catch (e: Exception) {
            LogManager.e(TAG, "创建临时音频文件失败", e)
        }
    }

    /**
     * 清理临时文件
     */
    private fun cleanupTempFiles() {
        try {
            tempAudioFile?.delete()
            tempAudioFile = null

            // 清理临时目录中的旧文件
            val context = contextRef?.get()
            if (context != null) {
                val tempDir = File(context.cacheDir, "speech_temp")
                if (tempDir.exists()) {
                    tempDir.listFiles()?.forEach { file ->
                        if (file.name.startsWith(SpeechConfig.Recording.TEMP_AUDIO_FILE_PREFIX)) {
                            file.delete()
                        }
                    }
                }
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "清理临时文件失败", e)
        }
    }

    /**
     * 解析部分识别结果
     */
    private fun parsePartialResult(params: String) {
        try {
            val json = JSONObject(params)
            val text = json.optString("best_result", "")
            val resultType = json.optString("result_type", "")

            if (text.isNotEmpty()) {
                LogManager.d(TAG, "部分识别结果: $text, 类型: $resultType")

                // 检查是否是最终结果
                if (resultType == "final_result") {
                    // 这是最终结果，创建结果对象
                    val confidence = json.optJSONObject("origin_result")
                        ?.optJSONArray("result")
                        ?.optJSONObject(0)
                        ?.optJSONArray("confident")
                        ?.optDouble(0, 0.0)?.toFloat() ?: 0.0f

                    val result = SpeechRecognitionResult(
                        text = text,
                        confidence = confidence,
                        isFinal = true,
                        duration = _audioState.value.duration
                    )

                    _result.value = result
                    _state.value = SpeechRecognitionState.SUCCESS

                    LogManager.d(TAG, "最终识别结果: $text, 置信度: $confidence")

                    callbacks.forEach {
                        it.onFinalResult(result)
                        it.onStateChanged(_state.value)
                    }
                } else {
                    // 这是部分结果
                    callbacks.forEach { it.onPartialResult(text) }
                }
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "解析部分结果失败", e)
        }
    }

    /**
     * 解析最终识别结果
     */
    private fun parseFinalResult(params: String) {
        try {
            val json = JSONObject(params)
            val resultsArray = json.optJSONArray("results_recognition")

            if (resultsArray != null && resultsArray.length() > 0) {
                val text = resultsArray.getString(0)
                val confidence = json.optDouble("confidence", 0.0).toFloat()

                val result = SpeechRecognitionResult(
                    text = text,
                    confidence = confidence,
                    isFinal = true,
                    duration = _audioState.value.duration
                )

                _result.value = result
                _state.value = SpeechRecognitionState.SUCCESS

                LogManager.d(TAG, "最终识别结果: $text, 置信度: $confidence")

                callbacks.forEach {
                    it.onFinalResult(result)
                    it.onStateChanged(_state.value)
                }

            } else {
                handleError(SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED, "识别结果为空")
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "解析最终结果失败", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "解析结果失败: ${e.message}",
                e
            )
        }
    }

    /**
     * 解析错误信息
     */
    private fun parseError(params: String) {
        try {
            val json = JSONObject(params)
            val errorCode = json.optInt("error", -1)
            val subErrorCode = json.optInt("sub_error", -1)
            val errorMessage = json.optString("desc", "未知错误")

            LogManager.e(TAG, "识别错误: $errorCode - $errorMessage (sub_error: $subErrorCode)")

            // 特殊处理VAD相关错误
            if (errorCode == 3 && subErrorCode == 3100) {
                LogManager.w(TAG, "VAD模型不可用，尝试使用手动模式重新启动识别")
                // 尝试使用禁用VAD的参数重新启动识别
                managerScope.launch {
                    delay(500) // 短暂延迟
                    try {
                        val params = SpeechRecognitionParams(enableVad = false)
                        val success = startRecognition(params)
                        if (!success) {
                            handleError(
                                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                                "语音识别启动失败，请重试"
                            )
                        }
                    } catch (e: Exception) {
                        LogManager.e(TAG, "重新启动识别失败", e)
                        handleError(
                            SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                            "语音识别失败，请重试"
                        )
                    }
                }
            } else {
                handleError(errorCode, errorMessage)
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "解析错误信息失败", e)
            handleError(
                SpeechConfig.ErrorCodes.ERROR_RECOGNITION_FAILED,
                "解析错误失败: ${e.message}",
                e
            )
        }
    }

    /**
     * 解析音量信息
     */
    private fun parseVolume(params: String) {
        try {
            val json = JSONObject(params)
            val volume = json.optDouble("volume", 0.0).toFloat()

            // 更新音量状态
            _audioState.value = _audioState.value.copy(volume = volume)
            callbacks.forEach { it.onVolumeChanged(volume) }

        } catch (e: Exception) {
            LogManager.e(TAG, "解析音量信息失败", e)
        }
    }

    /**
     * 处理错误
     */
    private fun handleError(code: Int, message: String, cause: Throwable? = null) {
        val error = SpeechRecognitionError(
            code = code,
            message = message,
            cause = cause
        )

        _state.value = SpeechRecognitionState.ERROR

        managerScope.launch {
            _error.emit(error)
        }

        callbacks.forEach {
            it.onError(error)
            it.onStateChanged(_state.value)
        }

        // 停止录音
        stopRecording()
    }

    /**
     * 重置状态
     */
    fun reset() {
        try {
            LogManager.d(TAG, "重置语音识别状态")

            // 停止录音
            stopRecording()

            // 重置状态
            _state.value = SpeechRecognitionState.IDLE
            _result.value = SpeechRecognitionResult()
            _audioState.value = AudioRecordingState()

            // 清理临时文件
            cleanupTempFiles()

            callbacks.forEach { it.onStateChanged(_state.value) }

        } catch (e: Exception) {
            LogManager.e(TAG, "重置状态失败", e)
        }
    }

    /**
     * 检查是否有录音权限
     */
    fun hasRecordPermission(): Boolean {
        return try {
            val context = contextRef?.get() ?: return false
            val permission = android.Manifest.permission.RECORD_AUDIO
            val result = androidx.core.content.ContextCompat.checkSelfPermission(
                context,
                permission
            )
            result == android.content.pm.PackageManager.PERMISSION_GRANTED
        } catch (e: Exception) {
            LogManager.e(TAG, "检查录音权限失败", e)
            false
        }
    }

    /**
     * 获取当前状态
     */
    fun getCurrentState(): SpeechRecognitionState = _state.value

    /**
     * 获取最新结果
     */
    fun getLatestResult(): SpeechRecognitionResult = _result.value

    /**
     * 获取录音状态
     */
    fun getAudioState(): AudioRecordingState = _audioState.value

    /**
     * 是否正在录音
     */
    fun isCurrentlyRecording(): Boolean = isRecording.get()

    /**
     * 是否已初始化
     */
    fun isSDKInitialized(): Boolean = isInitialized.get()
}
