package com.yjsoft.roadtravel.basiclibrary.network.cache

import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import okhttp3.CacheControl
import java.util.concurrent.TimeUnit

/**
 * 缓存策略枚举
 */
enum class CacheStrategy {
    /**
     * 不使用缓存
     */
    NO_CACHE,
    
    /**
     * 仅使用网络，不使用缓存
     */
    NETWORK_ONLY,
    
    /**
     * 仅使用缓存，不使用网络
     */
    CACHE_ONLY,
    
    /**
     * 优先使用缓存，缓存不存在时使用网络
     */
    CACHE_FIRST,
    
    /**
     * 优先使用网络，网络失败时使用缓存
     */
    NETWORK_FIRST,
    
    /**
     * 使用缓存，但会验证缓存是否过期
     */
    CACHE_WITH_VALIDATION,
    
    /**
     * 强制刷新缓存
     */
    FORCE_REFRESH
}

/**
 * 缓存策略管理器
 */
object CacheStrategyManager {
    
    /**
     * 根据缓存策略获取CacheControl
     */
    fun getCacheControl(strategy: CacheStrategy): CacheControl {
        return when (strategy) {
            CacheStrategy.NO_CACHE -> {
                CacheControl.Builder()
                    .noCache()
                    .noStore()
                    .build()
            }
            
            CacheStrategy.NETWORK_ONLY -> {
                CacheControl.Builder()
                    .noCache()
                    .build()
            }
            
            CacheStrategy.CACHE_ONLY -> {
                CacheControl.Builder()
                    .onlyIfCached()
                    .maxStale(Int.MAX_VALUE, TimeUnit.SECONDS)
                    .build()
            }
            
            CacheStrategy.CACHE_FIRST -> {
                CacheControl.Builder()
                    .maxStale(NetworkConfig.getCacheMaxStale(), TimeUnit.SECONDS)
                    .build()
            }
            
            CacheStrategy.NETWORK_FIRST -> {
                CacheControl.Builder()
                    .maxAge(0, TimeUnit.SECONDS)
                    .build()
            }
            
            CacheStrategy.CACHE_WITH_VALIDATION -> {
                CacheControl.Builder()
                    .maxAge(NetworkConfig.getCacheMaxAge(), TimeUnit.SECONDS)
                    .build()
            }
            
            CacheStrategy.FORCE_REFRESH -> {
                CacheControl.Builder()
                    .noCache()
                    .maxAge(0, TimeUnit.SECONDS)
                    .build()
            }
        }
    }
    
    /**
     * 获取默认缓存策略
     */
    fun getDefaultStrategy(): CacheStrategy {
        return if (NetworkConfig.isDebugMode()) {
            CacheStrategy.NETWORK_FIRST
        } else {
            CacheStrategy.CACHE_FIRST
        }
    }
    
    /**
     * 根据网络状态获取推荐的缓存策略
     */
    fun getRecommendedStrategy(isNetworkAvailable: Boolean): CacheStrategy {
        return if (isNetworkAvailable) {
            CacheStrategy.NETWORK_FIRST
        } else {
            CacheStrategy.CACHE_ONLY
        }
    }
    
    /**
     * 判断是否应该使用缓存
     */
    fun shouldUseCache(strategy: CacheStrategy): Boolean {
        return when (strategy) {
            CacheStrategy.NO_CACHE,
            CacheStrategy.NETWORK_ONLY -> false
            else -> true
        }
    }
    
    /**
     * 判断是否应该使用网络
     */
    fun shouldUseNetwork(strategy: CacheStrategy): Boolean {
        return when (strategy) {
            CacheStrategy.CACHE_ONLY -> false
            else -> true
        }
    }
}

/**
 * 缓存配置类
 */
data class CacheConfig(
    val strategy: CacheStrategy = CacheStrategyManager.getDefaultStrategy(),
    val maxAge: Int = NetworkConfig.getCacheMaxAge(),
    val maxStale: Int = NetworkConfig.getCacheMaxStale(),
    val customHeaders: Map<String, String> = emptyMap()
) {
    /**
     * 获取缓存控制头
     */
    fun toCacheControl(): CacheControl {
        return CacheStrategyManager.getCacheControl(strategy)
    }
    
    /**
     * 创建带有自定义最大存活时间的配置
     */
    fun withMaxAge(maxAge: Int): CacheConfig {
        return copy(maxAge = maxAge)
    }
    
    /**
     * 创建带有自定义最大过期时间的配置
     */
    fun withMaxStale(maxStale: Int): CacheConfig {
        return copy(maxStale = maxStale)
    }
    
    /**
     * 创建带有自定义策略的配置
     */
    fun withStrategy(strategy: CacheStrategy): CacheConfig {
        return copy(strategy = strategy)
    }
    
    /**
     * 添加自定义头部
     */
    fun withHeaders(headers: Map<String, String>): CacheConfig {
        return copy(customHeaders = customHeaders + headers)
    }
    
    companion object {
        /**
         * 创建不使用缓存的配置
         */
        fun noCache(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.NO_CACHE)
        }
        
        /**
         * 创建仅使用网络的配置
         */
        fun networkOnly(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.NETWORK_ONLY)
        }
        
        /**
         * 创建仅使用缓存的配置
         */
        fun cacheOnly(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.CACHE_ONLY)
        }
        
        /**
         * 创建缓存优先的配置
         */
        fun cacheFirst(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.CACHE_FIRST)
        }
        
        /**
         * 创建网络优先的配置
         */
        fun networkFirst(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.NETWORK_FIRST)
        }
        
        /**
         * 创建强制刷新的配置
         */
        fun forceRefresh(): CacheConfig {
            return CacheConfig(strategy = CacheStrategy.FORCE_REFRESH)
        }
    }
}
