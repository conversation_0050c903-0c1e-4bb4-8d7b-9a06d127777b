package com.yjsoft.roadtravel.basiclibrary.network.config

/**
 * 环境配置枚举
 */
enum class Environment(
    val displayName: String,
    val baseUrl: String,
    val isDebug: Boolean
) {
    /**
     * 开发环境
     */
    DEVELOPMENT(
        displayName = "开发环境",
//        baseUrl = "http://dev.yjsoft.com.cn/roadtrip-api/",
        baseUrl = "http://*************:12345/",
        isDebug = true
    ),

    /**
     * 测试环境
     */
    STAGING(
        displayName = "测试环境",
//        baseUrl = "http://dev.yjsoft.com.cn/roadtrip-api/",
        baseUrl = "http://*************:12345/",
        isDebug = true
    ),

    /**
     * 预生产环境
     */
    PRE_PRODUCTION(
        displayName = "预生产环境",
        baseUrl = "https://api.funfuntrip.cn/",
        isDebug = false
    ),

    /**
     * 生产环境
     */
    PRODUCTION(
        displayName = "生产环境",
        baseUrl = "https://api.funfuntrip.cn/",
        isDebug = false
    );

    companion object {
        /**
         * 根据构建类型获取默认环境
         */
        fun getDefault(isDebugBuild: Boolean): Environment {
            return if (isDebugBuild) DEVELOPMENT else PRODUCTION
        }

        /**
         * 根据名称获取环境
         */
        fun fromName(name: String): Environment? {
            return values().find { it.name.equals(name, ignoreCase = true) }
        }

        /**
         * 获取所有环境列表
         */
        fun getAllEnvironments(): List<Environment> {
            return values().toList()
        }
    }
}
