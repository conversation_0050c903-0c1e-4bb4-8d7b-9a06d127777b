package com.yjsoft.roadtravel.basiclibrary.location.model

import android.location.Location
import com.amap.api.location.AMapLocation

/**
 * 定位数据类
 * 封装定位结果信息
 */
data class LocationData(
    /** 纬度 */
    val latitude: Double,
    /** 经度 */
    val longitude: Double,
    /** 精度（米） */
    val accuracy: Float,
    /** 海拔高度（米） */
    val altitude: Double,
    /** 方向角（度） */
    val bearing: Float,
    /** 速度（米/秒） */
    val speed: Float,
    /** 定位时间戳 */
    val timestamp: Long,
    /** 地址信息 */
    val address: String?,
    /** 省份 */
    val province: String?,
    /** 城市 */
    val city: String?,
    /** 区县 */
    val district: String?,
    /** 街道 */
    val street: String?,
    /** 街道号码 */
    val streetNumber: String?,
    /** 兴趣点名称 */
    val poiName: String?,
    /** 定位来源 */
    val locationType: LocationType,
    /** 定位提供者 */
    val provider: String?
) {
    companion object {
        /**
         * 从高德定位结果创建LocationData
         */
        fun fromAMapLocation(aMapLocation: AMapLocation): LocationData {
            return LocationData(
                latitude = aMapLocation.latitude,
                longitude = aMapLocation.longitude,
                accuracy = aMapLocation.accuracy,
                altitude = aMapLocation.altitude,
                bearing = aMapLocation.bearing,
                speed = aMapLocation.speed,
                timestamp = aMapLocation.time,
                address = aMapLocation.address,
                province = aMapLocation.province,
                city = aMapLocation.city,
                district = aMapLocation.district,
                street = aMapLocation.street,
                streetNumber = aMapLocation.streetNum,
                poiName = aMapLocation.poiName,
                locationType = LocationType.fromAMapLocationType(aMapLocation.locationType),
                provider = aMapLocation.provider
            )
        }
        
        /**
         * 创建空的定位数据
         */
        fun empty(): LocationData {
            return LocationData(
                latitude = 0.0,
                longitude = 0.0,
                accuracy = 0f,
                altitude = 0.0,
                bearing = 0f,
                speed = 0f,
                timestamp = 0L,
                address = null,
                province = null,
                city = null,
                district = null,
                street = null,
                streetNumber = null,
                poiName = null,
                locationType = LocationType.UNKNOWN,
                provider = null
            )
        }
    }
    
    /**
     * 是否为有效的定位数据
     */
    val isValid: Boolean
        get() = latitude != 0.0 && longitude != 0.0 && timestamp > 0
    
    /**
     * 获取完整地址
     */
    val fullAddress: String
        get() = buildString {
            province?.let { append(it) }
            city?.let { append(it) }
            district?.let { append(it) }
            street?.let { append(it) }
            streetNumber?.let { append(it) }
        }.ifEmpty { address ?: "未知位置" }
    
    /**
     * 获取简短地址
     */
    val shortAddress: String
        get() = buildString {
            city?.let { append(it) }
            district?.let { append(it) }
        }.ifEmpty { province ?: "未知位置" }
    
    /**
     * 获取定位精度描述
     */
    val accuracyDescription: String
        get() = when {
            accuracy <= 10 -> "高精度"
            accuracy <= 50 -> "中等精度"
            accuracy <= 100 -> "低精度"
            else -> "精度较差"
        }
    
    /**
     * 计算与另一个位置的距离（米）
     */
    fun distanceTo(other: LocationData): Float {
        val results = FloatArray(1)
        Location.distanceBetween(
            latitude, longitude,
            other.latitude, other.longitude,
            results
        )
        return results[0]
    }
    
    /**
     * 转换为Android Location对象
     */
    fun toAndroidLocation(): Location {
        return Location(provider ?: "unknown").apply {
            latitude = <EMAIL>
            longitude = <EMAIL>
            accuracy = <EMAIL>
            altitude = <EMAIL>
            bearing = <EMAIL>
            speed = <EMAIL>
            time = <EMAIL>
        }
    }
}

/**
 * 定位类型枚举
 */
enum class LocationType(val value: Int, val description: String) {
    /** GPS定位 */
    GPS(1, "GPS定位"),
    /** 网络定位 */
    NETWORK(2, "网络定位"),
    /** 被动定位 */
    PASSIVE(3, "被动定位"),
    /** 缓存定位 */
    CACHE(4, "缓存定位"),
    /** 离线定位 */
    OFFLINE(5, "离线定位"),
    /** 未知类型 */
    UNKNOWN(0, "未知类型");
    
    companion object {
        /**
         * 从高德定位类型转换
         */
        fun fromAMapLocationType(type: Int): LocationType {
            return when (type) {
                AMapLocation.LOCATION_TYPE_GPS -> GPS
                AMapLocation.LOCATION_TYPE_SAME_REQ -> NETWORK
                AMapLocation.LOCATION_TYPE_NETWORK -> NETWORK
                AMapLocation.LOCATION_TYPE_WIFI -> NETWORK
                AMapLocation.LOCATION_TYPE_CELL -> NETWORK
                AMapLocation.LOCATION_TYPE_AMAP -> NETWORK
                AMapLocation.LOCATION_TYPE_OFFLINE -> OFFLINE
                AMapLocation.LOCATION_TYPE_LAST_LOCATION_CACHE -> CACHE
                else -> UNKNOWN
            }
        }
    }
}
