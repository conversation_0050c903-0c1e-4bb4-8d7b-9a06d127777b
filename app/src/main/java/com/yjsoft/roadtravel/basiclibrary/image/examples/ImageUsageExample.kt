package com.yjsoft.roadtravel.basiclibrary.image.examples

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.image.components.AvatarImage
import com.yjsoft.roadtravel.basiclibrary.image.components.AvatarSize
import com.yjsoft.roadtravel.basiclibrary.image.components.CircleAvatarImageWithText
import com.yjsoft.roadtravel.basiclibrary.image.components.ErrorPlaceholder
import com.yjsoft.roadtravel.basiclibrary.image.components.LoadingPlaceholder
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImage
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImageWithPlaceholder
import com.yjsoft.roadtravel.basiclibrary.image.components.PlaceholderSize
import com.yjsoft.roadtravel.basiclibrary.image.components.SmartAvatarImage
import com.yjsoft.roadtravel.basiclibrary.image.components.SquarePlaceholder

/**
 * 图片加载框架使用示例
 * 展示各种图片组件的使用方法
 */
@Composable
fun ImageUsageExample() {
    val context = LocalContext.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "图片加载框架使用示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        // 基础网络图片示例
        ExampleSection(title = "基础网络图片") {
            NetworkImage(
                url = "https://picsum.photos/300/200",
                contentDescription = "示例图片",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
            )
        }
        
        // 带占位符的网络图片示例
        ExampleSection(title = "带占位符的网络图片") {
            NetworkImageWithPlaceholder(
                url = "https://picsum.photos/300/200?random=1",
                contentDescription = "带占位符的示例图片",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                showLoadingIndicator = true
            )
        }
        
        // 头像示例
        ExampleSection(title = "头像组件") {
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 基础头像
                AvatarImage(
                    url = "https://picsum.photos/100/100?random=2",
                    contentDescription = "用户头像",
                    size = AvatarSize.Medium
                )
                
                // 带文字的头像
                CircleAvatarImageWithText(
                    url = null, // 故意设为null以显示文字
                    name = "张三",
                    contentDescription = "张三的头像",
                    size = AvatarSize.Medium
                )
                
                // 智能头像（自动生成颜色）
                SmartAvatarImage(
                    url = null,
                    name = "李四",
                    contentDescription = "李四的头像",
                    size = AvatarSize.Medium
                )
            }
        }
        
        // 不同尺寸的头像
        ExampleSection(title = "不同尺寸的头像") {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                SmartAvatarImage(
                    url = null,
                    name = "小",
                    contentDescription = "小头像",
                    size = AvatarSize.ExtraSmall
                )
                SmartAvatarImage(
                    url = null,
                    name = "中",
                    contentDescription = "中头像",
                    size = AvatarSize.Small
                )
                SmartAvatarImage(
                    url = null,
                    name = "大",
                    contentDescription = "大头像",
                    size = AvatarSize.Medium
                )
                SmartAvatarImage(
                    url = null,
                    name = "超大",
                    contentDescription = "超大头像",
                    size = AvatarSize.Large
                )
            }
        }
        
        // 占位符示例
        ExampleSection(title = "占位符组件") {
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                LoadingPlaceholder(
                    modifier = Modifier.size(PlaceholderSize.Small)
                )
                ErrorPlaceholder(
                    modifier = Modifier.size(PlaceholderSize.Small)
                )
                SquarePlaceholder(
                    size = PlaceholderSize.Small,
                    text = "自定义"
                )
            }
        }
        
        // 错误处理示例
        ExampleSection(title = "错误处理示例") {
            NetworkImageWithPlaceholder(
                url = "https://invalid-url-that-will-fail.com/image.jpg",
                contentDescription = "错误示例",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(150.dp),
                showLoadingIndicator = true
            )
        }
        
        // 使用说明
        ExampleSection(title = "使用说明") {
            Text(
                text = buildString {
                    appendLine("1. NetworkImage: 基础网络图片组件")
                    appendLine("2. NetworkImageWithPlaceholder: 带占位符和加载指示器的网络图片")
                    appendLine("3. AvatarImage: 头像组件，支持圆形和方形")
                    appendLine("4. SmartAvatarImage: 智能头像，自动生成背景色和文字")
                    appendLine("5. 各种占位符组件用于不同状态显示")
                    appendLine("6. 支持自定义尺寸、样式和变换效果")
                },
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * 示例区域组件
 */
@Composable
private fun ExampleSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            content()
        }
    }
}

/**
 * 代码使用示例（注释形式）
 */
object ImageUsageCodeExamples {
    
    /**
     * 基础使用示例
     */
    const val BASIC_USAGE = """
        // 1. 在Application中初始化
        class MyApplication : Application() {
            override fun onCreate() {
                super.onCreate()
                ImageManager.init(this)
            }
        }
        
        // 2. 使用网络图片组件
        @Composable
        fun MyScreen() {
            NetworkImage(
                url = "https://example.com/image.jpg",
                contentDescription = "示例图片",
                modifier = Modifier.size(200.dp)
            )
        }
        
        // 3. 使用头像组件
        @Composable
        fun UserProfile() {
            SmartAvatarImage(
                url = user.avatarUrl,
                name = user.name,
                contentDescription = "用户头像",
                size = AvatarSize.Large
            )
        }
    """
    
    /**
     * 高级配置示例
     */
    const val ADVANCED_USAGE = """
        // 1. 自定义配置初始化
        val customConfig = ImageConfig.createDebugConfig(okHttpClient)
        ImageManager.init(context, customConfig, okHttpClient)
        
        // 2. 使用特定类型的ImageLoader
        val avatarLoader = ImageManager.getImageLoader(
            context, 
            ImageLoaderFactory.LoaderType.AVATAR
        )
        
        // 3. 手动创建ImageRequest
        val request = ImageUtils.createAvatarImageRequest(
            context = context,
            data = imageUrl,
            size = 48.dp
        )
        
        // 4. 缓存管理
        ImageManager.clearMemoryCache()
        val cacheSize = ImageManager.getCacheStats()
    """
    
    /**
     * 组件配置示例
     */
    const val COMPONENT_USAGE = """
        // 1. 带错误处理的网络图片
        NetworkImageWithPlaceholder(
            url = imageUrl,
            contentDescription = "图片",
            modifier = Modifier.fillMaxWidth(),
            showLoadingIndicator = true,
            onError = { error ->
                Log.e("Image", "加载失败", error)
            }
        )
        
        // 2. 自定义头像
        AvatarImageWithText(
            url = user.avatar,
            name = user.name,
            contentDescription = "头像",
            size = 64.dp,
            backgroundColor = Color.Blue,
            textColor = Color.White
        )
        
        // 3. 占位符使用
        if (imageUrl.isNullOrEmpty()) {
            EmptyPlaceholder(
                modifier = Modifier.size(200.dp),
                text = "暂无图片"
            )
        }
    """
}
