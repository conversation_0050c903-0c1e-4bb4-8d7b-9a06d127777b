package com.yjsoft.roadtravel.basiclibrary.payment.core

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType

/**
 * 支付策略工厂
 * 负责创建和管理支付策略实例
 */
object PaymentFactory {
    
    private const val TAG = "PaymentFactory %s"
    
    // 策略创建器映射表
    private val strategyCreators = mutableMapOf<PaymentType, () -> PaymentStrategy>()
    
    /**
     * 初始化工厂
     */
    fun init() {
        // 注册默认的策略创建器
        registerDefaultCreators()
        LogManager.d(TAG, "支付策略工厂初始化完成")
    }
    
    /**
     * 注册默认的策略创建器
     */
    private fun registerDefaultCreators() {
        // 注册支付宝策略创建器
        registerCreator(PaymentType.ALIPAY) {
            createAlipayStrategy()
        }
        
        // 注册微信支付策略创建器
        registerCreator(PaymentType.WECHAT_PAY) {
            createWeChatPayStrategy()
        }
        
        // 注册云闪付策略创建器
        registerCreator(PaymentType.UNION_PAY) {
            createUnionPayStrategy()
        }
    }
    
    /**
     * 注册策略创建器
     * @param type 支付类型
     * @param creator 创建器函数
     */
    fun registerCreator(type: PaymentType, creator: () -> PaymentStrategy) {
        strategyCreators[type] = creator
        LogManager.d(TAG, "注册策略创建器: ${type.name}")
    }
    
    /**
     * 创建支付策略
     * @param type 支付类型
     * @return 支付策略实例
     */
    fun createStrategy(type: PaymentType): PaymentStrategy? {
        val creator = strategyCreators[type]
        return if (creator != null) {
            try {
                val strategy = creator()
                LogManager.d(TAG, "创建支付策略成功: ${type.name}")
                strategy
            } catch (e: Exception) {
                LogManager.e(TAG, "创建支付策略失败: ${type.name}", e)
                null
            }
        } else {
            LogManager.w(TAG, "未找到支付策略创建器: ${type.name}")
            null
        }
    }
    
    /**
     * 创建支付宝策略
     */
    private fun createAlipayStrategy(): PaymentStrategy {
        return try {
            // 使用反射创建，避免直接依赖
            val clazz = Class.forName("com.yjsoft.roadtravel.basiclibrary.payment.strategies.AlipayStrategy")
            clazz.getDeclaredConstructor().newInstance() as PaymentStrategy
        } catch (e: Exception) {
            LogManager.w(TAG, "支付宝策略类未找到，返回空实现")
            createEmptyStrategy(PaymentType.ALIPAY)
        }
    }
    
    /**
     * 创建微信支付策略
     */
    private fun createWeChatPayStrategy(): PaymentStrategy {
        return try {
            // 使用反射创建，避免直接依赖
            val clazz = Class.forName("com.yjsoft.roadtravel.basiclibrary.payment.strategies.WeChatPayStrategy")
            clazz.getDeclaredConstructor().newInstance() as PaymentStrategy
        } catch (e: Exception) {
            LogManager.w(TAG, "微信支付策略类未找到，返回空实现")
            createEmptyStrategy(PaymentType.WECHAT_PAY)
        }
    }
    
    /**
     * 创建云闪付策略
     */
    private fun createUnionPayStrategy(): PaymentStrategy {
        return try {
            // 使用反射创建，避免直接依赖
            val clazz = Class.forName("com.yjsoft.roadtravel.basiclibrary.payment.strategies.UnionPayStrategy")
            clazz.getDeclaredConstructor().newInstance() as PaymentStrategy
        } catch (e: Exception) {
            LogManager.w(TAG, "云闪付策略类未找到，返回空实现")
            createEmptyStrategy(PaymentType.UNION_PAY)
        }
    }
    
    /**
     * 创建空的支付策略实现
     * @param type 支付类型
     * @return 空实现的支付策略
     */
    private fun createEmptyStrategy(type: PaymentType): PaymentStrategy {
        return object : PaymentStrategy {
            override fun getPaymentType(): PaymentType = type
            
            override suspend fun isAvailable(context: android.content.Context): Boolean = false
            
            override suspend fun initialize(context: android.content.Context): Boolean = false
            
            override suspend fun pay(
                context: android.content.Context,
                request: com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
            ): com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult {
                return com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult.Error(
                    orderId = request.orderId,
                    paymentType = type,
                    errorCode = "STRATEGY_NOT_IMPLEMENTED",
                    errorMessage = "支付策略未实现: ${type.name}"
                )
            }
            
            override fun isConfigured(): Boolean = false
            
            override fun getSdkVersion(): String = "未知"
            
            override fun cleanup() {}
        }
    }
    
    /**
     * 获取所有已注册的支付类型
     * @return 支付类型列表
     */
    fun getRegisteredTypes(): List<PaymentType> {
        return strategyCreators.keys.toList()
    }
    
    /**
     * 检查支付类型是否已注册
     * @param type 支付类型
     * @return 是否已注册
     */
    fun isTypeRegistered(type: PaymentType): Boolean {
        return strategyCreators.containsKey(type)
    }
    
    /**
     * 清理工厂
     */
    fun cleanup() {
        strategyCreators.clear()
        LogManager.d(TAG, "支付策略工厂已清理")
    }
}
