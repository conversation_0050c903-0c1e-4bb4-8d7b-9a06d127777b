package com.yjsoft.roadtravel.basiclibrary.permission.utils

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig


/**
 * 权限日志工具类
 * 集成现有的LogManager，提供权限相关的日志记录功能
 */
object PermissionLogger {
    
    /**
     * 记录调试日志
     */
    fun d(tag: String, message: String) {
        if (PermissionConfig.enableLogging) {
            LogManager.d("[$tag] $message")
        }
    }
    
    /**
     * 记录信息日志
     */
    fun i(tag: String, message: String) {
        if (PermissionConfig.enableLogging) {
            LogManager.i("[$tag] $message")
        }
    }
    
    /**
     * 记录警告日志
     */
    fun w(tag: String, message: String) {
        if (PermissionConfig.enableLogging) {
            LogManager.w("[$tag] $message")
        }
    }
    
    /**
     * 记录错误日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        if (PermissionConfig.enableLogging) {
            if (throwable != null) {
                LogManager.e(throwable, "[$tag] $message")
            } else {
                LogManager.e("[$tag] $message")
            }
        }
    }
    
    /**
     * 记录权限请求开始
     */
    fun logPermissionRequestStart(permissions: List<String>, requestId: String, source: String?) {
        val sourceInfo = source?.let { " (来源: $it)" } ?: ""
        LogManager.i("开始权限请求$sourceInfo - 权限: $permissions, 请求ID: $requestId")
    }
    
    /**
     * 记录权限请求结果
     */
    fun logPermissionRequestResult(
        permissions: List<String>,
        grantedPermissions: List<String>,
        deniedPermissions: List<String>,
        requestId: String
    ) {
        when {
            deniedPermissions.isEmpty() -> {
                LogManager.i("权限请求成功 - 所有权限已授予: $permissions, 请求ID: $requestId")
            }
            grantedPermissions.isEmpty() -> {
                LogManager.w("权限请求失败 - 所有权限被拒绝: $permissions, 请求ID: $requestId")
            }
            else -> {
                LogManager.w("权限请求部分成功 - 已授予: $grantedPermissions, 被拒绝: $deniedPermissions, 请求ID: $requestId")
            }
        }
    }
    
    /**
     * 记录权限状态检查
     */
    fun logPermissionCheck(permission: String, isGranted: Boolean) {
        LogManager.d("权限检查 - $permission: ${if (isGranted) "已授予" else "未授予"}")
    }
    
    /**
     * 记录权限说明对话框显示
     */
    fun logRationaleDialogShown(permissions: List<String>) {
        LogManager.i("显示权限说明对话框 - 权限: $permissions")
    }
    
    /**
     * 记录权限说明对话框结果
     */
    fun logRationaleDialogResult(permissions: List<String>, userAccepted: Boolean) {
        val result = if (userAccepted) "用户同意" else "用户拒绝"
        LogManager.i("权限说明对话框结果 - $result, 权限: $permissions")
    }
    
    /**
     * 记录设置引导对话框显示
     */
    fun logSettingsDialogShown(permissions: List<String>) {
        LogManager.i("显示设置引导对话框 - 权限: $permissions")
    }
    
    /**
     * 记录设置引导对话框结果
     */
    fun logSettingsDialogResult(permissions: List<String>, userNavigated: Boolean) {
        val result = if (userNavigated) "用户前往设置" else "用户取消"
        LogManager.i("设置引导对话框结果 - $result, 权限: $permissions")
    }
    
    /**
     * 记录权限被永久拒绝
     */
    fun logPermissionPermanentlyDenied(permissions: List<String>) {
        LogManager.w("权限被永久拒绝 - $permissions")
    }
    
    /**
     * 记录权限请求取消
     */
    fun logPermissionRequestCancelled(permissions: List<String>, requestId: String, reason: String?) {
        val reasonInfo = reason?.let { " - 原因: $it" } ?: ""
        LogManager.w("权限请求被取消 - 权限: $permissions, 请求ID: $requestId$reasonInfo")
    }
    
    /**
     * 记录权限请求错误
     */
    fun logPermissionRequestError(permissions: List<String>, requestId: String, error: Throwable) {
        LogManager.e(error, "权限请求出错 - 权限: $permissions, 请求ID: $requestId")
    }
    
    /**
     * 记录权限请求超时
     */
    fun logPermissionRequestTimeout(permissions: List<String>, requestId: String, timeoutMillis: Long) {
        LogManager.w("权限请求超时 - 权限: $permissions, 请求ID: $requestId, 超时时间: ${timeoutMillis}ms")
    }
    
    /**
     * 记录框架初始化
     */
    fun logFrameworkInitialized() {
        LogManager.i("权限框架初始化完成")
    }
    
    /**
     * 记录框架销毁
     */
    fun logFrameworkDestroyed() {
        LogManager.i("权限框架已销毁")
    }
    
    /**
     * 记录权限检查器创建
     */
    fun logCheckerCreated(checkerType: String) {
        LogManager.d("创建权限检查器 - 类型: $checkerType")
    }
    
    /**
     * 记录权限申请配置
     */
    fun logRequestConfig(config: String) {
        LogManager.d("权限申请配置 - $config")
    }
    
    /**
     * 记录UI事件
     */
    fun logUIEvent(event: String, details: String? = null) {
        val detailsInfo = details?.let { " - $it" } ?: ""
        LogManager.d("UI事件 - $event$detailsInfo")
    }
    
    /**
     * 记录用户交互
     */
    fun logUserInteraction(action: String, context: String? = null) {
        val contextInfo = context?.let { " - $it" } ?: ""
        LogManager.d("用户交互 - $action$contextInfo")
    }
    
    /**
     * 记录性能指标
     */
    fun logPerformanceMetric(metric: String, value: Long, unit: String = "ms") {
        LogManager.d("性能指标 - $metric: $value$unit")
    }
    
    /**
     * 记录权限状态变化
     */
    fun logPermissionStateChange(permission: String, oldStatus: String, newStatus: String) {
        LogManager.i("权限状态变化 - $permission: $oldStatus -> $newStatus")
    }
}
