package com.yjsoft.roadtravel.basiclibrary.auth.wechat

import android.content.Context
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 微信登录管理器
 * 负责处理微信登录的所有相关逻辑
 * 使用单例模式，不依赖Hilt注入
 */
class WeChatLoginManager private constructor() {
    
    companion object {
        private const val TAG = "WeChatLoginManager %s"

        @Volatile
        private var INSTANCE: WeChatLoginManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(): WeChatLoginManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WeChatLoginManager().also { INSTANCE = it }
            }
        }
    }
    
    private var wxApi: IWXAPI? = null
    private var loginCallback: ((WeChatLoginResult) -> Unit)? = null
    
    /**
     * 初始化微信SDK
     */
    fun initialize(context: Context): Boolean {
        return try {
            wxApi = WXAPIFactory.createWXAPI(context, WeChatConfig.APP_ID, true)
            val registerResult = wxApi?.registerApp(WeChatConfig.APP_ID) ?: false
            
            LogManager.d(TAG, "微信SDK初始化结果: $registerResult")
            registerResult
        } catch (e: Exception) {
            LogManager.e(TAG, "微信SDK初始化失败", e)
            false
        }
    }
    
    /**
     * 检查微信是否已安装
     */
    fun isWeChatInstalled(): Boolean {
        return wxApi?.isWXAppInstalled == true
    }
    
    /**
     * 发起微信登录
     */
    suspend fun login(): WeChatLoginResult = suspendCancellableCoroutine { continuation ->
        try {
            if (!isWeChatInstalled()) {
                continuation.resume(WeChatLoginResult.Error("微信未安装"))
                return@suspendCancellableCoroutine
            }
            
            // 设置回调
            loginCallback = { result ->
                if (continuation.isActive) {
                    continuation.resume(result)
                }
            }
            
            // 构造登录请求
            val req = SendAuth.Req().apply {
                scope = WeChatConfig.SCOPE // 只能填 snsapi_userinfo
                state = "wechat_login_${System.currentTimeMillis()}" // 防CSRF攻击
            }
            
            // 发送登录请求
            val sendResult = wxApi?.sendReq(req) ?: false
            
            if (!sendResult) {
                loginCallback = null
                continuation.resume(WeChatLoginResult.Error("发起微信登录失败"))
            }
            
            LogManager.d(TAG, "微信登录请求已发送")
            
        } catch (e: Exception) {
            loginCallback = null
            LogManager.e(TAG, "微信登录异常", e)
            continuation.resume(WeChatLoginResult.Error("微信登录异常: ${e.message}"))
        }
    }
    
    /**
     * 处理微信登录回调
     * 这个方法应该在微信回调的Activity中调用
     */
    fun handleLoginCallback(code: String?, state: String?, errCode: Int) {
        val callback = loginCallback
        loginCallback = null
        
        when (errCode) {
            0 -> { // 用户同意授权
                if (!code.isNullOrEmpty()) {
                    LogManager.d(TAG, "微信授权成功，code: $code, state: $state")
                    callback?.invoke(WeChatLoginResult.Success(code, state))
                } else {
                    LogManager.w(TAG, "微信授权成功但code为空")
                    callback?.invoke(WeChatLoginResult.Error("授权码为空"))
                }
            }
            -2 -> { // 用户取消
                LogManager.d(TAG, "用户取消微信登录")
                callback?.invoke(WeChatLoginResult.Cancelled)
            }
            -4 -> { // 用户拒绝授权
                LogManager.d(TAG, "用户拒绝微信授权")
                callback?.invoke(WeChatLoginResult.Denied)
            }
            -1 -> { // 自定义错误码：响应为空
                LogManager.w(TAG, "微信登录响应为空")
                callback?.invoke(WeChatLoginResult.Error("微信登录响应异常"))
            }
            -999 -> { // 自定义错误码：处理异常
                LogManager.w(TAG, "微信登录处理异常")
                callback?.invoke(WeChatLoginResult.Error("微信登录处理异常"))
            }
            -998 -> { // 自定义错误码：回调处理失败
                LogManager.w(TAG, "微信回调处理失败")
                callback?.invoke(WeChatLoginResult.Error("微信回调处理失败"))
            }
            -997 -> { // 自定义错误码：回调异常
                LogManager.w(TAG, "微信回调异常")
                callback?.invoke(WeChatLoginResult.Error("微信回调异常"))
            }
            -996 -> { // 自定义错误码：新Intent处理失败
                LogManager.w(TAG, "微信新Intent处理失败")
                callback?.invoke(WeChatLoginResult.Error("微信Intent处理失败"))
            }
            -995 -> { // 自定义错误码：新Intent异常
                LogManager.w(TAG, "微信新Intent异常")
                callback?.invoke(WeChatLoginResult.Error("微信Intent异常"))
            }
            -994 -> { // 自定义错误码：未知响应类型
                LogManager.w(TAG, "微信未知响应类型")
                callback?.invoke(WeChatLoginResult.Error("微信响应类型未知"))
            }
            else -> { // 其他错误
                LogManager.w(TAG, "微信登录失败，错误码: $errCode")
                callback?.invoke(WeChatLoginResult.Error("登录失败，错误码: $errCode"))
            }
        }
    }
    
    /**
     * 取消登录
     */
    fun cancelLogin() {
        loginCallback?.invoke(WeChatLoginResult.Cancelled)
        loginCallback = null
    }
}

/**
 * 微信登录结果
 */
sealed class WeChatLoginResult {
    /**
     * 登录成功
     * @param code 授权码
     * @param state 状态参数
     */
    data class Success(val code: String, val state: String?) : WeChatLoginResult()
    
    /**
     * 用户取消登录
     */
    object Cancelled : WeChatLoginResult()
    
    /**
     * 用户拒绝授权
     */
    object Denied : WeChatLoginResult()
    
    /**
     * 登录错误
     * @param message 错误信息
     */
    data class Error(val message: String) : WeChatLoginResult()
}
