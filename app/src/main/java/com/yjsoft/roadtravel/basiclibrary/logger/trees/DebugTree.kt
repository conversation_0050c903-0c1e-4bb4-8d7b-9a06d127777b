package com.yjsoft.roadtravel.basiclibrary.logger.trees

import android.util.Log
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import timber.log.Timber
import java.util.regex.Pattern

/**
 * 调试环境日志Tree
 * 提供丰富的调试信息，包括线程、方法、文件位置等
 */
class DebugTree : Timber.DebugTree() {
    
    companion object {
        private const val MAX_LOG_LENGTH = 4000
        private const val MAX_TAG_LENGTH = 50  // 增加标签长度限制
        private const val CALL_STACK_INDEX = 5  // 调整调用栈索引
        private val ANONYMOUS_CLASS = Pattern.compile("(\\$\\d+)+$")
        
        // 需要跳过的类名（框架内部类）
        private val SKIP_CLASS_NAMES = setOf(
            "timber.log.Timber",
            "com.yjsoft.roadtravel.basiclibrary.logger.LogManager",
            "com.yjsoft.roadtravel.basiclibrary.logger.trees.DebugTree"
        )
    }
    
    /**
     * 创建日志标签
     * 包含类名、方法名、行号等信息
     */
    override fun createStackElementTag(element: StackTraceElement): String? {
        return when (LogConfig.Debug.logDisplayMode) {
            LogConfig.LogDisplayMode.CALLER_INFO_ONLY -> {
                // 仅调用者信息模式：使用简单的类名作为标签
                val realCaller = getRealCaller() ?: element
                val className = formatClassName(realCaller.className)
                val simpleClassName = className.substringAfterLast('.')
                simpleClassName
            }
            LogConfig.LogDisplayMode.MINIMAL -> {
                // 最简模式：使用默认标签
                super.createStackElementTag(element)
            }
            LogConfig.LogDisplayMode.STANDARD -> {
                // 标准模式：显示完整信息
                val realCaller = getRealCaller() ?: element
                var tag = formatClassName(realCaller.className)
                
                if (LogConfig.Debug.showDetailedTag && LogConfig.Debug.showMethodInfo) {
                    val methodInfo = if (LogConfig.Debug.showFileInfo) {
                        "(${realCaller.fileName}:${realCaller.lineNumber})"
                    } else {
                        ""
                    }
                    
                    val threadInfo = if (LogConfig.Debug.showThreadInfo) {
                        "[${Thread.currentThread().name}]"
                    } else {
                        ""
                    }
                    
                    tag = "$threadInfo$tag.${realCaller.methodName}$methodInfo"
                }
                
                // 确保标签长度不超过限制
                if (tag.length > MAX_TAG_LENGTH) {
                    tag.substring(0, MAX_TAG_LENGTH)
                } else {
                    tag
                }
            }
        }
    }
    
    /**
     * 输出日志
     * 处理长日志的分割输出，并在消息前添加详细的调用者信息
     */
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        val enhancedMessage = when (LogConfig.Debug.logDisplayMode) {
            LogConfig.LogDisplayMode.CALLER_INFO_ONLY -> {
                // 仅调用者信息模式：在消息前添加调用者信息
                val callerInfo = getDetailedCallerInfo()
                if (callerInfo.isNotEmpty()) {
                    "[$callerInfo] $message"
                } else {
                    message
                }
            }
            LogConfig.LogDisplayMode.MINIMAL -> {
                // 最简模式：不添加额外信息
                message
            }
            LogConfig.LogDisplayMode.STANDARD -> {
                // 标准模式：根据配置决定是否添加调用者信息
                val callerInfo = getDetailedCallerInfo()
                if (callerInfo.isNotEmpty() && !LogConfig.Debug.showDetailedTag) {
                    "[$callerInfo] $message"
                } else {
                    message
                }
            }
        }
        
        if (enhancedMessage.length < MAX_LOG_LENGTH) {
            // 短消息直接输出
            if (t != null) {
                Log.println(priority, tag, "$enhancedMessage\n${Log.getStackTraceString(t)}")
            } else {
                Log.println(priority, tag, enhancedMessage)
            }
            return
        }
        
        // 长消息分割输出
        var i = 0
        val length = enhancedMessage.length
        while (i < length) {
            var newline = enhancedMessage.indexOf('\n', i)
            newline = if (newline != -1) newline else length
            
            do {
                val end = Math.min(newline, i + MAX_LOG_LENGTH)
                val part = enhancedMessage.substring(i, end)
                Log.println(priority, tag, part)
                i = end
            } while (i < newline)
            
            i++
        }
        
        // 输出异常信息
        if (t != null) {
            Log.println(priority, tag, Log.getStackTraceString(t))
        }
    }
    
    /**
     * 获取真正的调用者信息，跳过框架内部类
     */
    private fun getRealCaller(): StackTraceElement? {
        val stackTrace = Thread.currentThread().stackTrace
        
        for (element in stackTrace) {
            val className = element.className
            // 跳过框架内部类和系统类
            if (!SKIP_CLASS_NAMES.any { className.startsWith(it) } &&
                !className.startsWith("java.") &&
                !className.startsWith("android.") &&
                !className.startsWith("dalvik.") &&
                !className.startsWith("com.android.")) {
                return element
            }
        }
        
        return null
    }
    
    /**
     * 获取详细的调用者信息
     */
    private fun getDetailedCallerInfo(): String {
        val caller = getRealCaller() ?: return ""
        
        val className = formatClassName(caller.className)
        val simpleClassName = className.substringAfterLast('.')
        
        return "${simpleClassName}.${caller.methodName}(${caller.fileName}:${caller.lineNumber})"
    }
    
    /**
     * 获取调用栈信息
     */
    private fun getStackTraceInfo(): String {
        val stackTrace = Thread.currentThread().stackTrace
        if (stackTrace.size <= CALL_STACK_INDEX) {
            return ""
        }
        
        val builder = StringBuilder()
        val maxDepth = Math.min(
            stackTrace.size,
            CALL_STACK_INDEX + LogConfig.Debug.methodStackDepth
        )
        
        for (i in CALL_STACK_INDEX until maxDepth) {
            val element = stackTrace[i]
            // 跳过框架内部类
            if (SKIP_CLASS_NAMES.any { element.className.startsWith(it) }) {
                continue
            }
            
            builder.append("  at ")
                .append(element.className)
                .append(".")
                .append(element.methodName)
                .append("(")
                .append(element.fileName)
                .append(":")
                .append(element.lineNumber)
                .append(")")
            
            if (i < maxDepth - 1) {
                builder.append("\n")
            }
        }
        
        return builder.toString()
    }
    
    /**
     * 格式化类名
     * 移除匿名类的数字后缀
     */
    private fun formatClassName(className: String): String {
        val matcher = ANONYMOUS_CLASS.matcher(className)
        return if (matcher.find()) {
            matcher.replaceAll("")
        } else {
            className
        }
    }
    
    /**
     * 检查是否应该输出日志
     */
    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // 在调试模式下，输出所有级别的日志
        return true
    }
}
