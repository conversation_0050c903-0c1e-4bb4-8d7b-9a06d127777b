package com.yjsoft.roadtravel.basiclibrary.payment.test

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentCallback
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.strategies.AlipayStrategy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.math.BigDecimal

/**
 * 支付测试辅助类
 * 用于测试支付功能的各种场景
 */
object PaymentTestHelper {
    
    private const val TAG = "PaymentTestHelper %s"
    
    /**
     * 初始化测试环境
     */
    fun initTestEnvironment(context: Context) {
        try {
            // 初始化支付管理器
            PaymentManager.init(context, debugMode = true)
            
            // 配置测试用的支付参数
            configureTestPaymentParams()
            
            LogManager.d(TAG, "支付测试环境初始化完成")
        } catch (e: Exception) {
            LogManager.e(TAG, "支付测试环境初始化失败", e)
            throw e
        }
    }
    
    /**
     * 配置测试用的支付参数
     */
    private fun configureTestPaymentParams() {
        // 配置支付宝测试参数（这里使用示例参数，实际使用时需要替换为真实参数）
        PaymentConfig.configureAlipay(
            appId = "2021000000000000", // 示例AppId
            privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...", // 示例私钥
            publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA..." // 示例公钥
        )
        
        // 配置微信支付测试参数
        PaymentConfig.configureWeChatPay(
            appId = "wx1234567890abcdef", // 示例AppId
            merchantId = "1234567890", // 示例商户号
            apiKey = "abcdefghijklmnopqrstuvwxyz123456" // 示例API密钥
        )
        
        LogManager.d(TAG, "测试支付参数配置完成")
    }
    
    /**
     * 测试微信支付
     */
    fun testWeChatPayment(context: Context, callback: PaymentTestCallback? = null) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                LogManager.d(TAG, "开始测试微信支付")

                // 创建测试订单
                val request = createTestPaymentRequest("WECHAT_TEST_${System.currentTimeMillis()}")

                // 创建支付回调
                val paymentCallback = object : PaymentCallback {
                    override fun onPaymentStart(orderId: String, paymentType: PaymentType) {
                        LogManager.d(TAG, "支付开始 - 订单: $orderId, 类型: ${paymentType.name}")
                        callback?.onTestStart("微信支付测试开始")
                    }

                    override fun onPaymentSuccess(result: PaymentResult.Success) {
                        LogManager.d(TAG, "支付成功 - 订单: ${result.orderId}, 交易号: ${result.transactionId}")
                        callback?.onTestSuccess("微信支付测试成功", result)
                    }

                    override fun onPaymentCancel(result: PaymentResult.Cancel) {
                        LogManager.d(TAG, "支付取消 - 订单: ${result.orderId}")
                        callback?.onTestCancel("微信支付测试取消", result)
                    }

                    override fun onPaymentError(result: PaymentResult.Error) {
                        LogManager.e(TAG, "支付失败 - 订单: ${result.orderId}, 错误: ${result.errorMessage}")
                        callback?.onTestError("微信支付测试失败", result)
                    }
                }

                // 执行支付
                val result = PaymentManager.pay(context, PaymentType.WECHAT_PAY, request)

                // 处理结果
                when (result) {
                    is PaymentResult.Success -> paymentCallback.onPaymentSuccess(result)
                    is PaymentResult.Cancel -> paymentCallback.onPaymentCancel(result)
                    is PaymentResult.Error -> paymentCallback.onPaymentError(result)
                    is PaymentResult.Processing -> {
                        LogManager.d(TAG, "支付处理中...")
                        callback?.onTestProcessing("微信支付处理中", result)
                    }
                }

            } catch (e: Exception) {
                LogManager.e(TAG, "微信支付测试异常", e)
                callback?.onTestException("微信支付测试异常", e)
            }
        }
    }

    /**
     * 测试支付宝支付
     */
    fun testAlipayPayment(context: Context, callback: PaymentTestCallback? = null) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                LogManager.d(TAG, "开始测试支付宝支付")
                
                // 创建测试订单
                val request = createTestPaymentRequest("ALIPAY_TEST_${System.currentTimeMillis()}")
                
                // 创建支付回调
                val paymentCallback = object : PaymentCallback {
                    override fun onPaymentStart(orderId: String, paymentType: PaymentType) {
                        LogManager.d(TAG, "支付开始 - 订单: $orderId, 类型: ${paymentType.name}")
                        callback?.onTestStart("支付宝支付测试开始")
                    }
                    
                    override fun onPaymentSuccess(result: PaymentResult.Success) {
                        LogManager.d(TAG, "支付成功 - 订单: ${result.orderId}, 交易号: ${result.transactionId}")
                        callback?.onTestSuccess("支付宝支付测试成功", result)
                    }
                    
                    override fun onPaymentCancel(result: PaymentResult.Cancel) {
                        LogManager.d(TAG, "支付取消 - 订单: ${result.orderId}")
                        callback?.onTestCancel("支付宝支付测试取消", result)
                    }
                    
                    override fun onPaymentError(result: PaymentResult.Error) {
                        LogManager.e(TAG, "支付失败 - 订单: ${result.orderId}, 错误: ${result.errorMessage}")
                        callback?.onTestError("支付宝支付测试失败", result)
                    }
                }
                
                // 执行支付
                val result = PaymentManager.pay(context, PaymentType.ALIPAY, request)
                
                // 处理结果
                when (result) {
                    is PaymentResult.Success -> paymentCallback.onPaymentSuccess(result)
                    is PaymentResult.Cancel -> paymentCallback.onPaymentCancel(result)
                    is PaymentResult.Error -> paymentCallback.onPaymentError(result)
                    is PaymentResult.Processing -> {
                        LogManager.d(TAG, "支付处理中...")
                        callback?.onTestProcessing("支付宝支付处理中", result)
                    }
                }
                
            } catch (e: Exception) {
                LogManager.e(TAG, "支付宝支付测试异常", e)
                callback?.onTestException("支付宝支付测试异常", e)
            }
        }
    }
    
    /**
     * 测试支付方式可用性
     */
    fun testPaymentAvailability(context: Context, callback: PaymentTestCallback? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LogManager.d(TAG, "开始测试支付方式可用性")
                
                val availableTypes = PaymentManager.getAvailablePaymentTypes(context)
                val results = mutableMapOf<PaymentType, Boolean>()
                
                for (type in PaymentType.values()) {
                    val isAvailable = PaymentManager.isPaymentTypeAvailable(context, type)
                    results[type] = isAvailable
                    LogManager.d(TAG, "支付方式 ${type.name} 可用性: $isAvailable")
                }
                
                callback?.onAvailabilityTestComplete(results)
                
            } catch (e: Exception) {
                LogManager.e(TAG, "支付方式可用性测试异常", e)
                callback?.onTestException("支付方式可用性测试异常", e)
            }
        }
    }
    
    /**
     * 测试支付策略
     */
    fun testPaymentStrategy(context: Context, paymentType: PaymentType, callback: PaymentTestCallback? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LogManager.d(TAG, "开始测试支付策略: ${paymentType.name}")
                
                val strategy = PaymentManager.getStrategy(paymentType)
                if (strategy != null) {
                    val isConfigured = strategy.isConfigured()
                    val isAvailable = strategy.isAvailable(context)
                    val sdkVersion = strategy.getSdkVersion()
                    
                    val testResult = PaymentStrategyTestResult(
                        paymentType = paymentType,
                        isConfigured = isConfigured,
                        isAvailable = isAvailable,
                        sdkVersion = sdkVersion,
                        strategyClass = strategy::class.java.simpleName
                    )
                    
                    LogManager.d(TAG, "策略测试结果: $testResult")
                    callback?.onStrategyTestComplete(testResult)
                } else {
                    LogManager.w(TAG, "未找到支付策略: ${paymentType.name}")
                    callback?.onTestError("未找到支付策略", null)
                }
                
            } catch (e: Exception) {
                LogManager.e(TAG, "支付策略测试异常", e)
                callback?.onTestException("支付策略测试异常", e)
            }
        }
    }
    
    /**
     * 创建测试支付请求
     */
    private fun createTestPaymentRequest(orderId: String): PaymentRequest {
        return PaymentRequest(
            orderId = orderId,
            amount = BigDecimal("0.01"), // 测试金额1分钱
            title = "支付框架测试订单",
            description = "这是一个用于测试支付框架功能的测试订单",
            extraParams = mapOf(
                "test_mode" to "true",
                "test_timestamp" to System.currentTimeMillis().toString()
            )
        )
    }
    
    /**
     * 测试所有支付方式
     */
    fun testAllPaymentMethods(context: Context, callback: PaymentTestCallback? = null) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                LogManager.d(TAG, "开始测试所有支付方式")
                callback?.onTestStart("开始测试所有支付方式")

                // 测试支付宝
                testAlipayPayment(context, object : PaymentTestCallback {
                    override fun onTestSuccess(message: String, result: PaymentResult?) {
                        LogManager.d(TAG, "支付宝测试成功")
                        // 继续测试微信支付
                        testWeChatPayment(context, callback)
                    }

                    override fun onTestError(message: String, result: PaymentResult?) {
                        LogManager.e(TAG, "支付宝测试失败: $message")
                        callback?.onTestError("支付宝测试失败: $message", result)
                    }

                    override fun onTestException(message: String, exception: Exception?) {
                        LogManager.e(TAG, "支付宝测试异常: $message")
                        callback?.onTestException("支付宝测试异常: $message", exception)
                    }
                })

            } catch (e: Exception) {
                LogManager.e(TAG, "测试所有支付方式异常", e)
                callback?.onTestException("测试所有支付方式异常", e)
            }
        }
    }

    /**
     * 清理测试环境
     */
    fun cleanupTestEnvironment() {
        try {
            PaymentManager.cleanup()
            LogManager.d(TAG, "支付测试环境清理完成")
        } catch (e: Exception) {
            LogManager.e(TAG, "支付测试环境清理失败", e)
        }
    }
}

/**
 * 支付测试回调接口
 */
interface PaymentTestCallback {
    fun onTestStart(message: String) {}
    fun onTestSuccess(message: String, result: PaymentResult?) {}
    fun onTestCancel(message: String, result: PaymentResult?) {}
    fun onTestError(message: String, result: PaymentResult?) {}
    fun onTestProcessing(message: String, result: PaymentResult?) {}
    fun onTestException(message: String, exception: Exception?) {}
    fun onAvailabilityTestComplete(results: Map<PaymentType, Boolean>) {}
    fun onStrategyTestComplete(result: PaymentStrategyTestResult) {}
}

/**
 * 支付策略测试结果
 */
data class PaymentStrategyTestResult(
    val paymentType: PaymentType,
    val isConfigured: Boolean,
    val isAvailable: Boolean,
    val sdkVersion: String,
    val strategyClass: String
)
