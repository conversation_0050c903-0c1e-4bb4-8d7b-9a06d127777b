package com.yjsoft.roadtravel.basiclibrary.image.loader

import android.content.Context
import coil3.ImageLoader
import coil3.disk.DiskCache
import coil3.memory.MemoryCache
import coil3.network.okhttp.OkHttpNetworkFetcherFactory
import coil3.util.DebugLogger
import com.yjsoft.roadtravel.basiclibrary.image.config.ImageCacheConfig
import com.yjsoft.roadtravel.basiclibrary.image.config.ImageConfig
import com.yjsoft.roadtravel.basiclibrary.image.interceptors.ImageCacheInterceptor
import com.yjsoft.roadtravel.basiclibrary.image.interceptors.ImageLoggingInterceptor
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import okhttp3.OkHttpClient

/**
 * ImageLoader实例管理器
 * 负责创建和管理ImageLoader实例，类似于RetrofitInstance的设计模式
 *
 * 注意：为了避免内存泄漏，使用ApplicationContext并确保正确的生命周期管理
 */
class ImageLoaderInstance private constructor(
    context: Context,
    private val config: ImageConfig.ImageLoadConfig,
    private val okHttpClient: OkHttpClient? = null
) {

    // 使用ApplicationContext避免内存泄漏
    private val applicationContext = context.applicationContext
    
    companion object {
        @Volatile
        private var INSTANCE: ImageLoaderInstance? = null
        
        /**
         * 获取ImageLoaderInstance单例
         */
        fun getInstance(
            context: Context,
            config: ImageConfig.ImageLoadConfig? = null,
            okHttpClient: OkHttpClient? = null
        ): ImageLoaderInstance {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ImageLoaderInstance(
                    context.applicationContext,
                    config ?: ImageConfig.getCurrentConfig(),
                    okHttpClient
                ).also { INSTANCE = it }
            }
        }
        
        /**
         * 重置实例（用于配置更新）
         */
        fun reset() {
            synchronized(this) {
                INSTANCE?.cleanup()
                INSTANCE = null
            }
        }
    }
    
    // 内存缓存实例
    private val memoryCacheInstance: MemoryCache by lazy {
        ImageCacheConfig.createMemoryCache(applicationContext, config)
    }

    // 磁盘缓存实例
    private val diskCacheInstance: DiskCache by lazy {
        ImageCacheConfig.createDiskCache(applicationContext, config)
    }
    
    // ImageLoader实例
    private val imageLoaderInstance: ImageLoader by lazy {
        createImageLoader()
    }
    
    /**
     * 创建ImageLoader实例
     */
    private fun createImageLoader(): ImageLoader {
        LogManager.tag(LogConfig.Tags.IMAGE).d("开始创建ImageLoader实例")
        
        return ImageLoader.Builder(applicationContext)
            .apply {
                // 配置网络客户端
                val networkClient = config.customOkHttpClient ?: okHttpClient
                if (networkClient != null) {
                    components {
                        add(OkHttpNetworkFetcherFactory(callFactory = { networkClient }))
                    }
                    LogManager.tag(LogConfig.Tags.IMAGE).d("使用自定义OkHttpClient")
                } else {
                    components {
                        add(OkHttpNetworkFetcherFactory())
                    }
                    LogManager.tag(LogConfig.Tags.IMAGE).d("使用默认OkHttpClient")
                }
                
                // 配置缓存策略
                when (config.cacheStrategy) {
                    ImageConfig.CacheStrategy.MEMORY_ONLY -> {
                        memoryCache { memoryCacheInstance }
                        LogManager.tag(LogConfig.Tags.IMAGE).d("启用仅内存缓存策略")
                    }
                    ImageConfig.CacheStrategy.DISK_ONLY -> {
                        diskCache { diskCacheInstance }
                        LogManager.tag(LogConfig.Tags.IMAGE).d("启用仅磁盘缓存策略")
                    }
                    ImageConfig.CacheStrategy.MEMORY_AND_DISK -> {
                        memoryCache { memoryCacheInstance }
                        diskCache { diskCacheInstance }
                        LogManager.tag(LogConfig.Tags.IMAGE).d("启用内存+磁盘缓存策略")
                    }
                    ImageConfig.CacheStrategy.NO_CACHE -> {
                        LogManager.tag(LogConfig.Tags.IMAGE).d("禁用所有缓存")
                    }
                }
                
                // 配置图片质量
                when (config.imageQuality) {
                    ImageConfig.ImageQuality.LOW -> {
                        // Coil3中通过bitmapConfig配置
                        LogManager.tag(LogConfig.Tags.IMAGE).d("设置低质量图片配置")
                    }
                    ImageConfig.ImageQuality.MEDIUM -> {
                        LogManager.tag(LogConfig.Tags.IMAGE).d("设置中等质量图片配置")
                    }
                    ImageConfig.ImageQuality.HIGH -> {
                        LogManager.tag(LogConfig.Tags.IMAGE).d("设置高质量图片配置")
                    }
                }
                
                // 配置拦截器
                components {
                    // 添加缓存拦截器
                    add(ImageCacheInterceptor())
                    
                    // 添加日志拦截器
                    if (config.enableLogging || ImageConfig.isDebugMode()) {
                        add(ImageLoggingInterceptor())
                    }
                }
                
                // 配置调试日志
                if (ImageConfig.isLoggingEnabled()) {
                    logger(DebugLogger())
                }
                
                // 配置其他选项
                // Coil3中crossfade通过ImageRequest配置，这里暂时移除
                // respectCacheHeaders在Coil3中可能有不同的API
            }
            .build()
    }
    
    /**
     * 获取ImageLoader实例
     */
    fun getImageLoader(): ImageLoader = imageLoaderInstance
    
    /**
     * 获取内存缓存实例
     */
    fun getMemoryCache(): MemoryCache = memoryCacheInstance
    
    /**
     * 获取磁盘缓存实例
     */
    fun getDiskCache(): DiskCache = diskCacheInstance
    
    /**
     * 获取当前配置
     */
    fun getConfig(): ImageConfig.ImageLoadConfig = config
    
    /**
     * 预热ImageLoader
     * 提前初始化关键组件以减少首次加载延迟
     */
    fun warmUp() {
        try {
            LogManager.tag(LogConfig.Tags.IMAGE).d("开始预热ImageLoader")
            
            // 触发懒加载组件的初始化
            memoryCacheInstance
            diskCacheInstance
            imageLoaderInstance
            
            LogManager.tag(LogConfig.Tags.IMAGE).i("ImageLoader预热完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "ImageLoader预热失败")
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        ImageCacheConfig.clearAllCache(memoryCacheInstance, diskCacheInstance)
    }
    
    /**
     * 清理内存缓存
     */
    fun clearMemoryCache() {
        ImageCacheConfig.clearMemoryCache(memoryCacheInstance)
    }
    
    /**
     * 清理磁盘缓存
     */
    fun clearDiskCache() {
        ImageCacheConfig.clearDiskCache(diskCacheInstance)
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): ImageCacheConfig.CacheStats {
        return ImageCacheConfig.getCacheStats(memoryCacheInstance, diskCacheInstance)
    }
    
    /**
     * 检查缓存健康状态
     */
    fun checkCacheHealth(): Boolean {
        return ImageCacheConfig.checkCacheHealth(applicationContext, memoryCacheInstance, diskCacheInstance)
    }
    
    /**
     * 获取缓存使用报告
     */
    fun getCacheUsageReport(): String {
        return ImageCacheConfig.getCacheUsageReport(memoryCacheInstance, diskCacheInstance)
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            imageLoaderInstance.shutdown()
            LogManager.tag(LogConfig.Tags.IMAGE).d("ImageLoader资源已清理")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "清理ImageLoader资源失败")
        }
    }
}
