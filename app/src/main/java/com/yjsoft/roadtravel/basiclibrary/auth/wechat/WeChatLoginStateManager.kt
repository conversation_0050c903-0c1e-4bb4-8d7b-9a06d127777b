package com.yjsoft.roadtravel.basiclibrary.auth.wechat

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 微信登录状态管理器
 * 使用Flow来管理全局的微信登录状态事件
 * 使用单例模式，不依赖Hilt注入，确保在任何情况下都能正常工作
 */
class WeChatLoginStateManager private constructor() {
    
    companion object {
        private const val TAG = "WeChatLoginStateManager %s"

        @Volatile
        private var INSTANCE: WeChatLoginStateManager? = null

        /**
         * 获取单例实例
         */
        fun getInstance(): WeChatLoginStateManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WeChatLoginStateManager().also { INSTANCE = it }
            }
        }
    }
    
    // 私有的可变Flow
    private val _loginStateEvents = MutableSharedFlow<WeChatLoginStateEvent>(
        replay = 0, // 不重放历史事件
        extraBufferCapacity = 1 // 额外缓冲容量
    )
    
    // 公开的只读Flow
    val loginStateEvents: SharedFlow<WeChatLoginStateEvent> = _loginStateEvents.asSharedFlow()
    
    /**
     * 发送登录失败事件
     */
    fun sendLoginFailedEvent(errorMessage: String) {
        try {
            val event = WeChatLoginStateEvent.LoginFailed(errorMessage)
            val result = _loginStateEvents.tryEmit(event)
            LogManager.d(TAG, "发送登录失败事件: $errorMessage, 结果: $result")
        } catch (e: Exception) {
            LogManager.e(TAG, "发送登录失败事件异常", e)
        }
    }
    
    /**
     * 发送登录成功事件
     */
    fun sendLoginSuccessEvent(userInfo: WeChatUserInfo) {
        try {
            val event = WeChatLoginStateEvent.LoginSuccess(userInfo)
            val result = _loginStateEvents.tryEmit(event)
            LogManager.d(TAG, "发送登录成功事件: ${userInfo.nickname}, 结果: $result")
        } catch (e: Exception) {
            LogManager.e(TAG, "发送登录成功事件异常", e)
        }
    }
    
    /**
     * 发送登录取消事件
     */
    fun sendLoginCancelledEvent() {
        try {
            val event = WeChatLoginStateEvent.LoginCancelled
            val result = _loginStateEvents.tryEmit(event)
            LogManager.d(TAG, "发送登录取消事件, 结果: $result")
        } catch (e: Exception) {
            LogManager.e(TAG, "发送登录取消事件异常", e)
        }
    }
    
    /**
     * 发送服务未初始化事件
     */
    fun sendServiceNotInitializedEvent() {
        try {
            val event = WeChatLoginStateEvent.ServiceNotInitialized
            val result = _loginStateEvents.tryEmit(event)
            LogManager.d(TAG, "发送服务未初始化事件, 结果: $result")
        } catch (e: Exception) {
            LogManager.e(TAG, "发送服务未初始化事件异常", e)
        }
    }
}

/**
 * 微信登录状态事件
 */
sealed class WeChatLoginStateEvent {
    /**
     * 登录成功
     */
    data class LoginSuccess(val userInfo: WeChatUserInfo) : WeChatLoginStateEvent()
    
    /**
     * 登录失败
     */
    data class LoginFailed(val errorMessage: String) : WeChatLoginStateEvent()
    
    /**
     * 登录取消
     */
    object LoginCancelled : WeChatLoginStateEvent()
    
    /**
     * 服务未初始化
     */
    object ServiceNotInitialized : WeChatLoginStateEvent()
}
