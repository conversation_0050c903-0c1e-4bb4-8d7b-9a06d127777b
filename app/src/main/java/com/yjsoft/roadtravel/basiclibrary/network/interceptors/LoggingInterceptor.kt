package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import okio.Buffer
import java.io.IOException
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * 智能日志拦截器
 * 根据构建类型智能调整日志级别，添加请求ID追踪
 */
class LoggingInterceptor(
    private val level: Level = Level.BODY,
    private val tag: String = "NetworkRequest"
) : Interceptor {
    
    /**
     * 日志级别
     */
    enum class Level {
        NONE,       // 不记录日志
        BASIC,      // 记录请求和响应行
        HEADERS,    // 记录请求和响应行以及它们各自的头
        BODY        // 记录请求和响应行以及它们各自的头和正文（如果存在）
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        
        if (level == Level.NONE) {
            return chain.proceed(request)
        }
        
        // 生成请求ID用于追踪
        val requestId = generateRequestId()
        val requestWithId = request.newBuilder()
            .header("X-Request-ID", requestId)
            .build()
        
        val startTime = System.nanoTime()
        
        // 记录请求日志
        logRequest(requestWithId, requestId)
        
        val response: Response
        try {
            response = chain.proceed(requestWithId)
        } catch (e: Exception) {
            logError(requestId, e)
            throw e
        }
        
        val endTime = System.nanoTime()
        val duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime)
        
        // 记录响应日志
        return logResponse(response, requestId, duration)
    }
    
    /**
     * 记录请求日志
     */
    private fun logRequest(request: Request, requestId: String) {
        if (level == Level.BASIC || level == Level.HEADERS || level == Level.BODY) {
            val method = request.method
            val url = request.url
            
            log("┌────── Request [$requestId] ──────")
            log("│ $method $url")
            
            if (level == Level.HEADERS || level == Level.BODY) {
                val headers = request.headers
                for (i in 0 until headers.size) {
                    log("│ ${headers.name(i)}: ${headers.value(i)}")
                }
                
                if (level == Level.BODY) {
                    val requestBody = request.body
                    if (requestBody != null) {
                        log("│")
                        if (isPlaintext(requestBody.contentType()?.toString())) {
                            val buffer = Buffer()
                            requestBody.writeTo(buffer)
                            val bodyString = buffer.readUtf8()
                            log("│ Body: $bodyString")
                        } else {
                            log("│ Body: [Binary content, ${requestBody.contentLength()} bytes]")
                        }
                    }
                }
            }
            log("└─────────────────────────────────")
        }
    }
    
    /**
     * 记录响应日志
     */
    private fun logResponse(response: Response, requestId: String, duration: Long): Response {
        if (level == Level.BASIC || level == Level.HEADERS || level == Level.BODY) {
            val code = response.code
            val message = response.message
            val url = response.request.url
            
            log("┌────── Response [$requestId] ──────")
            log("│ $code $message ($duration ms)")
            log("│ $url")
            
            if (level == Level.HEADERS || level == Level.BODY) {
                val headers = response.headers
                for (i in 0 until headers.size) {
                    log("│ ${headers.name(i)}: ${headers.value(i)}")
                }
                
                if (level == Level.BODY) {
                    val responseBody = response.body
                    if (responseBody != null) {
                        log("│")
                        if (isPlaintext(responseBody.contentType()?.toString())) {
                            val bodyString = responseBody.string()
                            log("│ Body: $bodyString")
                            
                            // 重新创建ResponseBody，因为已经读取过了
                            val newResponseBody = bodyString.toResponseBody(responseBody.contentType())
                            val newResponse = response.newBuilder()
                                .body(newResponseBody)
                                .build()
                            
                            log("└─────────────────────────────────")
                            return newResponse
                        } else {
                            log("│ Body: [Binary content, ${responseBody.contentLength()} bytes]")
                        }
                    }
                }
            }
            log("└─────────────────────────────────")
        }
        
        return response
    }
    
    /**
     * 记录错误日志
     */
    private fun logError(requestId: String, exception: Exception) {
        log("┌────── Error [$requestId] ──────")
        log("│ ${exception.javaClass.simpleName}: ${exception.message}")
        log("└─────────────────────────────────")
    }
    
    /**
     * 生成请求ID
     */
    private fun generateRequestId(): String {
        return UUID.randomUUID().toString().substring(0, 8)
    }
    
    /**
     * 判断是否为纯文本内容
     */
    private fun isPlaintext(mediaType: String?): Boolean {
        if (mediaType == null) return false
        
        return mediaType.startsWith("text/") ||
                mediaType.contains("json") ||
                mediaType.contains("xml") ||
                mediaType.contains("x-www-form-urlencoded")
    }
    
    /**
     * 输出日志
     */
    private fun log(message: String) {
        if (NetworkConfig.isDebugMode()) {
            // 使用新的日志框架，带网络标签
            LogManager.tag(LogConfig.Tags.NETWORK).d(message)
        }
    }
    
    companion object {
        /**
         * 根据调试模式创建日志拦截器
         */
        fun create(isDebug: Boolean = NetworkConfig.isDebugMode()): LoggingInterceptor {
            return LoggingInterceptor(
                level = if (isDebug) Level.BODY else Level.BASIC
            )
        }
        
        /**
         * 创建生产环境日志拦截器
         */
        fun createForProduction(): LoggingInterceptor {
            return LoggingInterceptor(Level.BASIC)
        }
        
        /**
         * 创建开发环境日志拦截器
         */
        fun createForDevelopment(): LoggingInterceptor {
            return LoggingInterceptor(Level.BODY)
        }
        
        /**
         * 创建无日志拦截器
         */
        fun createNone(): LoggingInterceptor {
            return LoggingInterceptor(Level.NONE)
        }
    }
}
