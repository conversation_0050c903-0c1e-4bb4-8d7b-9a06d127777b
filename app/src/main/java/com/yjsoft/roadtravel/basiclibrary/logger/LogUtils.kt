package com.yjsoft.roadtravel.basiclibrary.logger

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

/**
 * 日志工具类
 * 提供日志文件管理、导出、统计等功能
 */
object LogUtils {
    
    private const val LOG_FILE_PROVIDER_AUTHORITY = "com.yjsoft.roadtravel.fileprovider"
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    /**
     * 日志统计信息
     */
    data class LogStatistics(
        val totalLogFiles: Int,
        val totalCrashFiles: Int,
        val totalLogSize: Long,
        val oldestLogDate: Date?,
        val newestLogDate: Date?
    )
    
    /**
     * 获取日志统计信息
     */
    fun getLogStatistics(context: Context): LogStatistics {
        val logFiles = getLogFiles(context)
        val crashFiles = getCrashFiles(context)
        
        val allFiles = logFiles + crashFiles
        val totalSize = allFiles.sumOf { it.length() }
        
        val dates = allFiles.map { Date(it.lastModified()) }
        val oldestDate = dates.minOrNull()
        val newestDate = dates.maxOrNull()
        
        return LogStatistics(
            totalLogFiles = logFiles.size,
            totalCrashFiles = crashFiles.size,
            totalLogSize = totalSize,
            oldestLogDate = oldestDate,
            newestLogDate = newestDate
        )
    }
    
    /**
     * 获取所有日志文件
     */
    fun getLogFiles(context: Context): List<File> {
        val logDir = LogConfig.FileLog.getLogDir(context.filesDir)
        return logDir.listFiles { _, name ->
            name.startsWith(LogConfig.FileLog.LOG_FILE_PREFIX) &&
                    name.endsWith(LogConfig.FileLog.LOG_FILE_EXTENSION)
        }?.toList() ?: emptyList()
    }
    
    /**
     * 获取所有崩溃日志文件
     */
    fun getCrashFiles(context: Context): List<File> {
        val crashDir = File(context.filesDir, "crashes")
        return crashDir.listFiles { _, name ->
            name.startsWith(LogConfig.CrashLog.CRASH_FILE_PREFIX)
        }?.toList() ?: emptyList()
    }
    
    /**
     * 清理所有日志文件
     */
    fun clearAllLogs(context: Context): Boolean {
        return try {
            val logFiles = getLogFiles(context)
            val crashFiles = getCrashFiles(context)
            
            var success = true
            (logFiles + crashFiles).forEach { file ->
                if (!file.delete()) {
                    success = false
                    Timber.w("删除日志文件失败: ${file.name}")
                }
            }
            
            if (success) {
                Timber.i("所有日志文件已清理")
            }
            success
        } catch (e: Exception) {
            Timber.e(e, "清理日志文件时发生错误")
            false
        }
    }
    
    /**
     * 清理指定天数之前的日志文件
     */
    fun clearLogsOlderThan(context: Context, days: Int): Int {
        val cutoffTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L)
        val logFiles = getLogFiles(context)
        val crashFiles = getCrashFiles(context)
        
        var deletedCount = 0
        (logFiles + crashFiles).forEach { file ->
            if (file.lastModified() < cutoffTime) {
                if (file.delete()) {
                    deletedCount++
                    Timber.d("删除过期日志文件: ${file.name}")
                }
            }
        }
        
        Timber.i("清理了 $deletedCount 个过期日志文件")
        return deletedCount
    }
    
    /**
     * 导出日志文件为ZIP
     */
    fun exportLogsAsZip(context: Context): File? {
        return try {
            val logFiles = getLogFiles(context)
            val crashFiles = getCrashFiles(context)
            val allFiles = logFiles + crashFiles
            
            if (allFiles.isEmpty()) {
                Timber.w("没有日志文件可导出")
                return null
            }
            
            val exportDir = File(context.getExternalFilesDir(null), "exports").apply {
                if (!exists()) {
                    mkdirs()
                }
            }
            
            val zipFileName = "logs_${dateFormat.format(Date())}.zip"
            val zipFile = File(exportDir, zipFileName)
            
            ZipOutputStream(FileOutputStream(zipFile)).use { zipOut ->
                allFiles.forEach { file ->
                    FileInputStream(file).use { fileIn ->
                        val zipEntry = ZipEntry(file.name)
                        zipOut.putNextEntry(zipEntry)
                        
                        val buffer = ByteArray(1024)
                        var length: Int
                        while (fileIn.read(buffer).also { length = it } > 0) {
                            zipOut.write(buffer, 0, length)
                        }
                        zipOut.closeEntry()
                    }
                }
            }
            
            Timber.i("日志文件已导出到: ${zipFile.absolutePath}")
            zipFile
            
        } catch (e: IOException) {
            Timber.e(e, "导出日志文件失败")
            null
        }
    }
    
    /**
     * 分享日志文件
     */
    fun shareLogFiles(context: Context) {
        val zipFile = exportLogsAsZip(context)
        if (zipFile == null) {
            Timber.w("无法分享日志文件：导出失败")
            return
        }
        
        try {
            val uri = FileProvider.getUriForFile(
                context,
                LOG_FILE_PROVIDER_AUTHORITY,
                zipFile
            )
            
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "application/zip"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, "应用日志文件")
                putExtra(Intent.EXTRA_TEXT, "这是应用的日志文件，包含了运行日志和崩溃信息。")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            val chooserIntent = Intent.createChooser(shareIntent, "分享日志文件")
            chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(chooserIntent)
            
            Timber.i("日志文件分享Intent已启动")
            
        } catch (e: Exception) {
            Timber.e(e, "分享日志文件失败")
        }
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format(java.util.Locale.getDefault(), "%.2f %s", size, units[unitIndex])
    }
    
    /**
     * 获取日志目录大小
     */
    fun getLogDirectorySize(context: Context): Long {
        val logFiles = getLogFiles(context)
        val crashFiles = getCrashFiles(context)
        return (logFiles + crashFiles).sumOf { it.length() }
    }
    
    /**
     * 检查日志目录是否存在
     */
    fun isLogDirectoryExists(context: Context): Boolean {
        val logDir = LogConfig.FileLog.getLogDir(context.filesDir)
        val crashDir = File(context.filesDir, "crashes")
        return logDir.exists() || crashDir.exists()
    }
    
    /**
     * 创建日志目录
     */
    fun createLogDirectories(context: Context): Boolean {
        return try {
            val logDir = LogConfig.FileLog.getLogDir(context.filesDir)
            val crashDir = File(context.filesDir, "crashes")
            
            val logDirCreated = logDir.exists() || logDir.mkdirs()
            val crashDirCreated = crashDir.exists() || crashDir.mkdirs()
            
            logDirCreated && crashDirCreated
        } catch (e: Exception) {
            Timber.e(e, "创建日志目录失败")
            false
        }
    }
    
    /**
     * 获取最新的崩溃日志文件
     */
    fun getLatestCrashFile(context: Context): File? {
        val crashFiles = getCrashFiles(context)
        return crashFiles.maxByOrNull { it.lastModified() }
    }
    
    /**
     * 读取文件内容（限制大小以避免内存问题）
     */
    fun readFileContent(file: File, maxSize: Int = 1024 * 1024): String? {
        return try {
            if (file.length() > maxSize) {
                "文件过大，无法完整显示（大小: ${formatFileSize(file.length())}）"
            } else {
                file.readText()
            }
        } catch (e: Exception) {
            Timber.e(e, "读取文件内容失败: ${file.name}")
            null
        }
    }
}
