package com.yjsoft.roadtravel.basiclibrary.navigation.compose

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants

/**
 * Activity内部的Compose导航组件
 * 专门处理单个Activity内部的页面跳转
 * 注意：这不是用来管理Activity间跳转的，Activity间跳转请使用NavigationManager
 */
@Composable
fun InternalNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = NavigationConstants.Routes.MAIN_HOME,
    content: Map<String, @Composable () -> Unit>
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        // 动态注册页面
        content.forEach { (route, pageContent) ->
            composable(route) {
                pageContent()
            }
        }
    }
}

/**
 * WebView页面组件（Activity内部使用）
 * 用于在Activity内部嵌入WebView页面
 */
@Composable
fun InternalWebViewScreen(
    url: String,
    title: String?,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 在后续步骤中实现完整的WebView组件
    Column(modifier = modifier) {
        Text(
            text = "内部WebView页面 - $url",
            modifier = Modifier.padding(16.dp)
        )
        title?.let {
            Text(
                text = "标题: $it",
                modifier = Modifier.padding(16.dp)
            )
        }
        Button(
            onClick = onNavigateBack,
            modifier = Modifier.padding(16.dp)
        ) {
            Text("返回")
        }
    }
}

/**
 * 简单的页面容器
 * 用于快速创建简单的页面内容
 */
@Composable
fun SimplePage(
    title: String,
    content: @Composable () -> Unit = {},
    onNavigateBack: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.padding(16.dp)) {
        Text(
            text = title,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        content()
        
        onNavigateBack?.let { callback ->
            Button(
                onClick = callback,
                modifier = Modifier.padding(top = 16.dp)
            ) {
                Text("返回")
            }
        }
    }
} 