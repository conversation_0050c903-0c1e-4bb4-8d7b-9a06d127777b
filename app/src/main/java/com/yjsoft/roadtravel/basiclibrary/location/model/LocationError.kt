package com.yjsoft.roadtravel.basiclibrary.location.model

import com.amap.api.location.AMapLocation

/**
 * 定位错误类型
 */
sealed class LocationError(
    val code: Int,
    val message: String,
    val description: String
) {
    /**
     * 权限被拒绝
     */
    data class PermissionDenied(
        val deniedPermissions: List<String> = emptyList()
    ) : LocationError(
        code = ERROR_PERMISSION_DENIED,
        message = "位置权限被拒绝",
        description = "应用需要位置权限才能提供定位服务"
    )
    
    /**
     * GPS未开启
     */
    object GPSDisabled : LocationError(
        code = ERROR_GPS_DISABLED,
        message = "GPS未开启",
        description = "请在设置中开启GPS定位服务"
    )
    
    /**
     * 网络不可用
     */
    object NetworkUnavailable : LocationError(
        code = ERROR_NETWORK_UNAVAILABLE,
        message = "网络不可用",
        description = "定位服务需要网络连接，请检查网络设置"
    )
    
    /**
     * 定位服务不可用
     */
    object ServiceUnavailable : LocationError(
        code = ERROR_SERVICE_UNAVAILABLE,
        message = "定位服务不可用",
        description = "定位服务暂时不可用，请稍后重试"
    )
    
    /**
     * 定位超时
     */
    data class Timeout(
        val timeoutMillis: Long
    ) : LocationError(
        code = ERROR_TIMEOUT,
        message = "定位超时",
        description = "定位请求超时，请检查网络连接或GPS信号"
    )
    
    /**
     * 高德定位错误
     */
    data class AMapError(
        val aMapErrorCode: Int,
        val aMapErrorInfo: String
    ) : LocationError(
        code = aMapErrorCode,
        message = "定位失败",
        description = getAMapErrorDescription(aMapErrorCode, aMapErrorInfo)
    )
    
    /**
     * 未知错误
     */
    data class Unknown(
        val throwable: Throwable? = null
    ) : LocationError(
        code = ERROR_UNKNOWN,
        message = "未知错误",
        description = throwable?.message ?: "发生未知错误"
    )
    
    companion object {
        // 错误代码常量
        const val ERROR_PERMISSION_DENIED = -1001
        const val ERROR_GPS_DISABLED = -1002
        const val ERROR_NETWORK_UNAVAILABLE = -1003
        const val ERROR_SERVICE_UNAVAILABLE = -1004
        const val ERROR_TIMEOUT = -1005
        const val ERROR_UNKNOWN = -1999
        
        /**
         * 创建权限被拒绝错误
         */
        fun permissionDenied(deniedPermissions: List<String> = emptyList()): LocationError {
            return PermissionDenied(deniedPermissions)
        }
        
        /**
         * 创建GPS未开启错误
         */
        fun gpsDisabled(): LocationError {
            return GPSDisabled
        }
        
        /**
         * 创建网络不可用错误
         */
        fun networkUnavailable(): LocationError {
            return NetworkUnavailable
        }
        
        /**
         * 创建服务不可用错误
         */
        fun serviceUnavailable(): LocationError {
            return ServiceUnavailable
        }
        
        /**
         * 创建超时错误
         */
        fun timeout(timeoutMillis: Long): LocationError {
            return Timeout(timeoutMillis)
        }
        
        /**
         * 从高德定位错误创建
         */
        fun fromAMapLocation(aMapLocation: AMapLocation): LocationError {
            return AMapError(
                aMapErrorCode = aMapLocation.errorCode,
                aMapErrorInfo = aMapLocation.errorInfo
            )
        }
        
        /**
         * 创建未知错误
         */
        fun unknown(throwable: Throwable? = null): LocationError {
            return Unknown(throwable)
        }
        
        /**
         * 获取高德错误描述
         */
        private fun getAMapErrorDescription(errorCode: Int, errorInfo: String): String {
            return when (errorCode) {
                AMapLocation.ERROR_CODE_INVALID_PARAMETER -> "请求参数无效"
                AMapLocation.ERROR_CODE_FAILURE_WIFI_INFO -> "获取WiFi信息失败"
                AMapLocation.ERROR_CODE_FAILURE_LOCATION_PARAMETER -> "定位参数错误"
                AMapLocation.ERROR_CODE_FAILURE_CONNECTION -> "网络连接异常"
                AMapLocation.ERROR_CODE_FAILURE_PARSER -> "数据解析异常"
                AMapLocation.ERROR_CODE_FAILURE_LOCATION -> "定位失败"
                AMapLocation.ERROR_CODE_FAILURE_AUTH -> "Key鉴权失败"
                AMapLocation.ERROR_CODE_UNKNOWN -> "其他错误"
                AMapLocation.ERROR_CODE_FAILURE_INIT -> "初始化异常"
                AMapLocation.ERROR_CODE_SERVICE_FAIL -> "定位服务启动失败"
                AMapLocation.ERROR_CODE_FAILURE_CELL -> "获取基站信息失败"
                AMapLocation.ERROR_CODE_FAILURE_LOCATION_PERMISSION -> "缺少定位权限"
                AMapLocation.ERROR_CODE_FAILURE_NOWIFIANDAP -> "无法获取WiFi信息"
                AMapLocation.ERROR_CODE_FAILURE_NOENOUGHSATELLITES -> "卫星数不够"
                AMapLocation.ERROR_CODE_FAILURE_SIMULATION_LOCATION -> "检测到模拟位置"
                AMapLocation.ERROR_CODE_AIRPLANEMODE_WIFIOFF -> "飞行模式下关闭WiFi"
                AMapLocation.ERROR_CODE_NOCGI_WIFIOFF -> "没有检查到SIM卡且关闭WiFi"
                else -> errorInfo.ifEmpty { "定位失败，错误代码：$errorCode" }
            }
        }
    }
    
    /**
     * 是否为可重试的错误
     */
    val isRetryable: Boolean
        get() = when (this) {
            is PermissionDenied -> false
            is GPSDisabled -> false
            is NetworkUnavailable -> true
            is ServiceUnavailable -> true
            is Timeout -> true
            is AMapError -> when (aMapErrorCode) {
                AMapLocation.ERROR_CODE_FAILURE_CONNECTION,
                AMapLocation.ERROR_CODE_FAILURE_LOCATION,
                AMapLocation.ERROR_CODE_SERVICE_FAIL -> true
                else -> false
            }
            is Unknown -> true
        }
    
    /**
     * 获取用户友好的错误提示
     */
    val userFriendlyMessage: String
        get() = when (this) {
            is PermissionDenied -> "请授予位置权限以使用定位功能"
            is GPSDisabled -> "请开启GPS定位服务"
            is NetworkUnavailable -> "请检查网络连接"
            is ServiceUnavailable -> "定位服务暂时不可用，请稍后重试"
            is Timeout -> "定位超时，请检查GPS信号或网络连接"
            is AMapError -> description
            is Unknown -> "定位失败，请重试"
        }
}
