package com.yjsoft.roadtravel.basiclibrary.payment

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.OnBackPressedCallback
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationManager
import com.yjsoft.roadtravel.basiclibrary.payment.demo.PaymentDemoScreen
import com.yjsoft.roadtravel.ui.components.GradientBackground
import com.yjsoft.roadtravel.ui.components.TitleAlignment
import com.yjsoft.roadtravel.ui.components.TitleBar
import com.yjsoft.roadtravel.ui.theme.RoadTravelTheme
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity

/**
 * 独立的支付演示Activity
 * 包含完整的支付功能演示和自己的UI结构
 */
class PaymentActivity : BaseActivity() {
    
    companion object {
        private const val TAG = "PaymentActivity %s"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 设置现代化的返回键处理
        setupBackPressedHandler()
        
        // 获取传入参数
        val fromActivity = intent.getStringExtra(NavigationConstants.Params.FROM) ?: "Unknown"
        
        LogManager.d(TAG, "支付演示Activity启动，来源：$fromActivity")
        
        setContent {
            RoadTravelTheme {
                GradientBackground {
                    Scaffold(
                        modifier = Modifier.fillMaxSize(),
                        containerColor = Color.Transparent,
                        topBar = {
                            TitleBar(
                                title = "支付框架演示",
                                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                                titleAlignment = TitleAlignment.CENTER,
                                backgroundColor = Color.Transparent,
                                contentColor = Color.Black,
                                onLeftIconClick = {
                                    handleBackPressed()
                                }
                            )
                        }
                    ) { innerPadding ->
                        PaymentDemoScreen(
                            modifier = Modifier.padding(
                                innerPadding
                            ),
                            onNavigateBack = {
                                handleBackPressed()
                            }
                        )
                    }
                }
            }
        }
    }
    
    /**
     * 设置现代化的返回键处理
     */
    private fun setupBackPressedHandler() {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }
    
    private fun handleBackPressed() {
        // 可以在这里返回结果给调用方
        NavigationManager.finishWithResult(
            activity = this,
            resultCode = NavigationConstants.ResultCodes.PAYMENT_SUCCESS
        )
    }
} 