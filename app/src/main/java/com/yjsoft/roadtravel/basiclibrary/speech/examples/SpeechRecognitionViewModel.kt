package com.yjsoft.roadtravel.basiclibrary.speech.examples

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 语音识别ViewModel示例
 * 展示如何在MVVM架构中使用语音识别功能
 */
@HiltViewModel
class SpeechRecognitionViewModel @Inject constructor(
    private val speechManager: SpeechRecognitionManager
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(SpeechUiState())
    val uiState: StateFlow<SpeechUiState> = _uiState.asStateFlow()
    
    // 识别历史
    private val _recognitionHistory = MutableStateFlow<List<SpeechRecognitionResult>>(emptyList())
    val recognitionHistory: StateFlow<List<SpeechRecognitionResult>> = _recognitionHistory.asStateFlow()
    
    init {
        // 监听语音识别状态
        viewModelScope.launch {
            speechManager.state.collect { state ->
                _uiState.value = _uiState.value.copy(
                    recognitionState = state,
                    isRecording = state == SpeechRecognitionState.RECORDING
                )
            }
        }
        
        // 监听识别结果
        viewModelScope.launch {
            speechManager.result.collect { result ->
                if (result.isSuccess && result.isFinal) {
                    _uiState.value = _uiState.value.copy(
                        currentText = result.text,
                        errorMessage = ""
                    )
                    
                    // 添加到历史记录
                    val currentHistory = _recognitionHistory.value
                    _recognitionHistory.value = listOf(result) + currentHistory
                }
            }
        }
        
        // 监听录音状态
        viewModelScope.launch {
            speechManager.audioState.collect { audioState ->
                _uiState.value = _uiState.value.copy(
                    volume = audioState.volume,
                    recordingDuration = audioState.duration,
                    recordingProgress = audioState.progress
                )
            }
        }
        
        // 监听错误
        viewModelScope.launch {
            speechManager.error.collect { error ->
                _uiState.value = _uiState.value.copy(
                    errorMessage = error.message,
                    isLoading = false
                )
            }
        }
    }
    
    /**
     * 开始语音识别
     */
    fun startRecognition(params: SpeechRecognitionParams = SpeechRecognitionParams()) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    errorMessage = ""
                )
                
                // 检查权限
                if (!speechManager.hasRecordPermission()) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "需要录音权限",
                        isLoading = false
                    )
                    return@launch
                }
                
                // 初始化SDK（如果需要）
                if (!speechManager.isSDKInitialized()) {
                    val success = speechManager.initialize()
                    if (!success) {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "语音识别初始化失败",
                            isLoading = false
                        )
                        return@launch
                    }
                }
                
                // 开始识别
                val success = speechManager.startRecognition(params)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "启动语音识别失败",
                        isLoading = false
                    )
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "启动语音识别异常: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        speechManager.stopRecognition()
        _uiState.value = _uiState.value.copy(isLoading = false)
    }
    
    /**
     * 取消语音识别
     */
    fun cancelRecognition() {
        speechManager.cancelRecognition()
        _uiState.value = _uiState.value.copy(isLoading = false)
    }
    
    /**
     * 重置状态
     */
    fun reset() {
        speechManager.reset()
        _uiState.value = _uiState.value.copy(
            currentText = "",
            errorMessage = "",
            isLoading = false,
            volume = 0f,
            recordingDuration = 0L,
            recordingProgress = 0f
        )
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = "")
    }
    
    /**
     * 清除历史记录
     */
    fun clearHistory() {
        _recognitionHistory.value = emptyList()
    }
    
    /**
     * 删除历史记录项
     */
    fun removeHistoryItem(result: SpeechRecognitionResult) {
        val currentHistory = _recognitionHistory.value
        _recognitionHistory.value = currentHistory.filter { it.timestamp != result.timestamp }
    }
    
    /**
     * 获取当前状态
     */
    fun getCurrentState(): SpeechRecognitionState {
        return speechManager.getCurrentState()
    }
    
    /**
     * 检查是否有录音权限
     */
    fun hasRecordPermission(): Boolean {
        return speechManager.hasRecordPermission()
    }
    
    override fun onCleared() {
        super.onCleared()
        // ViewModel销毁时重置状态
        speechManager.reset()
    }
}

/**
 * 语音识别UI状态
 */
data class SpeechUiState(
    val recognitionState: SpeechRecognitionState = SpeechRecognitionState.IDLE,
    val currentText: String = "",
    val errorMessage: String = "",
    val isLoading: Boolean = false,
    val isRecording: Boolean = false,
    val volume: Float = 0f,
    val recordingDuration: Long = 0L,
    val recordingProgress: Float = 0f
) {
    /**
     * 是否可以开始识别
     */
    val canStartRecognition: Boolean
        get() = recognitionState == SpeechRecognitionState.IDLE && !isLoading
    
    /**
     * 是否可以停止识别
     */
    val canStopRecognition: Boolean
        get() = recognitionState == SpeechRecognitionState.RECORDING || 
                recognitionState == SpeechRecognitionState.PROCESSING
    
    /**
     * 是否有错误
     */
    val hasError: Boolean
        get() = errorMessage.isNotEmpty()
    
    /**
     * 是否有结果
     */
    val hasResult: Boolean
        get() = currentText.isNotEmpty()
}
