package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 语音识别模块的Hilt依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {
    
    /**
     * 提供语音识别管理器单例
     */
    @Provides
    @Singleton
    fun provideSpeechRecognitionManager(
        @ApplicationContext context: Context
    ): SpeechRecognitionManager {
        return SpeechRecognitionManager.getInstance(context)
    }
}
