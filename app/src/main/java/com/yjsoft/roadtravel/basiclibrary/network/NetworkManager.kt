package com.yjsoft.roadtravel.basiclibrary.network

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.auth.DefaultTokenProvider
import com.yjsoft.roadtravel.basiclibrary.network.config.Environment
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import com.yjsoft.roadtravel.basiclibrary.network.proxy.ProxyManager

/**
 * 网络框架管理器
 * 统一管理网络框架的初始化、配置和生命周期
 */
object NetworkManager {
    
    private var isInitialized = false
    private var tokenProvider: TokenProvider? = null
    
    /**
     * 初始化网络框架核心组件（轻量级，不预初始化RetrofitInstance）
     * @param context 应用上下文
     * @param customTokenProvider 自定义Token提供者
     * @param environment 网络环境，默认根据调试模式自动选择
     */
    fun initCore(
        context: Context,
        customTokenProvider: TokenProvider? = null,
        environment: Environment? = null
    ) {
        if (isInitialized) {
            LogManager.tag(LogConfig.Tags.NETWORK).w("NetworkManager已经初始化，跳过重复初始化")
            return
        }

        try {
            LogManager.tag(LogConfig.Tags.NETWORK).d("开始初始化网络框架核心组件")

            // 1. 设置网络环境
            val targetEnvironment = environment ?: getDefaultEnvironment(context)
            NetworkConfig.setEnvironment(targetEnvironment)
            LogManager.tag(LogConfig.Tags.NETWORK).i("网络环境设置为: ${targetEnvironment.name}")

            // 2. 初始化Token提供者
            tokenProvider = (customTokenProvider ?: DefaultTokenProvider()) as TokenProvider?
            LogManager.tag(LogConfig.Tags.NETWORK).d("Token提供者初始化完成")

            // 3. 标记为已初始化（不预初始化RetrofitInstance）
            isInitialized = true
            LogManager.tag(LogConfig.Tags.NETWORK).i("网络框架核心组件初始化完成")

        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络框架核心组件初始化失败")
            throw e
        }
    }

    /**
     * 初始化网络框架（完整版，包含预初始化）
     * @param context 应用上下文
     * @param environment 网络环境，默认根据调试模式自动选择
     * @param customTokenProvider 自定义Token提供者，为null时使用默认实现
     */
    fun init(
        context: Context,
        environment: Environment? = null,
        customTokenProvider: TokenProvider? = null
    ) {
        // 先初始化核心组件
        initCore(context, customTokenProvider, environment)

        try {
            // 预初始化RetrofitInstance（可选，用于预热）
            if (shouldPreInitialize()) {
                preInitializeRetrofit(context)
            }

            LogManager.tag(LogConfig.Tags.NETWORK).i("网络框架完整初始化完成")

        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络框架完整初始化失败")
            throw e
        }
    }
    
    /**
     * 获取RetrofitInstance实例
     * @param context 上下文
     * @return RetrofitInstance实例
     */
    fun getRetrofitInstance(context: Context): RetrofitInstance {
        if (!isInitialized) {
            LogManager.tag(LogConfig.Tags.NETWORK).w("网络框架未初始化，使用默认配置")
            init(context)
        }
        
        return RetrofitInstance.getInstance(context, tokenProvider)
    }
    
    /**
     * 获取API服务实例
     * @param context 上下文
     * @return ApiService实例
     */
    fun getApiService(context: Context): ApiService {
        return getRetrofitInstance(context).api
    }
    
    /**
     * 切换网络环境
     * @param context 上下文
     * @param environment 新的网络环境
     */
    fun switchEnvironment(context: Context, environment: Environment) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("切换网络环境: ${NetworkConfig.getCurrentEnvironment().name} -> ${environment.name}")
        
        // 重置RetrofitInstance
        RetrofitInstance.resetInstance()
        
        // 设置新环境
        NetworkConfig.setEnvironment(environment)
        
        // 重新初始化
        if (shouldPreInitialize()) {
            preInitializeRetrofit(context)
        }
        
        LogManager.tag(LogConfig.Tags.NETWORK).i("网络环境切换完成")
    }
    
    /**
     * 更新Token提供者
     * @param context 上下文
     * @param newTokenProvider 新的Token提供者
     */
    fun updateTokenProvider(context: Context, newTokenProvider: TokenProvider) {
        LogManager.tag(LogConfig.Tags.NETWORK).d("更新Token提供者")
        
        tokenProvider = newTokenProvider
        
        // 重置RetrofitInstance以使用新的TokenProvider
        RetrofitInstance.resetInstance()
        
        // 重新初始化
        if (shouldPreInitialize()) {
            preInitializeRetrofit(context)
        }
    }
    
    /**
     * 清理网络框架资源
     */
    fun cleanup() {
        LogManager.tag(LogConfig.Tags.NETWORK).i("清理网络框架资源")
        
        try {
            RetrofitInstance.resetInstance()
            tokenProvider = null
            isInitialized = false
            
            LogManager.tag(LogConfig.Tags.NETWORK).d("网络框架资源清理完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "清理网络框架资源时发生错误")
        }
    }
    
    /**
     * 检查网络框架是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 获取当前Token提供者
     */
    fun getTokenProvider(): TokenProvider? = tokenProvider
    
    /**
     * 根据调试模式获取默认环境
     */
    private fun getDefaultEnvironment(context: Context): Environment {
        val isDebug = try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
        
        return if (isDebug) Environment.DEVELOPMENT else Environment.PRODUCTION
    }
    
    /**
     * 是否应该预初始化Retrofit
     * 在调试模式下预初始化，生产模式下保持懒加载
     */
    private fun shouldPreInitialize(): Boolean {
        return NetworkConfig.isDebugMode()
    }
    
    /**
     * 预初始化Retrofit实例
     */
    private fun preInitializeRetrofit(context: Context) {
        try {
            LogManager.tag(LogConfig.Tags.NETWORK).d("开始预初始化Retrofit实例")
            
            // 触发RetrofitInstance的初始化
            val instance = RetrofitInstance.getInstance(context, tokenProvider)
            
            // 预热关键组件
            instance.getOkHttpClient()
            instance.getGson()
            
            LogManager.tag(LogConfig.Tags.NETWORK).d("Retrofit实例预初始化完成")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "Retrofit实例预初始化失败，将使用懒加载")
        }
    }
    
    /**
     * 获取网络框架状态信息
     */
    fun getStatus(): NetworkStatus {
        return NetworkStatus(
            isInitialized = isInitialized,
            currentEnvironment = if (isInitialized) NetworkConfig.getCurrentEnvironment() else null,
            hasTokenProvider = tokenProvider != null,
            baseUrl = if (isInitialized) NetworkConfig.getBaseUrl() else null
        )
    }

    /**
     * 启用直连模式（绕过系统代理）
     * @param context 上下文
     */
    fun enableDirectConnection(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("启用直连模式")

        NetworkConfig.setDirectConnectionEnabled(true)

        // 重置RetrofitInstance以应用新的代理配置
        RetrofitInstance.resetInstance()

        // 记录代理状态
        ProxyManager.logProxyStatus(context)

        // 重新初始化
        if (shouldPreInitialize()) {
            preInitializeRetrofit(context)
        }
    }

    /**
     * 禁用直连模式（使用系统代理）
     * @param context 上下文
     */
    fun disableDirectConnection(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("禁用直连模式，使用系统代理")

        NetworkConfig.setDirectConnectionEnabled(false)

        // 重置RetrofitInstance以应用新的代理配置
        RetrofitInstance.resetInstance()

        // 记录代理状态
        ProxyManager.logProxyStatus(context)

        // 重新初始化
        if (shouldPreInitialize()) {
            preInitializeRetrofit(context)
        }
    }

    /**
     * 检查系统代理状态并记录日志
     * @param context 上下文
     */
    fun checkProxyStatus(context: Context) {
        ProxyManager.logProxyStatus(context)
    }
}

/**
 * 网络框架状态信息
 */
data class NetworkStatus(
    val isInitialized: Boolean,
    val currentEnvironment: Environment?,
    val hasTokenProvider: Boolean,
    val baseUrl: String?
)
