package com.yjsoft.roadtravel.basiclibrary.auth.wechat

import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.datastore.core.WeChatUserInfoData
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 微信用户管理器
 * 提供微信用户信息的统一管理接口
 */
class WeChatUserManager private constructor() {
    
    companion object {
        private const val TAG = "WeChatUserManager"
        
        @Volatile
        private var INSTANCE: WeChatUserManager? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(): WeChatUserManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WeChatUserManager().also { INSTANCE = it }
            }
        }
    }
    
    private val dataStoreRepository: DataStoreRepository by lazy {
        DataStoreRepository.getInstance()
    }
    
    /**
     * 检查用户是否已通过微信登录
     */
    suspend fun isWeChatLoggedIn(): Boolean {
        return try {
            val openId = dataStoreRepository.getWeChatOpenId()
            val isLoggedIn = dataStoreRepository.isLoggedIn()
            openId.isNotEmpty() && isLoggedIn
        } catch (e: Exception) {
            LogManager.e(TAG, "检查微信登录状态失败", e)
            false
        }
    }
    
    /**
     * 获取当前微信用户信息
     */
    suspend fun getCurrentWeChatUser(): WeChatUserInfoData? {
        return try {
            dataStoreRepository.getWeChatUserInfo()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取当前微信用户信息失败", e)
            null
        }
    }
    
    /**
     * 获取微信用户信息流
     */
    fun getCurrentWeChatUserFlow(): Flow<WeChatUserInfoData?> {
        return dataStoreRepository.getValueFlow(
            com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys.WECHAT_OPEN_ID
        ).map { openId ->
            if (openId.isNotEmpty()) {
                dataStoreRepository.getWeChatUserInfo()
            } else {
                null
            }
        }
    }
    
    /**
     * 获取微信用户昵称
     */
    suspend fun getWeChatNickname(): String {
        return try {
            dataStoreRepository.getWeChatNickname()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信昵称失败", e)
            ""
        }
    }
    
    /**
     * 获取微信用户头像URL
     */
    suspend fun getWeChatAvatar(): String {
        return try {
            dataStoreRepository.getWeChatAvatar()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信头像失败", e)
            ""
        }
    }
    
    /**
     * 获取微信OpenID
     */
    suspend fun getWeChatOpenId(): String {
        return try {
            dataStoreRepository.getWeChatOpenId()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信OpenID失败", e)
            ""
        }
    }
    
    /**
     * 获取微信UnionID
     */
    suspend fun getWeChatUnionId(): String {
        return try {
            dataStoreRepository.getWeChatUnionId()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信UnionID失败", e)
            ""
        }
    }
    
    /**
     * 检查微信访问令牌是否过期
     */
    suspend fun isWeChatTokenExpired(): Boolean {
        return try {
            dataStoreRepository.isWeChatTokenExpired()
        } catch (e: Exception) {
            LogManager.e(TAG, "检查微信令牌过期状态失败", e)
            true // 出错时认为已过期
        }
    }
    
    /**
     * 获取微信访问令牌
     */
    suspend fun getWeChatAccessToken(): String {
        return try {
            val token = dataStoreRepository.getWeChatAccessToken()
            if (dataStoreRepository.isWeChatTokenExpired()) {
                LogManager.w(TAG, "微信访问令牌已过期")
                ""
            } else {
                token
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信访问令牌失败", e)
            ""
        }
    }
    
    /**
     * 更新微信访问令牌
     */
    suspend fun updateWeChatAccessToken(
        accessToken: String,
        refreshToken: String?,
        expiresIn: Int
    ): Boolean {
        return try {
            dataStoreRepository.updateWeChatAccessToken(accessToken, refreshToken, expiresIn)
            LogManager.d(TAG, "微信访问令牌更新成功")
            true
        } catch (e: Exception) {
            LogManager.e(TAG, "更新微信访问令牌失败", e)
            false
        }
    }
    
    /**
     * 微信用户登出
     */
    suspend fun weChatLogout(): Boolean {
        return try {
            dataStoreRepository.clearWeChatUserInfo()
            LogManager.d(TAG, "微信用户登出成功")
            true
        } catch (e: Exception) {
            LogManager.e(TAG, "微信用户登出失败", e)
            false
        }
    }
    
    /**
     * 获取用户地理位置信息（基于微信城市信息）
     */
    suspend fun getWeChatLocationInfo(): Triple<String, String, String> {
        return try {
            val country = dataStoreRepository.getWeChatCountry()
            val province = dataStoreRepository.getWeChatProvince()
            val city = dataStoreRepository.getWeChatCity()
            Triple(country, province, city)
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信地理位置信息失败", e)
            Triple("", "", "")
        }
    }
    
    /**
     * 获取用户完整显示名称
     * 优先使用微信昵称，如果为空则使用 OpenID 的前8位
     */
    suspend fun getUserDisplayName(): String {
        return try {
            val nickname = dataStoreRepository.getWeChatNickname()
            nickname.ifEmpty {
                val openId = dataStoreRepository.getWeChatOpenId()
                if (openId.length >= 8) {
                    "用户${openId.substring(0, 8)}"
                } else {
                    "微信用户"
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取用户显示名称失败", e)
            "微信用户"
        }
    }
    
    /**
     * 检查是否需要刷新微信令牌
     * 如果令牌在30分钟内过期，则建议刷新
     */
    suspend fun shouldRefreshWeChatToken(): Boolean {
        return try {
            val expiresTime = dataStoreRepository.getWeChatTokenExpiresTime()
            val currentTime = System.currentTimeMillis()
            val thirtyMinutes = 30 * 60 * 1000L // 30分钟的毫秒数
            
            (expiresTime - currentTime) <= thirtyMinutes
        } catch (e: Exception) {
            LogManager.e(TAG, "检查是否需要刷新令牌失败", e)
            true // 出错时建议刷新
        }
    }
    
    /**
     * 获取微信登录时间
     */
    suspend fun getWeChatLoginTime(): Long {
        return try {
            dataStoreRepository.getWeChatLoginTime()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信登录时间失败", e)
            0L
        }
    }
    
    /**
     * 获取用户性别的友好显示
     */
    suspend fun getWeChatSexDisplay(): String {
        return try {
            val sex = dataStoreRepository.getWeChatSex()
            when (sex) {
                "男", "女" -> sex
                else -> "未知"
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信性别显示失败", e)
            "未知"
        }
    }
}