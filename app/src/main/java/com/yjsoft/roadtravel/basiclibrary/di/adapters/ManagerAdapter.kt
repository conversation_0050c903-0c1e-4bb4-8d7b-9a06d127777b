package com.yjsoft.roadtravel.basiclibrary.di.adapters

import android.content.Context
import androidx.activity.ComponentActivity
import coil3.ImageLoader
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.di.HiltConfig
import com.yjsoft.roadtravel.basiclibrary.di.modules.PermissionModule
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.location.service.LocationManager as LocationService
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.ApiService
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.repository.PaymentRepository
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import retrofit2.Retrofit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager适配器类
 * 
 * 功能：
 * - 提供统一的Manager访问接口
 * - 兼容手动初始化和依赖注入两种方式
 * - 简化依赖注入的使用
 * 
 * 设计原则：
 * - 优先使用依赖注入，回退到手动初始化
 * - 提供类型安全的访问方式
 * - 统一的错误处理
 * 
 * 使用方式：
 * ```kotlin
 * @Inject
 * lateinit var managerAdapter: ManagerAdapter
 * 
 * // 使用
 * val networkManager = managerAdapter.getNetworkManager()
 * val logManager = managerAdapter.getLogManager()
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class ManagerAdapter @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val hiltConfig: HiltConfig,
    
    // 注入的Manager实例（可选）
    private val logManager: LogManager,
    private val networkManager: NetworkManager,
    private val dataStoreManager: DataStoreManager,
    private val dataStoreRepository: DataStoreRepository,
    private val imageManager: ImageManager,
    private val imageLoader: ImageLoader,
    private val paymentManager: PaymentManager,
    private val paymentRepository: PaymentRepository,
    private val permissionManagerFactory: PermissionModule.PermissionManagerFactory,
    private val locationService: LocationService,
    private val retrofit: Retrofit,
    private val apiService: ApiService
) {
    
    companion object {
        private const val TAG = "ManagerAdapter"
    }
    
    /**
     * 获取LogManager实例
     */
    fun getLogManager(): LogManager {
        return if (HiltConfig.isHiltEnabled()) {
            logManager
        } else {
            // 回退到手动初始化
            LogManager
        }
    }
    
    /**
     * 获取NetworkManager实例
     */
    fun getNetworkManager(): NetworkManager {
        return if (HiltConfig.isHiltEnabled()) {
            networkManager
        } else {
            // 回退到手动初始化
            NetworkManager
        }
    }

    /**
     * 获取DataStoreManager实例
     */
    fun getDataStoreManager(): DataStoreManager {
        return if (HiltConfig.isHiltEnabled()) {
            dataStoreManager
        } else {
            // 回退到手动初始化
            DataStoreManager
        }
    }

    /**
     * 获取DataStoreRepository实例
     */
    fun getDataStoreRepository(): DataStoreRepository {
        return if (HiltConfig.isHiltEnabled()) {
            dataStoreRepository
        } else {
            // 回退到手动初始化
            DataStoreRepository.getInstance()
        }
    }
    
    /**
     * 获取ImageManager实例
     */
    fun getImageManager(): ImageManager {
        return if (HiltConfig.isHiltEnabled()) {
            imageManager
        } else {
            // 回退到手动初始化
            ImageManager
        }
    }

    /**
     * 获取ImageLoader实例
     */
    fun getImageLoader(): ImageLoader {
        return if (HiltConfig.isHiltEnabled()) {
            imageLoader
        } else {
            // 回退到手动初始化
            ImageManager.getImageLoader(context)
        }
    }

    /**
     * 获取PaymentManager实例
     */
    fun getPaymentManager(): PaymentManager {
        return if (HiltConfig.isHiltEnabled()) {
            paymentManager
        } else {
            // 回退到手动初始化
            PaymentManager
        }
    }

    /**
     * 获取PaymentRepository实例
     */
    fun getPaymentRepository(): PaymentRepository {
        return if (HiltConfig.isHiltEnabled()) {
            paymentRepository
        } else {
            // 回退到手动初始化
            PaymentRepository.getInstance()
        }
    }
    
    /**
     * 获取PermissionManager实例
     * 注意：需要传入Activity实例
     */
    fun getPermissionManager(activity: ComponentActivity): PermissionManager {
        return if (HiltConfig.isHiltEnabled()) {
            permissionManagerFactory.getInstance(activity)
        } else {
            // 回退到手动初始化
            PermissionManager.getInstance(activity)
        }
    }

    /**
     * 获取LocationService实例
     */
    fun getLocationService(): LocationService {
        return if (HiltConfig.isHiltEnabled()) {
            locationService
        } else {
            // 回退到手动初始化
            LocationService.getInstance(context)
        }
    }

    /**
     * 获取Retrofit实例
     */
    fun getRetrofit(): Retrofit {
        return if (HiltConfig.isHiltEnabled()) {
            retrofit
        } else {
            // 回退到手动初始化
            com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance.getInstance(context).getRetrofit()
        }
    }

    /**
     * 获取ApiService实例
     */
    fun getApiService(): ApiService {
        return if (HiltConfig.isHiltEnabled()) {
            apiService
        } else {
            // 回退到手动初始化
            getRetrofit().create(ApiService::class.java)
        }
    }
    
    /**
     * 检查依赖注入是否可用
     */
    fun isDependencyInjectionAvailable(): Boolean {
        return hiltConfig.isDependencyInjectionAvailable()
    }

    /**
     * 获取适配器状态信息
     */
    fun getAdapterInfo(): Map<String, Any> {
        return mapOf(
            "hiltEnabled" to HiltConfig.isHiltEnabled(),
            "compatibilityMode" to HiltConfig.isCompatibilityMode(),
            "diAvailable" to isDependencyInjectionAvailable(),
            "managersInjected" to areManagersInjected()
        )
    }

    /**
     * 检查Manager是否已注入
     */
    private fun areManagersInjected(): Boolean {
        return try {
            // 尝试访问注入的Manager实例来检查是否已注入
            logManager != null && networkManager != null
        } catch (e: Exception) {
            false
        }
    }
}
