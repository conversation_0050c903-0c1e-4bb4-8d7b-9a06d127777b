package com.yjsoft.roadtravel.basiclibrary.network.proxy

import android.content.Context
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import java.net.InetSocketAddress
import java.net.Proxy

/**
 * 代理管理器
 * 负责检测系统代理设置并提供直连配置
 * 
 * 功能：
 * - 检测系统是否设置了代理
 * - 提供直连配置（绕过代理）
 * - 记录代理相关日志
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object ProxyManager {

    /**
     * 检测系统是否设置了代理
     * @param context 应用上下文
     * @return 是否设置了代理
     */
    fun isProxyEnabled(context: Context): Boolean {
        return try {
            val proxyInfo = getSystemProxyInfo(context)
            val hasProxy = proxyInfo != null && proxyInfo.host.isNotEmpty() && proxyInfo.port > 0
            
            LogManager.tag(LogConfig.Tags.NETWORK).d("系统代理检测结果: $hasProxy")
            if (hasProxy) {
                LogManager.tag(LogConfig.Tags.NETWORK).d("代理信息: ${proxyInfo.host}:${proxyInfo.port}")
            }
            
            hasProxy
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "检测系统代理时发生错误")
            false
        }
    }
    
    /**
     * 获取系统代理信息
     * @param context 应用上下文
     * @return 代理信息，如果没有代理则返回null
     */
    private fun getSystemProxyInfo(context: Context): ProxyInfo? {
        return try {
            // Android 6.0及以上版本
            getProxyInfoFromConnectivityManager(context)
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "获取系统代理信息失败")
            null
        }
    }
    
    /**
     * 从ConnectivityManager获取代理信息（Android 6.0+）
     */
    private fun getProxyInfoFromConnectivityManager(context: Context): ProxyInfo? {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetwork = connectivityManager.activeNetwork
            val networkInfo = connectivityManager.getNetworkInfo(activeNetwork)
            
            if (networkInfo?.isConnected == true) {
                // 尝试获取代理信息
                val proxyInfo = System.getProperty("http.proxyHost")
                val proxyPort = System.getProperty("http.proxyPort")
                
                if (!proxyInfo.isNullOrEmpty() && !proxyPort.isNullOrEmpty()) {
                    return ProxyInfo(proxyInfo, proxyPort.toIntOrNull() ?: 0)
                }
            }
            
            null
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "从ConnectivityManager获取代理信息失败")
            null
        }
    }
    
    /**
     * 从WifiManager获取代理信息（Android 6.0以下）
     */
    private fun getProxyInfoFromWifiManager(context: Context): ProxyInfo? {
        return try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val wifiInfo = wifiManager.connectionInfo
            
            if (wifiInfo != null) {
                // 尝试从系统属性获取代理信息
                val proxyHost = System.getProperty("http.proxyHost")
                val proxyPort = System.getProperty("http.proxyPort")
                
                if (!proxyHost.isNullOrEmpty() && !proxyPort.isNullOrEmpty()) {
                    return ProxyInfo(proxyHost, proxyPort.toIntOrNull() ?: 0)
                }
            }
            
            null
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "从WifiManager获取代理信息失败")
            null
        }
    }
    
    /**
     * 获取直连代理配置（绕过系统代理）
     * @return 直连代理配置
     */
    fun getDirectConnectionProxy(): Proxy {
        LogManager.tag(LogConfig.Tags.NETWORK).d("使用直连配置（绕过系统代理）")
        return Proxy.NO_PROXY
    }
    
    /**
     * 获取系统代理配置
     * @param context 应用上下文
     * @return 系统代理配置，如果没有代理则返回直连
     */
    fun getSystemProxy(context: Context): Proxy {
        val proxyInfo = getSystemProxyInfo(context)
        
        return if (proxyInfo != null && proxyInfo.host.isNotEmpty() && proxyInfo.port > 0) {
            LogManager.tag(LogConfig.Tags.NETWORK).d("使用系统代理: ${proxyInfo.host}:${proxyInfo.port}")
            Proxy(Proxy.Type.HTTP, InetSocketAddress(proxyInfo.host, proxyInfo.port))
        } else {
            LogManager.tag(LogConfig.Tags.NETWORK).d("系统未设置代理，使用直连")
            Proxy.NO_PROXY
        }
    }
    
    /**
     * 根据配置获取合适的代理
     * @param context 应用上下文
     * @param forceDirectConnection 是否强制直连
     * @return 代理配置
     */
    fun getProxyForRequest(context: Context, forceDirectConnection: Boolean = true): Proxy {
        return if (forceDirectConnection) {
            getDirectConnectionProxy()
        } else {
            getSystemProxy(context)
        }
    }
    
    /**
     * 记录代理状态信息
     * @param context 应用上下文
     */
    fun logProxyStatus(context: Context) {
        try {
            val isProxyEnabled = isProxyEnabled(context)
            val proxyInfo = getSystemProxyInfo(context)
            
            LogManager.tag(LogConfig.Tags.NETWORK).i("=== 代理状态信息 ===")
            LogManager.tag(LogConfig.Tags.NETWORK).i("系统代理启用: $isProxyEnabled")
            
            if (proxyInfo != null) {
                LogManager.tag(LogConfig.Tags.NETWORK).i("代理地址: ${proxyInfo.host}")
                LogManager.tag(LogConfig.Tags.NETWORK).i("代理端口: ${proxyInfo.port}")
            }
            
            LogManager.tag(LogConfig.Tags.NETWORK).i("应用配置: 强制直连")
            LogManager.tag(LogConfig.Tags.NETWORK).i("==================")
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).w(e, "记录代理状态信息时发生错误")
        }
    }
    
    /**
     * 代理信息数据类
     */
    private data class ProxyInfo(
        val host: String,
        val port: Int
    )
}
