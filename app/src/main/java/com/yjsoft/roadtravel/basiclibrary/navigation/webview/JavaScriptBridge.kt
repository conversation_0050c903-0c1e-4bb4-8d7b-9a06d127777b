package com.yjsoft.roadtravel.basiclibrary.navigation.webview

import android.content.Context
import android.webkit.JavascriptInterface
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationManager
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * JavaScript Bridge类
 * 负责H5页面与原生Android应用的双向通讯
 */
class JavaScriptBridge(
    private val context: Context,
    private val onNavigationRequest: ((String, Map<String, String>) -> Unit)? = null,
    private val onDataReceived: ((String, String) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "JavaScriptBridge %s"
    }
    
    private val json = Json { ignoreUnknownKeys = true }
    private var webView: android.webkit.WebView? = null
    
    /**
     * 设置WebView引用，用于向H5发送消息
     */
    fun setWebView(webView: android.webkit.WebView) {
        this.webView = webView
    }
    
    /**
     * 获取设备信息
     */
    @JavascriptInterface
    fun getDeviceInfo(): String {
        return try {
            val deviceInfo = mapOf(
                "platform" to "Android",
                "version" to android.os.Build.VERSION.RELEASE,
                "model" to android.os.Build.MODEL,
                "brand" to android.os.Build.BRAND,
                "app_version" to getAppVersion()
            )
            json.encodeToString(deviceInfo)
        } catch (e: Exception) {
            LogManager.e(TAG, "获取设备信息失败", e)
            "{\"error\": \"获取设备信息失败\"}"
        }
    }
    
    /**
     * 页面导航请求
     */
    @JavascriptInterface
    fun navigate(action: String, params: String = "{}"): String {
        return try {
            val paramsMap = json.decodeFromString<Map<String, String>>(params)
            onNavigationRequest?.invoke(action, paramsMap)
            LogManager.d(TAG, "导航请求: action=$action, params=$params")
            "{\"success\": true}"
        } catch (e: Exception) {
            LogManager.e(TAG, "导航请求处理失败", e)
            "{\"success\": false, \"error\": \"${e.message}\"}"
        }
    }
    
    /**
     * 调用原生功能
     */
    @JavascriptInterface
    fun callNativeFunction(functionName: String, params: String = "{}"): String {
        return try {
            when (functionName) {
                "showToast" -> {
                    val paramsMap = json.decodeFromString<Map<String, String>>(params)
                    val message = paramsMap["message"] ?: "提示信息"
                    showToast(message)
                    "{\"success\": true}"
                }
                "getLocation" -> {
                    // TODO: 集成定位功能
                    "{\"success\": false, \"error\": \"定位功能暂未实现\"}"
                }
                "openCamera" -> {
                    // TODO: 集成相机功能
                    "{\"success\": false, \"error\": \"相机功能暂未实现\"}"
                }
                "makePayment" -> {
                    // TODO: 集成支付功能
                    "{\"success\": false, \"error\": \"支付功能暂未实现\"}"
                }
                else -> {
                    LogManager.w(TAG, "未知的原生函数: $functionName")
                    "{\"success\": false, \"error\": \"未知的函数: $functionName\"}"
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "调用原生功能失败: $functionName", e)
            "{\"success\": false, \"error\": \"${e.message}\"}"
        }
    }
    
    /**
     * 接收H5发送的数据
     */
    @JavascriptInterface
    fun sendData(key: String, value: String) {
        try {
            onDataReceived?.invoke(key, value)
            LogManager.d(TAG, "接收到H5数据: key=$key, value=$value")
        } catch (e: Exception) {
            LogManager.e(TAG, "处理H5数据失败", e)
        }
    }
    
    /**
     * 获取用户token
     */
    @JavascriptInterface
    fun getUserToken(): String {
        return try {
            // TODO: 从用户管理模块获取token
            val token = "demo_token_123456"
            "{\"token\": \"$token\"}"
        } catch (e: Exception) {
            LogManager.e(TAG, "获取用户token失败", e)
            "{\"error\": \"获取token失败\"}"
        }
    }
    
    /**
     * 检查应用权限
     */
    @JavascriptInterface
    fun checkPermission(permission: String): String {
        return try {
            // TODO: 集成权限检查
            val hasPermission = false // 默认无权限
            "{\"permission\": \"$permission\", \"granted\": $hasPermission}"
        } catch (e: Exception) {
            LogManager.e(TAG, "权限检查失败", e)
            "{\"error\": \"权限检查失败\"}"
        }
    }
    
    /**
     * 向H5发送消息
     */
    fun sendMessageToH5(eventName: String, data: Map<String, Any>) {
        try {
            val jsonData = json.encodeToString(data)
            val jsCode = "window.RoadTravelBridge && window.RoadTravelBridge.onNativeMessage('$eventName', $jsonData);"
            
            webView?.post {
                webView?.evaluateJavascript(jsCode) { result ->
                    LogManager.d(TAG, "向H5发送消息完成: $eventName, result: $result")
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "向H5发送消息失败", e)
        }
    }
    
    /**
     * 通知H5页面准备就绪
     */
    fun notifyH5Ready() {
        sendMessageToH5("bridgeReady", mapOf("status" to "ready"))
    }
    
    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        try {
            android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            LogManager.e(TAG, "显示Toast失败", e)
        }
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            LogManager.e(TAG, "获取应用版本失败", e)
            "1.0.0"
        }
    }
} 