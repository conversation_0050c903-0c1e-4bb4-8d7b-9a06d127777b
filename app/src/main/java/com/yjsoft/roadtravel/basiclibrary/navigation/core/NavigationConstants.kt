package com.yjsoft.roadtravel.basiclibrary.navigation.core

/**
 * 导航常量定义
 * 支持多Activity架构的路由管理
 */
object NavigationConstants {
    
    // Activity路由
    object Activities {
        const val MAIN = "com.yjsoft.roadtravel.ui.activities.main.MainActivity"
        const val LOGIN = "com.yjsoft.roadtravel.ui.activities.login.LoginActivity"
        const val AI_CHAT = "com.yjsoft.roadtravel.ui.activities.aichat.AIChatActivity"
        const val PAYMENT_DEMO = "com.yjsoft.roadtravel.basiclibrary.payment.PaymentActivity"
        const val WEBVIEW = "com.yjsoft.roadtravel.basiclibrary.navigation.WebViewActivity"
        const val PLAN_DETAILS = "com.yjsoft.roadtravel.ui.activities.plan.PlanDetailsActivity"
        const val FAVORITE_PLANS = "com.yjsoft.roadtravel.ui.activities.favorite.FavoritePlansActivity"
    }

    // Activity类引用（用于类型安全的跳转）
    object ActivityClasses {
        val MAIN = com.yjsoft.roadtravel.ui.activities.main.MainActivity::class.java
        val LOGIN = com.yjsoft.roadtravel.ui.activities.login.LoginActivity::class.java
        val AI_CHAT = com.yjsoft.roadtravel.ui.activities.aichat.AIChatActivity::class.java
        val PAYMENT_DEMO = com.yjsoft.roadtravel.basiclibrary.payment.PaymentActivity::class.java
        val WEBVIEW = com.yjsoft.roadtravel.basiclibrary.navigation.WebViewActivity::class.java
        val PLAN_DETAILS = com.yjsoft.roadtravel.ui.activities.plan.PlanDetailsActivity::class.java
        val FAVORITE_PLANS = com.yjsoft.roadtravel.ui.activities.favorite.FavoritePlansActivity::class.java
    }
    
    // Compose内部路由（Activity内部的页面跳转）
    object Routes {
        const val MAIN_HOME = "main_home"
        const val MAIN_PROFILE = "main_profile"
        const val MAIN_SETTINGS = "main_settings"
    }
    
    // Intent动作
    object Actions {
        const val ACTION_PAYMENT = "com.yjsoft.roadtravel.action.PAYMENT"
        const val ACTION_WEBVIEW = "com.yjsoft.roadtravel.action.WEBVIEW"
    }
    
    // 参数键名
    object Params {
        const val URL = "url"
        const val TITLE = "title"
        const val DATA = "data"
        const val FROM = "from"
        const val PAYMENT_TYPE = "payment_type"
        const val AMOUNT = "amount"
        const val ORDER_ID = "order_id"
        const val PLAN_ID = "plan_id"
        const val AI_REQID = "ai_reqid"
    }
    
    // 深度链接scheme
    const val DEEP_LINK_SCHEME = "roadtravel"
    const val DEEP_LINK_HOST = "app"
    
    // WebView相关常量
    object WebView {
        const val JAVASCRIPT_INTERFACE_NAME = "RoadTravelBridge"
        const val USER_AGENT_SUFFIX = "RoadTravel/1.0"
        const val DEFAULT_TITLE = "网页"
        const val ERROR_TITLE = "页面加载失败"
        const val DEFAULT_URL = "https://www.baidu.com"
    }
    
    // 请求码
    object RequestCodes {
        const val PAYMENT_REQUEST = 1001
        const val WEBVIEW_REQUEST = 1002
        const val FILE_PICKER_REQUEST = 1003
    }
    
    // 结果码
    object ResultCodes {
        const val PAYMENT_SUCCESS = 2001
        const val PAYMENT_FAILED = 2002
        const val PAYMENT_CANCELLED = 2003
        const val WEBVIEW_FINISHED = 2004
        const val WEBVIEW_ERROR = 2005
    }
} 