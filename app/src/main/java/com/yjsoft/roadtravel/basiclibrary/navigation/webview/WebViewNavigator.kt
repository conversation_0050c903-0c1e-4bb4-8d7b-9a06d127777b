package com.yjsoft.roadtravel.basiclibrary.navigation.webview

import android.app.Activity
import android.webkit.WebView
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationManager
import com.yjsoft.roadtravel.basiclibrary.navigation.models.NavigationParams
import java.net.URL
/**
 * H5页面导航管理器
 * 负责WebView页面的导航逻辑、历史管理和与原生导航的协调
 */
class WebViewNavigator(
    private val activity: BaseActivity,
    private val webView: WebView
) {
    
    companion object {
        private const val TAG = "WebViewNavigator %s"
    }
    
    // NavigationManager现在是object，不需要实例
    private var javaScriptBridge: JavaScriptBridge? = null
    
    // 当前页面信息
    private var currentPage: WebPageInfo? = null
    
    /**
     * 设置JavaScript Bridge
     */
    fun setJavaScriptBridge(bridge: JavaScriptBridge) {
        this.javaScriptBridge = bridge
        bridge.setWebView(webView)
        
        // 设置导航请求处理器
        bridge.apply {
            // 这里需要重新设置bridge，因为构造函数的回调已经固定
            // 实际应用中可以通过interface方式重新设计
        }
    }
    
    /**
     * 加载H5页面
     */
    fun loadUrl(url: String, title: String? = null) {
        try {
            val pageInfo = WebPageInfo(url, title ?: extractTitleFromUrl(url))
            
            // 更新当前页面信息，让WebView自己管理历史
            currentPage = pageInfo
            
            // 加载页面
            webView.loadUrl(url)
            
            LogManager.d(TAG, "加载H5页面: $url")
        } catch (e: Exception) {
            LogManager.e(TAG, "加载H5页面失败: $url", e)
        }
    }
    
    /**
     * H5页面返回
     * @return true表示在H5内部处理了返回，false表示需要关闭WebView
     */
    fun goBack(): Boolean {
        return try {
            val canGoBack = webView.canGoBack()
            val currentUrl = webView.url
            val originalUrl = webView.originalUrl
            
            LogManager.d(TAG, "返回检查 - canGoBack: $canGoBack, currentUrl: $currentUrl, originalUrl: $originalUrl")
            
            if (canGoBack) {
                // WebView内部可以返回，直接使用WebView的历史管理
                webView.goBack()
                LogManager.d(TAG, "H5页面返回成功，返回后URL: ${webView.url}")
                true
            } else {
                // WebView无法返回，需要关闭WebView
                LogManager.d(TAG, "H5页面无法返回，准备关闭WebView")
                false
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "H5页面返回失败", e)
            false
        }
    }
    
    /**
     * 处理H5导航请求
     */
    fun handleNavigationRequest(action: String, params: Map<String, String>) {
        try {
            when (action) {
                "openUrl" -> {
                    val url = params["url"]
                    val title = params["title"]
                    if (!url.isNullOrEmpty()) {
                        loadUrl(url, title)
                    }
                }
                "openNativePage" -> {
                    handleNativePageNavigation(params)
                }
                "goBack" -> {
                    if (!goBack()) {
                        // H5无法返回，关闭当前Activity
                        activity.finish()
                    }
                }
                "close" -> {
                    activity.finish()
                }
                "refresh" -> {
                    webView.reload()
                }
                else -> {
                    LogManager.w(TAG, "未知的导航动作: $action")
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "处理H5导航请求失败: $action", e)
        }
    }
    
    /**
     * 处理原生页面导航
     */
    private fun handleNativePageNavigation(params: Map<String, String>) {
        val pageType = params["pageType"]
        when (pageType) {
            "payment" -> {
                val navParams = NavigationParams.builder()
                    .putString("from", "webview")
                    .putString("url", currentPage?.url ?: "")
                    .build()
                NavigationManager.navigateToPaymentDemo(activity, navParams)
            }
            "main" -> {
                // 返回主页面
                activity.finish()
            }
            else -> {
                LogManager.w(TAG, "未知的原生页面类型: $pageType")
            }
        }
    }
    
    /**
     * 获取当前页面信息
     */
    fun getCurrentPage(): WebPageInfo? = currentPage
    
    /**
     * 获取页面历史数量
     */
    fun getHistorySize(): Int {
        return if (webView.canGoBack()) 1 else 0 // WebView不提供直接的历史数量API，简化返回
    }
    
    /**
     * 清空页面历史
     */
    fun clearHistory() {
        webView.clearHistory()
        LogManager.d(TAG, "WebView页面历史已清空")
    }
    
    /**
     * 检查是否可以返回
     */
    fun canGoBack(): Boolean {
        return webView.canGoBack()
    }
    
    /**
     * 向H5发送导航状态
     */
    fun sendNavigationState() {
        javaScriptBridge?.sendMessageToH5("navigationState", mapOf(
            "canGoBack" to canGoBack(),
            "historySize" to getHistorySize(),
            "currentUrl" to (currentPage?.url ?: "")
        ))
    }
    
    /**
     * 页面加载完成回调
     */
    fun onPageFinished(url: String) {
        // 更新当前页面URL（可能发生了重定向）
        currentPage?.let {
            if (it.url != url) {
                LogManager.d(TAG, "页面URL重定向: ${it.url} -> $url")
                currentPage = it.copy(url = url)
            }
        }
        
        // 记录WebView历史状态
        val canGoBack = webView.canGoBack()
        val canGoForward = webView.canGoForward()
        LogManager.d(TAG, "页面加载完成: $url, 历史状态 - canGoBack: $canGoBack, canGoForward: $canGoForward")
        
        // 通知H5页面导航状态
        sendNavigationState()
        
        // 通知Bridge准备就绪
        javaScriptBridge?.notifyH5Ready()
    }
    
    /**
     * 从URL提取标题
     */
    private fun extractTitleFromUrl(url: String): String {
        return try {
            val urlObj = URL(url)
            urlObj.host ?: "Web Page"
        } catch (e: Exception) {
            "Web Page"
        }
    }
}

/**
 * H5页面信息数据类
 */
data class WebPageInfo(
    val url: String,
    val title: String,
    val timestamp: Long = System.currentTimeMillis()
) 