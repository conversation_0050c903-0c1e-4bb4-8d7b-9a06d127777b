package com.yjsoft.roadtravel.basiclibrary.image.interceptors

import coil3.intercept.Interceptor
import coil3.request.ImageRequest
import coil3.request.ImageResult
import coil3.request.SuccessResult
import coil3.request.ErrorResult
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import java.util.Locale

/**
 * 图片缓存拦截器
 * 处理图片缓存相关的逻辑和统计
 */
class ImageCacheInterceptor : Interceptor {
    
    companion object {
        private var cacheHitCount = 0L
        private var cacheMissCount = 0L
    }
    
    override suspend fun intercept(chain: Interceptor.Chain): ImageResult {
        val result = chain.proceed()

        // 统计缓存命中情况
        when (result) {
            is SuccessResult -> {
                // 注意：Coil3中可能需要通过其他方式判断是否来自缓存
                // 这里先做基础统计
                cacheHitCount++

                LogManager.tag(LogConfig.Tags.IMAGE).v(
                    "缓存统计 - 命中: $cacheHitCount, 未命中: $cacheMissCount"
                )
            }
            is ErrorResult -> {
                cacheMissCount++
            }
        }
        
        return result
    }
    
    /**
     * 获取缓存命中率
     */
    fun getCacheHitRate(): Double {
        val total = cacheHitCount + cacheMissCount
        return if (total > 0) {
            cacheHitCount.toDouble() / total
        } else {
            0.0
        }
    }
    
    /**
     * 重置缓存统计
     */
    fun resetCacheStats() {
        cacheHitCount = 0L
        cacheMissCount = 0L
        LogManager.tag(LogConfig.Tags.IMAGE).d("缓存统计已重置")
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): String {
        return "缓存统计 - 命中: $cacheHitCount, 未命中: $cacheMissCount, 命中率: ${String.format(Locale.getDefault(), "%.2f", getCacheHitRate() * 100)}%"
    }
}
