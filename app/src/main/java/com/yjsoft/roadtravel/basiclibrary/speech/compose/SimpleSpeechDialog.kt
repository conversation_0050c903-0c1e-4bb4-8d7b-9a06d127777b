package com.yjsoft.roadtravel.basiclibrary.speech.compose

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionParams
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 简化的语音识别对话框
 * 使用手动控制，不依赖VAD
 */
@Composable
fun SimpleSpeechDialog(
    onDismiss: () -> Unit,
    onResult: (String) -> Unit,
    onError: (String) -> Unit = {},
    title: String = "语音识别",
    hint: String = "点击开始录音，说话完毕后会自动停止",
    maxDuration: Long = 60000L // 最大录音时长(ms)
) {
    val context = LocalContext.current
    val speechManager = remember { SpeechRecognitionManager.getInstance(context) }
    val coroutineScope = rememberCoroutineScope()
    
    // 收集状态
    val state by speechManager.state.collectAsStateWithLifecycle()
    val result by speechManager.result.collectAsStateWithLifecycle()
    val audioState by speechManager.audioState.collectAsStateWithLifecycle()
    val error by speechManager.error.collectAsStateWithLifecycle(initialValue = null)
    
    // 本地状态
    var isRecording by remember { mutableStateOf(false) }
    var recordingTime by remember { mutableStateOf(0L) }
    
    // 处理结果
    LaunchedEffect(result) {
        if (result.isSuccess && result.isFinal) {
            onResult(result.text)
            onDismiss()
        }
    }
    
    // 处理错误
    LaunchedEffect(error) {
        error?.let {
            LogManager.e("SimpleSpeechDialog", "语音识别错误: ${it.message}")

            // 如果是VAD相关错误，不立即关闭对话框，等待自动重试
            if (it.message.contains("VAD") || it.message.contains("语音检测")) {
                LogManager.d("SimpleSpeechDialog", "VAD错误，等待自动重试")
                // 不关闭对话框，等待重试
            } else {
                onError(it.message)
                // 其他错误延迟关闭对话框
                delay(2000)
                onDismiss()
            }
        }
    }
    
    // 录音时间计时器
    LaunchedEffect(isRecording) {
        if (isRecording) {
            while (isRecording) {
                delay(100)
                recordingTime += 100
                if (recordingTime >= maxDuration) {
                    // 达到最大时长，自动停止
                    handleStopRecording(speechManager, coroutineScope) {
                        isRecording = false
                        recordingTime = 0
                    }
                    break
                }
            }
        } else {
            recordingTime = 0
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            speechManager.reset()
        }
    }
    
    Dialog(
        onDismissRequest = {
            if (isRecording) {
                handleStopRecording(speechManager, coroutineScope) {
                    isRecording = false
                }
            }
            speechManager.cancelRecognition()
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = title,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 录音按钮
                FloatingActionButton(
                    onClick = {
                        if (isRecording) {
                            // 停止录音
                            handleStopRecording(speechManager, coroutineScope) {
                                isRecording = false
                            }
                        } else {
                            // 开始录音
                            handleStartRecording(speechManager, coroutineScope) {
                                isRecording = true
                            }
                        }
                    },
                    modifier = Modifier.size(80.dp),
                    containerColor = if (isRecording) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                ) {
                    Icon(
                        imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.Mic,
                        contentDescription = if (isRecording) "停止录音" else "开始录音",
                        modifier = Modifier.size(32.dp),
                        tint = Color.White
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 状态文本
                val statusText = when {
                    result.text.isNotEmpty() -> result.text
                    isRecording -> "正在录音中..."
                    state == SpeechRecognitionState.PROCESSING -> "正在识别..."
                    state == SpeechRecognitionState.ERROR -> "识别失败，请重试"
                    else -> hint
                }
                
                Text(
                    text = statusText,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp),
                    textAlign = TextAlign.Center,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    minLines = 2
                )
                
                // 录音进度
                if (isRecording) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    LinearProgressIndicator(
                        progress = { recordingTime.toFloat() / maxDuration },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(4.dp),
                        color = if (recordingTime > maxDuration * 0.9f) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.primary
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "${recordingTime / 1000}s / ${maxDuration / 1000}s",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 取消按钮
                    TextButton(
                        onClick = {
                            if (isRecording) {
                                handleStopRecording(speechManager, coroutineScope) {
                                    isRecording = false
                                }
                            }
                            speechManager.cancelRecognition()
                            onDismiss()
                        }
                    ) {
                        Text("取消")
                    }
                    
                    // 重新开始按钮
                    if (state == SpeechRecognitionState.ERROR || state == SpeechRecognitionState.SUCCESS) {
                        TextButton(
                            onClick = {
                                speechManager.reset()
                                isRecording = false
                                recordingTime = 0
                            }
                        ) {
                            Text("重新开始")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 开始录音
 */
private fun handleStartRecording(
    speechManager: SpeechRecognitionManager,
    coroutineScope: CoroutineScope,
    onStarted: () -> Unit
) {
    coroutineScope.launch {
        try {
            // 使用默认参数（启用VAD）
            val params = SpeechRecognitionParams()
            val success = speechManager.startRecognition(params)
            if (success) {
                onStarted()
                LogManager.d("SimpleSpeechDialog", "开始录音成功")
            } else {
                LogManager.e("SimpleSpeechDialog", "开始录音失败")
            }
        } catch (e: Exception) {
            LogManager.e("SimpleSpeechDialog", "开始录音异常", e)
        }
    }
}

/**
 * 停止录音
 */
private fun handleStopRecording(
    speechManager: SpeechRecognitionManager,
    coroutineScope: CoroutineScope,
    onStopped: () -> Unit
) {
    coroutineScope.launch {
        try {
            speechManager.stopRecognition()
            onStopped()
            LogManager.d("SimpleSpeechDialog", "停止录音成功")
        } catch (e: Exception) {
            LogManager.e("SimpleSpeechDialog", "停止录音异常", e)
        }
    }
}
