package com.yjsoft.roadtravel.basiclibrary.network.examples

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.network.proxy.ProxyManager
import com.yjsoft.roadtravel.basiclibrary.network.utils.NetworkTestUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 代理配置使用示例
 * 展示如何在应用中使用直连功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object ProxyConfigExample {
    
    private const val TAG = "ProxyConfigExample"
    
    /**
     * 示例1：应用启动时检查并配置网络
     */
    fun configureNetworkOnAppStart(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("=== 应用启动网络配置 ===")
        
        // 1. 检查系统代理状态
        val hasProxy = ProxyManager.isProxyEnabled(context)
        LogManager.tag(LogConfig.Tags.NETWORK).i("系统代理状态: ${if (hasProxy) "已启用" else "未启用"}")
        
        // 2. 根据代理状态决定是否启用直连
        if (hasProxy) {
            LogManager.tag(LogConfig.Tags.NETWORK).i("检测到系统代理，启用直连模式以避免网关错误")
            NetworkManager.enableDirectConnection(context)
        } else {
            LogManager.tag(LogConfig.Tags.NETWORK).i("未检测到系统代理，使用默认配置")
        }
        
        // 3. 记录最终配置
        val isDirectEnabled = NetworkConfig.isDirectConnectionEnabled()
        LogManager.tag(LogConfig.Tags.NETWORK).i("最终配置 - 直连模式: ${if (isDirectEnabled) "启用" else "禁用"}")
        
        LogManager.tag(LogConfig.Tags.NETWORK).i("========================")
    }
    
    /**
     * 示例2：网络请求失败时的处理
     */
    fun handleNetworkFailure(context: Context, error: Throwable) {
        LogManager.tag(LogConfig.Tags.NETWORK).w(error, "网络请求失败，开始诊断")
        
        // 启动网络诊断
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val testResult = NetworkTestUtils.testNetworkConnection(context)
                
                LogManager.tag(LogConfig.Tags.NETWORK).i("网络诊断完成")
                LogManager.tag(LogConfig.Tags.NETWORK).i("诊断结果: ${if (testResult.success) "成功" else "失败"}")
                LogManager.tag(LogConfig.Tags.NETWORK).i("建议: ${testResult.recommendation}")
                
                // 根据诊断结果自动调整配置
                if (!testResult.success) {
                    val directResult = testResult.results.find { it.type == "直连" }
                    if (directResult?.success == true && !NetworkConfig.isDirectConnectionEnabled()) {
                        LogManager.tag(LogConfig.Tags.NETWORK).i("直连测试成功，切换到直连模式")
                        NetworkManager.enableDirectConnection(context)
                    }
                }
                
            } catch (e: Exception) {
                LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络诊断失败")
            }
        }
    }
    
    /**
     * 示例3：手动切换网络模式
     */
    fun switchToDirectConnection(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("手动切换到直连模式")
        
        try {
            // 启用直连
            NetworkManager.enableDirectConnection(context)
            
            // 测试连接
            CoroutineScope(Dispatchers.IO).launch {
                val testResult = NetworkTestUtils.testNetworkConnection(context)
                LogManager.tag(LogConfig.Tags.NETWORK).i("直连模式测试结果: ${if (testResult.success) "成功" else "失败"}")
            }
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "切换到直连模式失败")
        }
    }
    
    /**
     * 示例4：恢复系统代理模式
     */
    fun switchToSystemProxy(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("切换到系统代理模式")
        
        try {
            // 禁用直连（使用系统代理）
            NetworkManager.disableDirectConnection(context)
            
            // 测试连接
            CoroutineScope(Dispatchers.IO).launch {
                val testResult = NetworkTestUtils.testNetworkConnection(context)
                LogManager.tag(LogConfig.Tags.NETWORK).i("系统代理模式测试结果: ${if (testResult.success) "成功" else "失败"}")
            }
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "切换到系统代理模式失败")
        }
    }
    
    /**
     * 示例5：网络状态检查
     */
    fun checkNetworkStatus(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("=== 网络状态检查 ===")
        
        try {
            // 1. 检查网络框架状态
            val networkStatus = NetworkManager.getStatus()
            LogManager.tag(LogConfig.Tags.NETWORK).i("网络框架已初始化: ${networkStatus.isInitialized}")
            LogManager.tag(LogConfig.Tags.NETWORK).i("当前环境: ${networkStatus.currentEnvironment?.displayName}")
            LogManager.tag(LogConfig.Tags.NETWORK).i("Base URL: ${networkStatus.baseUrl}")
            
            // 2. 检查代理状态
            NetworkManager.checkProxyStatus(context)
            
            // 3. 检查直连配置
            val isDirectEnabled = NetworkConfig.isDirectConnectionEnabled()
            LogManager.tag(LogConfig.Tags.NETWORK).i("直连模式: ${if (isDirectEnabled) "启用" else "禁用"}")
            
            LogManager.tag(LogConfig.Tags.NETWORK).i("==================")
            
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络状态检查失败")
        }
    }
    
    /**
     * 示例6：完整的网络诊断和自动修复
     */
    fun performNetworkDiagnosisAndAutoFix(context: Context) {
        LogManager.tag(LogConfig.Tags.NETWORK).i("开始网络诊断和自动修复")
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 1. 执行网络测试
                val testResult = NetworkTestUtils.testNetworkConnection(context)
                
                // 2. 分析结果并自动修复
                if (!testResult.success) {
                    LogManager.tag(LogConfig.Tags.NETWORK).w("网络连接失败，尝试自动修复")
                    
                    val directResult = testResult.results.find { it.type == "直连" }
                    val proxyResult = testResult.results.find { it.type == "系统代理" }
                    
                    when {
                        directResult?.success == true -> {
                            LogManager.tag(LogConfig.Tags.NETWORK).i("直连可用，切换到直连模式")
                            NetworkManager.enableDirectConnection(context)
                        }
                        proxyResult?.success == true -> {
                            LogManager.tag(LogConfig.Tags.NETWORK).i("代理可用，切换到代理模式")
                            NetworkManager.disableDirectConnection(context)
                        }
                        else -> {
                            LogManager.tag(LogConfig.Tags.NETWORK).w("所有连接方式都失败，请检查网络设置")
                        }
                    }
                } else {
                    LogManager.tag(LogConfig.Tags.NETWORK).i("网络连接正常，无需修复")
                }
                
                LogManager.tag(LogConfig.Tags.NETWORK).i("网络诊断完成: ${testResult.recommendation}")
                
            } catch (e: Exception) {
                LogManager.tag(LogConfig.Tags.NETWORK).e(e, "网络诊断和自动修复失败")
            }
        }
    }
}
