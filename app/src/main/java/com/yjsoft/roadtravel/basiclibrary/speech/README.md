# 语音识别模块

基于百度语音识别SDK封装的Android语音识别模块，提供完整的语音识别功能。

## 功能特性

- ✅ 基于百度语音识别SDK
- ✅ 支持实时语音识别
- ✅ 音量级别检测
- ✅ 录音状态管理
- ✅ Compose UI组件
- ✅ 协程支持
- ✅ 生命周期感知
- ✅ 权限管理集成
- ✅ 错误处理

## 快速开始

### 1. 配置API密钥

在 `SpeechConfig.kt` 中配置你的百度语音识别API密钥：

```kotlin
object BaiduSpeech {
    const val API_KEY = "your_api_key_here"
    const val SECRET_KEY = "your_secret_key_here"
    const val APP_ID = "your_app_id_here"
}
```

### 2. 基本使用

#### 使用语音识别按钮

```kotlin
@Composable
fun MyScreen() {
    var recognizedText by remember { mutableStateOf("") }
    
    SpeechRecognitionButton(
        onResult = { text ->
            recognizedText = text
        },
        onError = { error ->
            // 处理错误
        }
    )
}
```

#### 使用语音识别对话框

```kotlin
@Composable
fun MyScreen() {
    var showDialog by remember { mutableStateOf(false) }
    var recognizedText by remember { mutableStateOf("") }
    
    if (showDialog) {
        SpeechRecognitionDialog(
            onDismiss = { showDialog = false },
            onResult = { text ->
                recognizedText = text
            },
            title = "语音输入",
            hint = "请说出您的问题..."
        )
    }
    
    Button(onClick = { showDialog = true }) {
        Text("开始语音识别")
    }
}
```

### 3. 高级使用

#### 直接使用SpeechRecognitionManager

```kotlin
class MyViewModel : ViewModel() {
    private val speechManager = SpeechRecognitionManager.getInstance(context)
    
    init {
        // 监听状态变化
        viewModelScope.launch {
            speechManager.state.collect { state ->
                // 处理状态变化
            }
        }
        
        // 监听识别结果
        viewModelScope.launch {
            speechManager.result.collect { result ->
                if (result.isSuccess) {
                    // 处理识别结果
                }
            }
        }
    }
    
    suspend fun startRecognition() {
        if (!speechManager.isSDKInitialized()) {
            speechManager.initialize()
        }
        speechManager.startRecognition()
    }
    
    fun stopRecognition() {
        speechManager.stopRecognition()
    }
}
```

#### 自定义识别参数

```kotlin
val customParams = SpeechRecognitionParams(
    language = "zh",
    devPid = 1537, // 普通话
    maxSpeechTimeS = 30,
    enablePunctuation = true,
    enableNumberConvert = true
)

speechManager.startRecognition(customParams)
```

## 组件说明

### SpeechRecognitionButton

语音识别按钮组件，提供完整的录音和识别功能。

**参数：**
- `onResult: (String) -> Unit` - 识别结果回调
- `onError: (String) -> Unit` - 错误回调
- `enabled: Boolean` - 是否启用
- `size: Dp` - 按钮大小
- `colors: SpeechButtonColors` - 颜色配置

### SpeechRecognitionDialog

语音识别对话框，提供完整的语音识别UI体验。

**参数：**
- `onDismiss: () -> Unit` - 关闭回调
- `onResult: (String) -> Unit` - 识别结果回调
- `onError: (String) -> Unit` - 错误回调
- `title: String` - 对话框标题
- `hint: String` - 提示文本
- `maxDuration: Long` - 最大录音时长

### VolumeIndicator

音量指示器组件，显示录音音量级别。

### SpeechStateIndicator

状态指示器组件，显示当前识别状态。

## 状态管理

### SpeechRecognitionState

```kotlin
enum class SpeechRecognitionState {
    IDLE,           // 空闲状态
    INITIALIZING,   // 初始化中
    LISTENING,      // 监听中
    RECORDING,      // 录音中
    PROCESSING,     // 处理中
    SUCCESS,        // 识别成功
    ERROR           // 错误状态
}
```

### SpeechRecognitionResult

```kotlin
data class SpeechRecognitionResult(
    val text: String,           // 识别文本
    val confidence: Float,      // 置信度
    val isFinal: Boolean,       // 是否最终结果
    val timestamp: Long,        // 时间戳
    val duration: Long,         // 录音时长
    val errorCode: Int,         // 错误码
    val errorMessage: String    // 错误信息
)
```

## 权限要求

模块需要以下权限（已在AndroidManifest.xml中配置）：

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

## 错误处理

模块提供完整的错误处理机制：

```kotlin
speechManager.error.collect { error ->
    when {
        error.isNetworkError -> {
            // 网络错误
        }
        error.isPermissionError -> {
            // 权限错误
        }
        error.isAudioError -> {
            // 录音错误
        }
        error.isRecognitionError -> {
            // 识别错误
        }
        error.isTimeoutError -> {
            // 超时错误
        }
    }
}
```

## 配置说明

### 语音识别参数

在 `SpeechConfig.kt` 中可以配置各种参数：

- API密钥配置
- 录音参数配置
- UI动画配置
- 错误码定义
- 日志配置

### 自定义颜色

```kotlin
val customColors = SpeechButtonDefaults.colors(
    idleColor = Color.Blue,
    recordingColor = Color.Red,
    successColor = Color.Green
)

SpeechRecognitionButton(
    colors = customColors
)
```

## 注意事项

1. **API密钥安全**：生产环境中应从安全的配置服务获取API密钥
2. **权限检查**：使用前确保已获得录音权限
3. **网络连接**：语音识别需要网络连接
4. **资源释放**：及时释放资源避免内存泄漏
5. **错误处理**：妥善处理各种错误情况

## 依赖关系

- 百度语音识别SDK
- Kotlin协程
- Jetpack Compose
- 项目内的日志模块
- 项目内的权限模块
