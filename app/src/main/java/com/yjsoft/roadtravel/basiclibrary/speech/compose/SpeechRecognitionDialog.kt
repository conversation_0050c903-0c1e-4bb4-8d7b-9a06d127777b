package com.yjsoft.roadtravel.basiclibrary.speech.compose

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionState
import kotlinx.coroutines.launch

/**
 * 语音识别对话框
 * 提供完整的语音识别UI体验
 */
@Composable
fun SpeechRecognitionDialog(
    onDismiss: () -> Unit,
    onResult: (String) -> Unit,
    onError: (String) -> Unit = {},
    title: String = "语音识别",
    hint: String = "请说话...",
    maxDuration: Long = 60000L // 最大录音时长(ms)
) {
    val context = LocalContext.current
    val speechManager = remember { SpeechRecognitionManager.getInstance(context) }
    val coroutineScope = rememberCoroutineScope()
    
    // 收集状态
    val state by speechManager.state.collectAsStateWithLifecycle()
    val result by speechManager.result.collectAsStateWithLifecycle()
    val audioState by speechManager.audioState.collectAsStateWithLifecycle()
    val error by speechManager.error.collectAsStateWithLifecycle(initialValue = null)
    
    // 处理结果
    LaunchedEffect(result) {
        if (result.isSuccess && result.isFinal) {
            onResult(result.text)
            onDismiss()
        }
    }
    
    // 处理错误
    LaunchedEffect(error) {
        error?.let {
            onError(it.message)
            // 错误后延迟关闭对话框
            kotlinx.coroutines.delay(2000)
            onDismiss()
        }
    }
    
    // 自动开始识别
    LaunchedEffect(Unit) {
        if (!speechManager.isSDKInitialized()) {
            speechManager.initialize()
        }
        kotlinx.coroutines.delay(500) // 等待初始化完成
        speechManager.startRecognition()
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            speechManager.reset()
        }
    }
    
    Dialog(
        onDismissRequest = {
            speechManager.cancelRecognition()
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = title,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 语音识别按钮
                SpeechRecognitionButton(
                    size = 80.dp,
                    onResult = onResult,
                    onError = onError
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 音量指示器
                if (audioState.isRecording) {
                    VolumeIndicator(
                        volume = audioState.volume,
                        modifier = Modifier.height(32.dp)
                    )
                } else {
                    Spacer(modifier = Modifier.height(32.dp))
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 状态指示器
                SpeechStateIndicator(
                    state = state,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 提示文本或识别结果
                val displayText = when {
                    result.text.isNotEmpty() -> result.text
                    state == SpeechRecognitionState.RECORDING -> hint
                    state == SpeechRecognitionState.PROCESSING -> "正在识别..."
                    state == SpeechRecognitionState.ERROR -> "识别失败，请重试"
                    else -> "点击麦克风开始录音"
                }
                
                Text(
                    text = displayText,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp),
                    textAlign = TextAlign.Center,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    minLines = 2
                )
                
                // 录音进度条
                if (audioState.isRecording) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    LinearProgressIndicator(
                        progress = { audioState.progress },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(4.dp)
                            .clip(RoundedCornerShape(2.dp)),
                        color = if (audioState.isNearMaxDuration) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.primary
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "${audioState.duration / 1000}s / ${maxDuration / 1000}s",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 取消按钮
                    TextButton(
                        onClick = {
                            speechManager.cancelRecognition()
                            onDismiss()
                        }
                    ) {
                        Text("取消")
                    }
                    
                    // 重新开始按钮
                    if (state == SpeechRecognitionState.ERROR || state == SpeechRecognitionState.SUCCESS) {
                        TextButton(
                            onClick = {
                                coroutineScope.launch {
                                    speechManager.reset()
                                    kotlinx.coroutines.delay(500)
                                    speechManager.startRecognition()
                                }
                            }
                        ) {
                            Text("重新开始")
                        }
                    }
                }
            }
        }
    }
}
