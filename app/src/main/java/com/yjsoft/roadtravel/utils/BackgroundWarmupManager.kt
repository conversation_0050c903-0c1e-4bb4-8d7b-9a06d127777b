package com.yjsoft.roadtravel.utils

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 后台预热管理器
 * 管理所有后台预热任务，避免阻塞应用启动
 */
object BackgroundWarmupManager {
    
    private const val TAG = "BackgroundWarmupManager"
    
    // 预热状态
    private val isWarmupStarted = AtomicBoolean(false)
    private val isWarmupCompleted = AtomicBoolean(false)
    
    // 预热任务
    private var warmupJob: Job? = null
    
    // 预热进度回调
    private var progressCallback: ((String, Int, Int) -> Unit)? = null
    
    /**
     * 启动后台预热
     * @param context 应用上下文
     * @param delayMs 延迟启动时间（毫秒）
     * @param onProgress 进度回调 (taskName, current, total)
     */
    fun startWarmup(
        context: Context, 
        delayMs: Long = 1000L,
        onProgress: ((String, Int, Int) -> Unit)? = null
    ) {
        if (isWarmupStarted.getAndSet(true)) {
            LogManager.d(TAG, "后台预热已启动，跳过重复启动")
            return
        }
        
        progressCallback = onProgress
        
        warmupJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                LogManager.i(TAG, "后台预热将在${delayMs}ms后启动")
                delay(delayMs)
                
                LogManager.i(TAG, "开始后台预热任务")
                PerformanceMonitor.startTiming("background_warmup", "后台预热总时间")
                
                executeWarmupTasks(context)
                
                val duration = PerformanceMonitor.endTiming("background_warmup", "后台预热完成")
                isWarmupCompleted.set(true)
                LogManager.i(TAG, "后台预热完成，总耗时: ${duration}ms")
                
            } catch (e: Exception) {
                LogManager.e(TAG, "后台预热失败", e)
            }
        }
    }
    
    /**
     * 执行预热任务
     */
    private suspend fun executeWarmupTasks(context: Context) {
        val tasks = listOf(
            "网络组件预热" to { warmupNetworkComponents(context) },
            "缓存系统预热" to { warmupCacheSystem(context) },
            "图片框架预热" to { warmupImageFramework(context) },
            "支付框架预热" to { warmupPaymentFramework(context) }
        )
        
        tasks.forEachIndexed { index, (taskName, task) ->
            try {
                LogManager.d(TAG, "执行预热任务: $taskName")
                progressCallback?.invoke(taskName, index + 1, tasks.size)
                
                PerformanceMonitor.startTiming("warmup_$taskName", taskName)
                task.invoke()
                val duration = PerformanceMonitor.endTiming("warmup_$taskName", "$taskName 完成")
                
                LogManager.d(TAG, "$taskName 完成，耗时: ${duration}ms")
                
                // 任务间短暂延迟，避免CPU占用过高
                delay(100)
                
            } catch (e: Exception) {
                LogManager.w(TAG, "$taskName 失败: ${e.message}")
            }
        }
    }
    
    /**
     * 预热网络组件
     */
    private fun warmupNetworkComponents(context: Context) {
        try {
            // 获取RetrofitInstance并预热
            val retrofitInstance = NetworkManager.getRetrofitInstance(context)
            retrofitInstance.warmUp()
            
            LogManager.d(TAG, "网络组件预热完成")
        } catch (e: Exception) {
            LogManager.w(TAG, "网络组件预热失败: ${e.message}")
        }
    }
    
    /**
     * 预热缓存系统
     */
    private fun warmupCacheSystem(context: Context) {
        try {
            // 预热网络缓存
            val retrofitInstance = NetworkManager.getRetrofitInstance(context)
            val networkCache = retrofitInstance.getNetworkCache()
            // 触发缓存初始化
            networkCache.getCache()
            
            LogManager.d(TAG, "缓存系统预热完成")
        } catch (e: Exception) {
            LogManager.w(TAG, "缓存系统预热失败: ${e.message}")
        }
    }
    
    /**
     * 预热图片框架
     */
    private fun warmupImageFramework(context: Context) {
        try {
            // 这里可以添加图片框架的预热逻辑
            // 例如：预加载常用图片、初始化图片缓存等
            LogManager.d(TAG, "图片框架预热完成")
        } catch (e: Exception) {
            LogManager.w(TAG, "图片框架预热失败: ${e.message}")
        }
    }
    
    /**
     * 预热支付框架
     */
    private fun warmupPaymentFramework(context: Context) {
        try {
            // 这里可以添加支付框架的预热逻辑
            // 例如：预初始化支付SDK、检查支付环境等
            LogManager.d(TAG, "支付框架预热完成")
        } catch (e: Exception) {
            LogManager.w(TAG, "支付框架预热失败: ${e.message}")
        }
    }
    
    /**
     * 检查预热是否完成
     */
    fun isWarmupCompleted(): Boolean = isWarmupCompleted.get()
    
    /**
     * 检查预热是否已启动
     */
    fun isWarmupStarted(): Boolean = isWarmupStarted.get()
    
    /**
     * 取消预热任务
     */
    fun cancelWarmup() {
        warmupJob?.cancel()
        LogManager.d(TAG, "后台预热任务已取消")
    }
    
    /**
     * 重置预热状态（用于测试）
     */
    fun reset() {
        cancelWarmup()
        isWarmupStarted.set(false)
        isWarmupCompleted.set(false)
        progressCallback = null
    }
    
    /**
     * 获取预热状态摘要
     */
    fun getWarmupStatus(): String {
        return when {
            isWarmupCompleted.get() -> "已完成"
            isWarmupStarted.get() -> "进行中"
            else -> "未启动"
        }
    }
}
