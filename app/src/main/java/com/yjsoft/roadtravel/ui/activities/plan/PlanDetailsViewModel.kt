package com.yjsoft.roadtravel.ui.activities.plan

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.NavigationEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiEvent
import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailData
import com.yjsoft.roadtravel.ui.activities.plan.models.MapV2Response
import com.yjsoft.roadtravel.ui.activities.plan.repository.PlanRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * 行程详情页面ViewModel
 *
 * 功能：
 * - 管理顶部显示模式（轮播图/地图）
 * - 管理详情面板拖拽状态
 * - 处理行程数据加载
 *
 * <AUTHOR> Team
 */
@HiltViewModel
class PlanDetailsViewModel @Inject constructor(
    private val planRepository: PlanRepository
) : BaseViewModel<PlanDetailsViewModel.PlanDetailsUiState>() {

    companion object {
        private const val TAG = "PlanDetailsViewModel"

        // 详情面板高度相关常量
        const val DEFAULT_DETAILS_HEIGHT_RATIO = 0.6f // 默认高度比例

        // 顶部容器高度
        const val CAROUSEL_HEIGHT_DP = 210
        const val MAP_HEIGHT_DP = 320

        // 详情面板向上偏移
        const val DETAILS_OFFSET_DP = 8
    }

    // ========== 状态管理 ==========

    /**
     * 顶部显示模式
     */
    enum class TopDisplayMode {
        CAROUSEL, // 轮播图模式
        MAP       // 地图模式
    }

    /**
     * 页面UI状态
     */
    data class PlanDetailsUiState(
        val isLoading: Boolean = false,
        val topDisplayMode: TopDisplayMode = TopDisplayMode.CAROUSEL,
        val planDetails: PlanDetailData? = null,
        val planPolyline: MapV2Response? = null,
        val isLoadingPolyline: Boolean = false,
        val detailsHeightRatio: Float = DEFAULT_DETAILS_HEIGHT_RATIO,
        val isDragging: Boolean = false,
        val error: String? = null,
        val polylineError: String? = null
    )

    // ========== BaseViewModel实现 ==========

    override fun createInitialState(): PlanDetailsUiState = PlanDetailsUiState()

    // ========== 初始化 ==========

    /**
     * 初始化ViewModel
     * @param planId 行程ID
     */
    fun initialize(planId: Int = 0, aiReqId: String? = null) {
        LogManager.d(TAG, "初始化行程详情页面，planId: $planId")
        loadPlanDetails(planId, aiReqId)
        loadPlanPolyline(planId, aiReqId)
    }

    // ========== 数据加载 ==========

    /**
     * 加载行程详情数据
     * @param planId 行程ID
     * @param aiReqId AI请求ID
     */
    private fun loadPlanDetails(planId: Int, aiReqId: String?) {
        launch {
            try {
                updateState { it.copy(isLoading = true, error = null) }

                LogManager.d(TAG, "开始加载行程详情，planId: $planId")

                // 使用Repository调用真实API
                val result = planRepository.getPlanDetail(planId, aiReqId)

                when (result) {
                    is Resource.Success -> {
                        updateState {
                            it.copy(
                                isLoading = false,
                                planDetails = result.data
                            )
                        }
                        LogManager.d(TAG, "行程详情数据加载成功")
                    }

                    is Resource.Error -> {
                        val errorMessage = result.message
                        LogManager.e(TAG, "加载行程详情失败: $errorMessage")
                        updateState {
                            it.copy(
                                isLoading = false,
                                error = "加载失败：$errorMessage"
                            )
                        }
                        sendUiEvent(UiEvent.ShowToast("加载行程详情失败：$errorMessage"))
                    }

                    is Resource.Loading -> {
                        // Loading状态已经在开始时设置
                    }
                }

            } catch (e: Exception) {
                LogManager.e(TAG, "加载行程详情异常", e)
                updateState {
                    it.copy(
                        isLoading = false,
                        error = "加载失败：${e.message}"
                    )
                }
                sendUiEvent(UiEvent.ShowToast("加载行程详情失败"))
            }
        }
    }

    /**
     * 加载行程路线数据
     * @param planId 行程ID
     * @param aiReqId AI请求ID（可选）
     */
    fun loadPlanPolyline(planId: Int, aiReqId: String? = null) {
        launch {
            try {
                updateState { it.copy(isLoadingPolyline = true, polylineError = null) }

                LogManager.d(TAG, "开始加载行程路线，planId: $planId")

                // 使用Repository调用真实API
                val result = planRepository.getPlanPolyline(planId, aiReqId)

                when (result) {
                    is Resource.Success -> {
                        updateState {
                            it.copy(
                                isLoadingPolyline = false,
                                planPolyline = result.data
                            )
                        }
                        LogManager.d(TAG, "行程路线数据加载成功")
                    }

                    is Resource.Error -> {
                        val errorMessage = result.message
                        LogManager.e(TAG, "加载行程路线失败: $errorMessage")
                        updateState {
                            it.copy(
                                isLoadingPolyline = false,
                                polylineError = "加载失败：$errorMessage"
                            )
                        }
                        sendUiEvent(UiEvent.ShowToast("加载行程路线失败：$errorMessage"))
                    }

                    is Resource.Loading -> {
                        // Loading状态已经在开始时设置
                    }
                }

            } catch (e: Exception) {
                LogManager.e(TAG, "加载行程路线异常", e)
                updateState {
                    it.copy(
                        isLoadingPolyline = false,
                        polylineError = "加载失败：${e.message}"
                    )
                }
                sendUiEvent(UiEvent.ShowToast("加载行程路线失败"))
            }
        }
    }


    // ========== 顶部显示模式管理 ==========

    /**
     * 切换到轮播图模式
     */
    fun switchToCarouselMode() {
        LogManager.d(TAG, "切换到轮播图模式")
        updateState {
            it.copy(
                topDisplayMode = TopDisplayMode.CAROUSEL,
                isDragging = false // 轮播图模式下禁用拖拽
            )
        }
    }

    /**
     * 切换到地图模式
     */
    fun switchToMapMode() {
        LogManager.d(TAG, "切换到地图模式")
        updateState {
            it.copy(topDisplayMode = TopDisplayMode.MAP)
        }
    }

    /**
     * 切换顶部显示模式
     */
    fun toggleTopDisplayMode() {
        val currentMode = currentState.topDisplayMode
        when (currentMode) {
            TopDisplayMode.CAROUSEL -> switchToMapMode()
            TopDisplayMode.MAP -> switchToCarouselMode()
        }
    }

    // ========== 事件处理 ==========

    /**
     * 处理返回按钮点击
     */
    fun onBackPressed() {
        LogManager.d(TAG, "返回按钮点击")
        sendNavigationEvent(NavigationEvent.Back)
    }

    /**
     * 处理分享按钮点击
     */
    fun onShareClicked() {
        LogManager.d(TAG, "分享按钮点击")
        sendUiEvent(UiEvent.ShowToast("分享功能开发中"))
    }

    /**
     * 处理收藏按钮点击
     */
    fun onFavoriteClicked() {
        LogManager.d(TAG, "收藏按钮点击")
        sendUiEvent(UiEvent.ShowToast("收藏功能开发中"))
    }

}
