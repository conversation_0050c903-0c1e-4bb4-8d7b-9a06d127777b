package com.yjsoft.roadtravel.ui.fragments.home.models

import com.google.gson.annotations.SerializedName
import com.yjsoft.roadtravel.basiclibrary.network.models.ApiResponse

/**
 * 地理信息接口响应数据模型
 */
typealias GeoApiResponse = ApiResponse<GeoData>

/**
 * 地理信息数据
 */
data class GeoData(
    @SerializedName("address")
    val address: String,
    
    @SerializedName("lng")
    val lng: Double,
    
    @SerializedName("lat")
    val lat: Double,
    
    @SerializedName("city")
    val city: String,
    
    @SerializedName("zone_id")
    val zoneId: Int
) 