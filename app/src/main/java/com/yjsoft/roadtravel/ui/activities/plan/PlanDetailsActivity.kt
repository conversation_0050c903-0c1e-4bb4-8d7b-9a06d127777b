package com.yjsoft.roadtravel.ui.activities.plan

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.core.view.WindowCompat
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants
import com.yjsoft.roadtravel.ui.activities.plan.screen.PlanDetailsScreen
import dagger.hilt.android.AndroidEntryPoint

/**
 * 行程详情页面
 *
 * 功能：
 * - 顶部轮播图/地图切换显示
 * - 详情面板向上遮盖效果
 * - 地图模式下支持拖拽调整详情面板高度
 *
 * <AUTHOR> Team
 */
@AndroidEntryPoint
class PlanDetailsActivity : BaseActivity() {

    private val viewModel: PlanDetailsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 配置状态栏
        setupStatusBar()

        // 获取传递的参数
        val planId = intent.getIntExtra(NavigationConstants.Params.PLAN_ID, 0)
        val aiReqId = intent.getStringExtra(NavigationConstants.Params.AI_REQID) ?: ""
        LogManager.d("PlanDetailsActivity", "接收到: planId: $planId, aiReqId: $aiReqId")

        // 设置Compose内容
        setupContent {
            PlanDetailsScreen(viewModel = viewModel)
        }

        // 观察ViewModel事件
        observeEvents(
            uiEvents = viewModel.uiEvents,
            navigationEvents = viewModel.navigationEvents
        )

        // 初始化数据，传递planId
        viewModel.initialize(planId, aiReqId)
    }

    /**
     * 配置状态栏样式
     * 设置状态栏文字为黑色（深色模式）
     */
    private fun setupStatusBar() {
        // 启用边到边显示
        enableEdgeToEdge()

        // 设置状态栏文字为黑色
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = true // true表示状态栏文字为黑色
        }

        LogManager.d("PlanDetailsActivity", "状态栏配置完成 - 文字颜色设置为黑色")
    }
}