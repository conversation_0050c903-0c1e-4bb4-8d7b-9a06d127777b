package com.yjsoft.roadtravel.ui.fragments.home.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 提示列表组件
 *
 * 特性：
 * - 自动换行布局，每行最多两个item
 * - 每个item前添加🔎 emoji
 * - Card样式，8%黑色阴影
 * - 内间距：左右10dp，上下5dp
 * - 文字：14sp，黑色
 *
 * @param prompts 提示文本列表
 * @param onPromptClick 提示点击事件回调
 * @param modifier 修饰符
 */
@Composable
fun PromptsList(
    modifier: Modifier = Modifier,
    prompts: List<String>,
    onPromptClick: (String) -> Unit = {}
) {
    FlowRow(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        maxItemsInEachRow = 2
    ) {
        prompts.forEachIndexed { index, prompt ->
            PromptItem(
                text = prompt,
                onClick = { onPromptClick(prompt) }
            )
        }
    }
}

/**
 * 提示项组件
 *
 * @param text 提示文本
 * @param onClick 点击事件
 * @param modifier 修饰符
 */
@Composable
fun PromptItem(
    modifier: Modifier = Modifier,
    text: String,
    onClick: () -> Unit = {},
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(8.dp),
                ambientColor = Color.Black.copy(alpha = 0.08f), // 8%的黑色阴影
                spotColor = Color.Black.copy(alpha = 0.08f)
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Text(
            text = "🔎 $text",
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 5.dp)
        )
    }
}

/**
 * 简化版提示列表（无点击事件）
 */
@Composable
fun SimplePromptsList(
    prompts: List<String>,
    modifier: Modifier = Modifier
) {
    PromptsList(
        prompts = prompts,
        onPromptClick = {},
        modifier = modifier
    )
}
