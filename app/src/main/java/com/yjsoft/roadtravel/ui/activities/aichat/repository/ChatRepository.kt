package com.yjsoft.roadtravel.ui.activities.aichat.repository

import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseRepository
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.network.ApiService
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryData
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryItem
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryResponse
import com.yjsoft.roadtravel.ui.activities.aichat.models.InitChatData
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject


@ViewModelScoped
class ChatRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "ChatRepository"
    }

    /**
     * 初始化聊天
     */
    suspend fun initChat(): Resource<InitChatData> {
        return safeApiCall {
            val response = apiService.initChat()
            response.getDataOrThrow()
        }
    }
    /**
     * 获取聊天历史
     */
    suspend fun getChatHistory(conversationId: String): Resource<ChatHistoryData> {
        return safeApiCall {
            val response = apiService.getChatHistory(conversationId)
            response.getDataOrThrow()
        }
    }
}