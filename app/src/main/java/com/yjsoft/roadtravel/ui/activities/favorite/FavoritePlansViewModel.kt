package com.yjsoft.roadtravel.ui.activities.favorite

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.NavigationEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatUserManager
import com.yjsoft.roadtravel.data.repository.FavoriteRepository
import com.yjsoft.roadtravel.ui.fragments.home.models.PlanItem
import com.yjsoft.roadtravel.ui.fragments.home.models.PlansData
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import android.content.Context

/**
 * 收藏页面UI状态
 */
data class FavoritePlansUiState(
    val isLoading: Boolean = false,
    val plansData: PlansData? = null,
    val error: String? = null
)

/**
 * 收藏行程ViewModel
 *
 * 功能：
 * - 管理收藏列表数据
 * - 处理收藏/取消收藏操作
 * - 处理页面导航
 *
 * <AUTHOR> Team
 */
@HiltViewModel
class FavoritePlansViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val favoriteRepository: FavoriteRepository
) : BaseViewModel<UiState<FavoritePlansUiState>>() {

    companion object {
        private const val TAG = "FavoritePlansViewModel"
    }

    private val apiService = NetworkManager.getApiService(context)
    private val weChatUserManager = WeChatUserManager.getInstance()

    override fun createInitialState(): UiState<FavoritePlansUiState> {
        return UiState.Success(FavoritePlansUiState())
    }

    init {
        LogManager.d(TAG, "FavoritePlansViewModel 初始化")
        // 使用 Flow 监听收藏数据变化，实现实时同步
        observeFavoriteChanges()
    }

    /**
     * 观察收藏数据变化，实现实时同步
     */
    private fun observeFavoriteChanges() {
        launch {
            favoriteRepository.getFavoritePlansFlow().collect { favoriteList ->
                try {
                    LogManager.d(TAG, "收藏数据发生变化，共 ${favoriteList.size} 条")

                    // 检查登录状态
                    val isLoggedIn = weChatUserManager.isWeChatLoggedIn()
                    if (!isLoggedIn) {
                        LogManager.d(TAG, "用户未登录，显示登录提示")
                        updateState {
                            UiState.Success(
                                FavoritePlansUiState(
                                    plansData = null,
                                    isLoading = false,
                                    error = "请先登录查看收藏列表"
                                )
                            )
                        }
                        return@collect
                    }

                    if (favoriteList.isNotEmpty()) {
                        // 构造 PlansData 对象
                        val plansData = PlansData(
                            title = "我的收藏",
                            color = "#1890FF",
                            list = favoriteList
                        )

                        updateState {
                            UiState.Success(
                                FavoritePlansUiState(
                                    plansData = plansData,
                                    isLoading = false,
                                    error = null
                                )
                            )
                        }
                    } else {
                        // 空收藏列表
                        updateState {
                            UiState.Success(
                                FavoritePlansUiState(
                                    plansData = null,
                                    isLoading = false,
                                    error = null
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    LogManager.e(TAG, "处理收藏数据变化异常", e)
                    updateState {
                        UiState.Success(
                            FavoritePlansUiState(
                                isLoading = false,
                                error = "加载收藏列表失败: ${e.message}"
                            )
                        )
                    }
                }
            }
        }
    }

    /**
     * 加载收藏列表（保留用于手动刷新）
     */
    fun loadFavorites(page: Int = 1) {
        // 数据同步已通过 Flow 自动处理，这里只需要触发一次数据检查
        LogManager.d(TAG, "手动刷新收藏列表")
        // Flow 会自动更新数据，无需手动处理
    }

    /**
     * 切换收藏状态（主要是取消收藏）
     */
    fun toggleFavorite(item: PlanItem) {
        launch {
            try {
                LogManager.d(TAG, "切换收藏状态: ${item.subject}")

                val success = favoriteRepository.removeFavorite(item)
                if (success) {
                    LogManager.d(TAG, "取消收藏成功")
                    showToast("已取消收藏")
                    // 重新加载列表
                    loadFavorites()
                } else {
                    LogManager.e(TAG, "取消收藏失败")
                    showToast("取消收藏失败")
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "切换收藏状态异常", e)
                showToast("操作失败")
            }
        }
    }

    /**
     * 跳转到行程详情
     */
    fun navigateToPlanDetail(planItem: PlanItem) {
        LogManager.d(TAG, "跳转到行程详情: ${planItem.subject}")
        // 这里可以发送导航事件或直接处理跳转
        sendUiEvent(UiEvent.ShowToast("跳转到详情页面"))
    }

    /**
     * 返回上一页
     */
    fun onBackPressed() {
        LogManager.d(TAG, "返回按钮点击")
        sendNavigationEvent(NavigationEvent.Back)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        LogManager.d(TAG, "刷新收藏列表")
        loadFavorites()
    }
}
