package com.yjsoft.roadtravel.ui.activities.favorite

import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.WindowCompat
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

/**
 * 收藏行程页面
 *
 * 功能：
 * - 显示用户收藏的行程列表
 * - 支持取消收藏
 * - 支持跳转到行程详情
 *
 * <AUTHOR> Team
 */
@AndroidEntryPoint
class FavoritePlansActivity : BaseActivity() {

    private val viewModel: FavoritePlansViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 配置状态栏
        setupStatusBar()

        // 设置Compose内容
        setupContent {
            FavoritePlansScreen(viewModel = viewModel)
        }

        // 观察ViewModel事件
        observeEvents(
            uiEvents = viewModel.uiEvents,
            navigationEvents = viewModel.navigationEvents
        )
    }

    /**
     * 配置状态栏
     */
    private fun setupStatusBar() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
    }
}
