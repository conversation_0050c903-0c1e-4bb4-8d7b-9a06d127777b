package com.yjsoft.roadtravel.ui.activities.login.models

import com.google.gson.annotations.SerializedName


data class LoginResponse(
    val code: Int,
    val data: LoginData,
    val msg: String
)

data class LoginData(
    val token: String,
    val nickname: String,
    val avatar: String,
    @SerializedName("user_id")
    val userId: String,
    @SerializedName("open_id")
    val openId: String,
    @SerializedName("need_nickname")
    val needNickName: Boolean
)