package com.yjsoft.roadtravel.ui.activities.plan.components

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Map
import androidx.compose.material.icons.filled.Photo
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImage
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.ui.activities.plan.PlanDetailsViewModel
import com.yjsoft.roadtravel.ui.components.TitleBar
import com.yjsoft.roadtravel.ui.components.RightIconButton
import com.yjsoft.roadtravel.ui.components.IconBackground
import com.yjsoft.roadtravel.ui.activities.plan.models.Route
import kotlinx.coroutines.delay

/**
 * 顶部容器组件
 * 支持轮播图和地图两种显示模式
 *
 * @param displayMode 显示模式
 * @param banners 轮播图片列表
 * @param mapLatitude 地图纬度
 * @param mapLongitude 地图经度
 * @param routes 路线数据列表（可选）
 * @param onModeToggle 模式切换回调
 * @param onBackClick 返回按钮点击回调
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@Composable
fun TopContainer(
    displayMode: PlanDetailsViewModel.TopDisplayMode,
    banners: List<String>,
    mapLatitude: Double,
    mapLongitude: Double,
    routes: List<Route> = emptyList(),
    onModeToggle: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val containerHeight = when (displayMode) {
        PlanDetailsViewModel.TopDisplayMode.CAROUSEL -> PlanDetailsViewModel.CAROUSEL_HEIGHT_DP.dp
        PlanDetailsViewModel.TopDisplayMode.MAP -> PlanDetailsViewModel.MAP_HEIGHT_DP.dp
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(containerHeight)
    ) {
        // 主要内容区域
        AnimatedContent(
            targetState = displayMode,
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) togetherWith
                        fadeOut(animationSpec = tween(300))
            },
            modifier = Modifier.fillMaxSize(),
            label = "TopContainerContent"
        ) { mode ->
            when (mode) {
                PlanDetailsViewModel.TopDisplayMode.CAROUSEL -> {
                    CarouselContent(
                        banners = banners,
                        modifier = Modifier.fillMaxSize()
                    )
                }

                PlanDetailsViewModel.TopDisplayMode.MAP -> {
                    MapContainer(
                        latitude = mapLatitude,
                        longitude = mapLongitude,
                        routes = routes,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }

        // 顶部工具栏 - 使用TitleBar组件
        TitleBar(
            title = "", // 空标题，只显示图标
            leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
            leftIconBackground = IconBackground(
                backgroundColor = Color.Black.copy(alpha = 0.3f), // 半透明黑色背景
                padding = 6.dp
            ),
            rightIcons = listOf(
                RightIconButton(
                    imageVector = when (displayMode) {
                        PlanDetailsViewModel.TopDisplayMode.CAROUSEL -> Icons.Default.Map
                        PlanDetailsViewModel.TopDisplayMode.MAP -> Icons.Default.Photo
                    },
                    contentDescription = when (displayMode) {
                        PlanDetailsViewModel.TopDisplayMode.CAROUSEL -> "切换到地图"
                        PlanDetailsViewModel.TopDisplayMode.MAP -> "切换到轮播图"
                    },
                    onClick = onModeToggle,
                    background = IconBackground(
                        backgroundColor = Color.Black.copy(alpha = 0.3f), // 半透明黑色背景
                        padding = 6.dp
                    )
                )
            ),
            onLeftIconClick = onBackClick,
            backgroundColor = Color.Transparent, // 透明背景
            contentColor = Color.White, // 白色图标
            includeStatusBarPadding = true, // 包含状态栏间距
            modifier = Modifier.align(Alignment.TopStart)
        )
    }
}

/**
 * 轮播图内容
 */
@Composable
private fun CarouselContent(
    banners: List<String>,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.FillBounds
) {
    if (banners.isEmpty()) {
        // 空状态占位
        Box(
            modifier = modifier.background(Color.Gray.copy(alpha = 0.3f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "暂无图片",
                color = Color.Gray
            )
        }
        return
    }

    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { banners.size }
    )

    // 自动轮播
//    LaunchedEffect(banners.size) {
//        if (banners.size > 1) {
//            while (true) {
//                delay(3000L)
//                val nextPage = (pagerState.currentPage + 1) % banners.size
//                pagerState.animateScrollToPage(nextPage)
//            }
//        }
//    }

    Box(modifier = modifier) {
        // 轮播图
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NetworkImage(
                    url = banners[page],
                    contentDescription = "行程图片 ${page + 1}",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = contentScale,
                    onError = { error ->
                        LogManager.e(
                            "TopContainer",
                            "图片加载失败: ${banners[page]}",
                            error.result.throwable
                        )
                    },
                    onSuccess = { success ->
                        LogManager.d("TopContainer", "图片加载成功: ${banners[page]}")
                    }
                )
            }
        }

        // 页面指示器
        if (banners.size > 1) {
            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(banners.size) { index ->
                    val isSelected = index == pagerState.currentPage
                    Box(
                        modifier = Modifier
                            .size(if (isSelected) 8.dp else 6.dp)
                            .background(
                                color = if (isSelected) Color.White else Color.White.copy(alpha = 0.5f),
                                shape = CircleShape
                            )
                    )
                }
            }
        }
    }
}





