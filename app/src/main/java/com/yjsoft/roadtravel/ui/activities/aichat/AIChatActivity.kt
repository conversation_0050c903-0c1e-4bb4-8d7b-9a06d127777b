package com.yjsoft.roadtravel.ui.activities.aichat

import android.os.Build
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImageWithPlaceholder
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.UiStateHandler
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryData
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryItem
import com.yjsoft.roadtravel.ui.activities.aichat.models.InitChatData
import com.yjsoft.roadtravel.ui.activities.aichat.components.ChatInputBar
import com.yjsoft.roadtravel.ui.components.GradientBackground
import com.yjsoft.roadtravel.ui.components.IconSize
import com.yjsoft.roadtravel.ui.components.RightIconButton
import com.yjsoft.roadtravel.ui.components.TitleBar
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AIChatActivity : BaseActivity() {

    private val TAG = "AIChatActivity"
    private val viewModel: AIChatViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传入的conversationId
        val conversationId = intent.getStringExtra("conversationId") ?: ""
        LogManager.d(TAG, "AIChatActivity启动，conversationId: $conversationId")

        // 如果有传入的conversationId，设置到ViewModel中
        if (conversationId.isNotEmpty()) {
            viewModel.setConversationId(conversationId)
        }

        setupContent {
            GradientBackground {
                AIChatScreen(
                    viewModel = viewModel,
                    onShowToast = { message -> showToast(message) }
                )
            }
        }

        // 观察ViewModel事件
        observeEvents(
            uiEvents = viewModel.uiEvents
        )
    }

    override fun finish() {
        super.finish()
        // 设置退出动画：当前页面向下滑出，上一个页面淡入
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            // Android 14+ 使用新的API
            overrideActivityTransition(
                OVERRIDE_TRANSITION_CLOSE,
                R.anim.fade_in,
                R.anim.slide_down_out
            )
        } else {
            // 兼容旧版本
            @Suppress("DEPRECATION")
            overridePendingTransition(R.anim.fade_in, R.anim.slide_down_out)
        }
    }

    @Composable
    fun AIChatScreen(
        viewModel: AIChatViewModel,
        onShowToast: (String) -> Unit = {}
    ) {
        val context = LocalContext.current
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        val conversationId by viewModel.conversationId.collectAsStateWithLifecycle()
        val initChatState by viewModel.initChatState.collectAsStateWithLifecycle()
        val chatHistoryState by viewModel.chatHistoryState.collectAsStateWithLifecycle()

        // 输入框状态
        var inputText by remember { mutableStateOf("") }

        // 监听对话ID变化
        LaunchedEffect(conversationId) {
            if (conversationId.isNotEmpty()) {
                LogManager.d(TAG, "对话ID已更新: $conversationId")
            }
        }

        // 配置状态栏样式 - 固定样式，不跟随系统色改变
        val view = LocalView.current
        SideEffect {
            val window = (view.context as AIChatActivity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)
            val insetsController = WindowCompat.getInsetsController(window, view)
            insetsController.isAppearanceLightStatusBars = true
        }
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent
        ) { padding ->
            Column(
                modifier = Modifier
                    .padding(padding)
            ) {
                TitleBar(
                    title = "行程规划",
                    includeStatusBarPadding = false,
                    titleIconColor = Color(0xFF1990FF),
                    backgroundColor = Color.Transparent,
                    leftIconSize = IconSize(24.dp),
                    titleLeftIconRes = R.drawable.chat_title_left,
                    leftIconRes = R.drawable.close,
                    titleLeftIconSize = IconSize(width = 30.dp, height = 15.dp),
                    titleRightIconSize = IconSize(24.dp),
                    onLeftIconClick = { onBackPressedDispatcher.onBackPressed() },
                    rightIcons =
                        listOf(
                            RightIconButton(
                                drawableRes = R.drawable.xinduihua,
                                onClick = {
                                    // 处理新对话按钮点击
                                    viewModel.handleNewConversation()
                                }
                            ),
                            RightIconButton(
                                drawableRes = R.drawable.chat_history,
                                onClick = {
                                    // 处理聊天历史按钮点击 - 临时修改对话ID的值
                                    viewModel.handleChatHistory()
                                }
                            ),
                        )
                )

                // 主要内容区域
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    if (conversationId.isEmpty()) {
                        // 对话ID为空，显示初始化界面
                        InitChatContent(
                            initChatState = initChatState,
                            onPromptClick = { prompt ->
                                inputText = prompt
                                onShowToast("已选择提示词: $prompt")
                            },
                            onRetry = { viewModel.refreshChatData() }
                        )
                    } else {
                        // 对话ID不为空，显示聊天历史
                        ChatHistoryContent(
                            chatHistoryState = chatHistoryState,
                            conversationId = conversationId,
                            onRetry = { viewModel.refreshChatData() }
                        )
                    }
                }

                // 底部输入框
                ChatInputBar(
                    modifier = Modifier.imePadding(),
                    text = inputText,
                    onTextChange = { inputText = it },
                    onSendMessage = { message ->
                        LogManager.d(TAG, "发送消息: $message")
                        inputText = "" // 清空输入框
                    },
                    onVoiceInput = { voiceText ->
                        LogManager.d(TAG, "语音输入: $voiceText")
                        inputText = voiceText // 填入输入框
                    },
                    placeholder = "给出您的旅行需求..."
                )
            }
        }
    }
}

/**
 * 初始化聊天内容组件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun InitChatContent(
    initChatState: UiState<InitChatData>,
    onPromptClick: (String) -> Unit,
    onRetry: () -> Unit
) {
    UiStateHandler(
        uiState = initChatState,
        onRetry = onRetry
    ) { initData ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .offset(y=(-100).dp)
                .padding(start = 24.dp, end = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Logo图片 - 126*86dp
            NetworkImageWithPlaceholder(
                url = initData.logo,
                contentDescription = "Logo",
                modifier = Modifier
                    .width(126.dp)
                    .height(86.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Fit
            )

            // Slogan - 14sp, #999999
            Text(
                text = initData.slogan,
                fontSize = 14.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp, bottom = 24.dp)
            )

            // Prompts - FlowRow排列，每行最多两个item
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                maxItemsInEachRow = 2
            ) {
                initData.prompts.forEach { prompt ->
                    PromptItem(
                        text = "🔎 $prompt",
                        onClick = { onPromptClick(prompt) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 提示词项目组件
 */
@Composable
fun PromptItem(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Text(
            text = text,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            fontSize = 14.sp,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            lineHeight = 20.sp
        )
    }
}

/**
 * 聊天历史内容组件
 */
@Composable
fun ChatHistoryContent(
    chatHistoryState: UiState<ChatHistoryData>,
    conversationId: String,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 对话ID显示
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White.copy(alpha = 0.9f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "当前对话",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFF1990FF)
                )
                Text(
                    text = "对话ID: $conversationId",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }

        // 聊天历史列表
        UiStateHandler(
            uiState = chatHistoryState,
            onRetry = onRetry
        ) { chatHistoryData ->
            if (chatHistoryData.list.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无聊天记录",
                        color = Color.Gray,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState()),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    chatHistoryData.list.forEach { item ->
                        ChatHistoryItem(item = item)
                    }
                }
            }
        }
    }
}

/**
 * 聊天历史项目组件
 */
@Composable
fun ChatHistoryItem(item: ChatHistoryItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (item.role == "user") Color(0xFFE3F2FD) else Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = item.role.uppercase(),
                fontSize = 12.sp,
                color = Color(0xFF666666),
                fontWeight = FontWeight.Bold
            )
            Text(
                text = item.content,
                fontSize = 14.sp,
                color = Color(0xFF333333),
                modifier = Modifier.padding(top = 4.dp),
                lineHeight = 20.sp
            )
        }
    }
}
