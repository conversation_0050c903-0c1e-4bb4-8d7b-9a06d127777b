package com.yjsoft.roadtravel.ui.activities.aichat

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.baidu.speech.EventManagerFactory
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.launchWithUiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryData
import com.yjsoft.roadtravel.ui.activities.aichat.models.ChatHistoryItem
import com.yjsoft.roadtravel.ui.activities.aichat.models.InitChatData
import com.yjsoft.roadtravel.ui.activities.aichat.repository.ChatRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * AI聊天ViewModel
 * 
 * 功能：
 * - 管理聊天会话
 * - 处理对话ID
 * - 初始化聊天数据
 * - 管理聊天状态
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class AIChatViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val chatRepository: ChatRepository
) : BaseViewModel<AIChatUiState>() {
    
    companion object {
        private const val TAG = "AIChatViewModel"
    }
    
    // 数据存储仓库
    private val dataStoreRepository = DataStoreRepository.getInstance()
    
    // ========== 对话ID管理 ==========
    
    /**
     * 当前对话ID - 从DataStore自动监听
     */
    val conversationId: StateFlow<String> = dataStoreRepository.getConversionIDFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )
    
    /**
     * 初始化聊天数据状态
     */
    private val _initChatState = MutableStateFlow<UiState<InitChatData>>(UiState.Idle)
    val initChatState: StateFlow<UiState<InitChatData>> = _initChatState.asStateFlow()

    /**
     * 聊天历史数据状态
     */
    private val _chatHistoryState = MutableStateFlow<UiState<ChatHistoryData>>(UiState.Idle)
    val chatHistoryState: StateFlow<UiState<ChatHistoryData>> = _chatHistoryState.asStateFlow()
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): AIChatUiState {
        return AIChatUiState()
    }
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "AIChatViewModel启动")
        initializeChat()
    }
    
    /**
     * 初始化聊天
     */
    private fun initializeChat() {
        safeLaunch {
            updateState {
                it.copy(
                    lastAction = "初始化聊天中..."
                )
            }

            // 监听对话ID变化，决定调用哪个接口
            conversationId.collect { id ->
                if (id.isEmpty()) {
                    // 对话ID为空，调用初始化接口
                    loadInitChatData()
                } else {
                    // 对话ID不为空，调用获取聊天历史接口
                    loadChatHistory(id)
                }
            }
        }
    }
    
    /**
     * 设置对话ID
     */
    fun setConversationId(conversationId: String) {
        safeLaunch {
            try {
                dataStoreRepository.setConversionID(conversationId)
                updateState { 
                    it.copy(
                        lastAction = "设置对话ID: $conversationId"
                    )
                }
                LogManager.d(TAG, "对话ID设置成功: $conversationId")
            } catch (e: Exception) {
                LogManager.e(TAG, "设置对话ID失败", e)
                showError("设置对话ID失败: ${e.message}")
            }
        }
    }
    
    /**
     * 生成新的对话ID
     */
    fun generateNewConversationId() {
        val newId = ""
        setConversationId(newId)
    }
    
    /**
     * 获取当前对话ID
     */
    fun getCurrentConversationId(): String {
        return conversationId.value
    }
    
    /**
     * 加载初始化聊天数据
     */
    private fun loadInitChatData() {
        launchWithUiState(_initChatState, "加载聊天初始化数据...") {
            val result = chatRepository.initChat()
            when (result) {
                is Resource.Success -> result.data
                is Resource.Error -> {
                    throw Exception(result.message)
                }
                is Resource.Loading -> {
                    result.data ?: throw Exception("数据加载中")
                }
            }
        }
    }

    /**
     * 加载聊天历史数据
     */
    private fun loadChatHistory(conversationId: String) {
        launchWithUiState(_chatHistoryState, "加载聊天历史...") {
            val result = chatRepository.getChatHistory(conversationId)
            when (result) {
                is Resource.Success -> result.data // 现在是ChatHistoryData
                is Resource.Error -> {
                    throw Exception(result.message)
                }
                is Resource.Loading -> {
                    result.data ?: throw Exception("数据加载中")
                }
            }
        }
    }
    
    /**
     * 刷新聊天数据
     */
    fun refreshChatData() {
        loadInitChatData()
    }
    
    /**
     * 处理新对话按钮点击
     */
    fun handleNewConversation() {
        safeLaunch {
            updateState { 
                it.copy(
                    lastAction = "创建新对话"
                )
            }
            
            // 生成新的对话ID
            generateNewConversationId()
            
            LogManager.d(TAG, "创建新对话，ID: ${getCurrentConversationId()}")
        }
    }
    
    /**
     * 处理聊天历史按钮点击
     */
    fun handleChatHistory() {
        safeLaunch {
            updateState { 
                it.copy(
                    lastAction = "查看聊天历史"
                )
            }
            
            // 临时修改对话ID的值（用于测试）
            val testId = "test_${System.currentTimeMillis()}"
            setConversationId(testId)
            
            LogManager.d(TAG, "查看聊天历史，当前对话ID: ${getCurrentConversationId()}")
        }
    }
    
    /**
     * 处理用户登录状态变化
     */
    fun handleUserLogin() {
        safeLaunch {
            updateState { 
                it.copy(
                    lastAction = "处理用户登录"
                )
            }
            
            LogManager.d(TAG, "处理用户登录状态变化")
            showToast("跳转到登录页面")
        }
    }
    
    /**
     * 获取聊天统计信息
     */
    fun getChatStats(): String {
        val currentId = getCurrentConversationId()
        return if (currentId.isNotEmpty()) {
            "当前对话ID: $currentId"
        } else {
            "暂无活跃对话"
        }
    }
}

/**
 * AI聊天UI状态
 */
data class AIChatUiState(
    val isInitialized: Boolean = false,
    val currentConversationId: String = "",
    val lastAction: String = ""
)
