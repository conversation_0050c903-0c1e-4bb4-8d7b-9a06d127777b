package com.yjsoft.roadtravel.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

/**
 * 应用通用渐变背景组件
 * 实现多层渐变效果，对应CSS样式：
 * - linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0))
 * - linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0))
 * - linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1))
 */
@Composable
fun GradientBackground(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            // 第一层渐变：to left top
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0x3387EFFF), // rgba(135, 239, 255, 0.2)
                        Color(0x00FFFFFF)  // rgba(255, 255, 255, 0)
                    ),
                    start = Offset.Infinite,
                    end = Offset.Zero
                )
            )
            // 第二层渐变：to bottom right
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0x33FFFFDC), // rgba(255, 255, 220, 0.2)
                        Color(0x00FFFFFF)  // rgba(255, 255, 255, 0)
                    ),
                    start = Offset.Zero,
                    end = Offset.Infinite
                )
            )
            // 第三层渐变：to bottom
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0x1AFFFFDC), // rgba(255, 255, 220, 0.1)
                        Color(0x1A87EFFF)  // rgba(135, 239, 255, 0.1)
                    ),
                    start = Offset(0f, 0f),
                    end = Offset(0f, Float.POSITIVE_INFINITY)
                )
            )
    ) {
        content()
    }
}

/**
 * 渐变背景的颜色定义
 * 方便统一管理和修改
 */
object GradientColors {
    // 第一层渐变颜色 (to left top)
    val lightBlue = Color(0x3387EFFF) // rgba(135, 239, 255, 0.2)
    val transparent = Color(0x00FFFFFF) // rgba(255, 255, 255, 0)
    
    // 第二层渐变颜色 (to bottom right)
    val lightYellow = Color(0x33FFFFDC) // rgba(255, 255, 220, 0.2)
    
    // 第三层渐变颜色 (to bottom)
    val lightYellowFaint = Color(0x1AFFFFDC) // rgba(255, 255, 220, 0.1)
    val lightBlueFaint = Color(0x1A87EFFF) // rgba(135, 239, 255, 0.1)
}
