package com.yjsoft.roadtravel.ui.activities.aichat.models

import com.google.gson.annotations.SerializedName
import com.yjsoft.roadtravel.basiclibrary.network.models.ApiResponse

typealias ChatHistoryResponse = ApiResponse<ChatHistoryData>

data class ChatHistoryData(
    val list: List<ChatHistoryItem>
)

data class ChatHistoryItem(
    @SerializedName("created_at")
    val createdAt:Int,
    val role: String,
    @SerializedName("request_id")
    val requestId: String,
    @SerializedName("need_more")
    val needMore: Boolean,
    val content: String,
    @SerializedName("content_type")
    val contentType: String
)