package com.yjsoft.roadtravel.ui.activities.plan.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Map
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.map.compose.SimpleAMapView
import com.yjsoft.roadtravel.basiclibrary.map.compose.PolylineAMapView
import com.yjsoft.roadtravel.ui.activities.plan.models.Route

/**
 * 地图容器组件
 * 集成高德地图显示行程位置和路线
 *
 * @param latitude 纬度
 * @param longitude 经度
 * @param routes 路线数据列表（可选）
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@Composable
fun MapContainer(
    latitude: Double,
    longitude: Double,
    routes: List<Route> = emptyList(),
    modifier: Modifier = Modifier
) {
    // 根据是否有路线数据选择不同的地图组件
    if (routes.isNotEmpty()) {
        // 有路线数据时使用支持Polyline的地图组件
        PolylineAMapView(
            latitude = latitude,
            longitude = longitude,
            zoom = 12f, // 有路线时使用较小的缩放级别以显示更大范围
            routes = routes,
            modifier = modifier
        )

        // 记录路线地图初始化
        LaunchedEffect(latitude, longitude, routes.size) {
            LogManager.d("MapContainer", "路线地图初始化: ($latitude, $longitude), 路线数: ${routes.size}")
        }
    } else {
        // 没有路线数据时使用简单地图组件
        SimpleAMapView(
            latitude = latitude,
            longitude = longitude,
            zoom = 15f,
            modifier = modifier
        )

        // 记录简单地图初始化
        LaunchedEffect(latitude, longitude) {
            LogManager.d("MapContainer", "简单地图初始化: ($latitude, $longitude)")
        }
    }
}


