package com.yjsoft.roadtravel.ui.activities.favorite

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.ui.components.TitleBar
import com.yjsoft.roadtravel.ui.activities.favorite.components.FavoriteItemCard
import com.yjsoft.roadtravel.ui.fragments.home.models.PlanItem

/**
 * 收藏行程主屏幕
 *
 * 功能：
 * - 显示收藏列表
 * - 支持下拉刷新
 * - 支持取消收藏
 * - 支持跳转详情
 *
 * @param viewModel 页面ViewModel
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoritePlansScreen(
    viewModel: FavoritePlansViewModel,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 标题栏
        TitleBar(
            title = "我的收藏",
            leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
            onLeftIconClick = { viewModel.onBackPressed() },
            backgroundColor = Color.White
        )

        // 内容区域
        when (uiState) {
            is UiState.Loading -> {
                LoadingContent()
            }
            is UiState.Success -> {
                val successState = uiState as UiState.Success<FavoritePlansUiState>
                val data = successState.data
                if (data.error != null) {
                    // 有错误信息
                    ErrorContent(
                        error = data.error,
                        onRetry = { viewModel.refresh() }
                    )
                } else if (data.plansData != null && data.plansData.list.isNotEmpty()) {
                    // 有数据
                    FavoriteListContent(
                        plans = data.plansData.list,
                        onItemClick = { planItem ->
                            viewModel.navigateToPlanDetail(planItem)
                        },
                        onFavoriteClick = { planItem ->
                            viewModel.toggleFavorite(planItem)
                        },
                        onRefresh = { viewModel.refresh() }
                    )
                } else {
                    // 空数据
                    EmptyContent(onRefresh = { viewModel.refresh() })
                }
            }
            is UiState.Error -> {
                ErrorContent(
                    error = "加载失败",
                    onRetry = { viewModel.refresh() }
                )
            }
            else -> {
                // 其他状态
                LoadingContent()
            }
        }
    }
}

/**
 * 加载中内容
 */
@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "加载中...",
                fontSize = 14.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 收藏列表内容 - 使用列表布局展示收藏卡片
 */
@Composable
private fun FavoriteListContent(
    plans: List<PlanItem>,
    onItemClick: (PlanItem) -> Unit,
    onFavoriteClick: (PlanItem) -> Unit,
    onRefresh: () -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        itemsIndexed(plans) { index, plan ->
            FavoriteItemCard(
                planItem = plan,
                onItemClick = { onItemClick(plan) },
                onFavoriteClick = { onFavoriteClick(plan) },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 空状态内容
 */
@Composable
private fun EmptyContent(onRefresh: () -> Unit) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "暂无收藏的行程",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "快去首页收藏喜欢的行程吧",
                fontSize = 14.sp,
                color = Color.Gray
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRefresh,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("刷新")
            }
        }
    }
}

/**
 * 错误状态内容
 */
@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "加载失败",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Red
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = error,
                fontSize = 14.sp,
                color = Color.Gray
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("重试")
            }
        }
    }
}
