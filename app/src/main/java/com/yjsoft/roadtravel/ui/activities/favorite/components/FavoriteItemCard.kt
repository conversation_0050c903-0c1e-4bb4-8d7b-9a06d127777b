package com.yjsoft.roadtravel.ui.activities.favorite.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.ui.fragments.home.models.PlanItem

/**
 * 收藏页面专用卡片组件 - 参考Material Design 3风格
 *
 * @param planItem 行程数据
 * @param onItemClick 卡片点击事件
 * @param onFavoriteClick 收藏按钮点击事件
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoriteItemCard(
    planItem: PlanItem,
    onItemClick: () -> Unit,
    onFavoriteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onItemClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 顶部：日期 + 删除按钮
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "2025.8.1", // 固定日期，可以后续从数据中提取
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
                IconButton(onClick = onFavoriteClick) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "取消收藏",
                        tint = if (planItem.isOwn) Color.Red else Color.Gray
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 城市线路
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = extractStartCity(planItem.subject),
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    Icons.Default.ArrowForward,
                    contentDescription = "路线",
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = extractEndCity(planItem.subject),
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 数据行：天数 / 夜数 / 景点 / 出行方式
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                InfoColumn(number = "${extractDays(planItem)}天", label = "时间")
                InfoColumn(number = "${extractDays(planItem) - 1}夜", label = "住宿")
                InfoColumn(number = "${extractAttractions(planItem)}个", label = "景点")
                InfoColumn(number = extractTravelMode(planItem), label = "出行")
            }
        }
    }
}

/**
 * 信息列组件
 */
@Composable
private fun InfoColumn(number: String, label: String) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = number,
            style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold)
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall
        )
    }
}



/**
 * 从 subject 中提取出发城市
 */
private fun extractStartCity(subject: String): String {
    // 尝试从标题中提取城市信息
    // 例如："上海到昆明3日游" -> "上海"
    val cities = listOf("上海", "北京", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "西安", "昆明", "大理", "丽江")
    return cities.find { subject.contains(it) } ?: "上海"
}

/**
 * 从 subject 中提取目的地城市
 */
private fun extractEndCity(subject: String): String {
    // 尝试从标题中提取目的地信息
    val cities = listOf("昆明", "大理", "丽江", "西安", "成都", "重庆", "杭州", "南京", "苏州", "上海", "北京", "广州", "深圳")
    val startCity = extractStartCity(subject)
    return cities.find { it != startCity && subject.contains(it) } ?: "昆明"
}

/**
 * 从 subject 或 tags 中提取天数
 */
private fun extractDays(planItem: PlanItem): Int {
    // 从标题中提取天数，例如："3日游" -> 3
    val dayPattern = Regex("(\\d+)[日天]")
    val match = dayPattern.find(planItem.subject)
    return match?.groupValues?.get(1)?.toIntOrNull() ?: 1
}

/**
 * 从 tags 中提取景点数量
 */
private fun extractAttractions(planItem: PlanItem): Int {
    // 从 tags 中查找景点相关信息，或使用默认值
    return planItem.tags.find { it.contains("景点") || it.contains("个") }
        ?.replace(Regex("[^0-9]"), "")?.toIntOrNull() ?: 12
}

/**
 * 从 tags 中提取出行方式
 */
private fun extractTravelMode(planItem: PlanItem): String {
    val modes = listOf("自驾", "跟团", "自由行", "包车", "火车", "飞机")
    return planItem.tags.find { tag -> modes.any { tag.contains(it) } } ?: "自驾"
}
