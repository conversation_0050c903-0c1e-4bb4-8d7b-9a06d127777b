package com.yjsoft.roadtravel.ui.activities.plan.extensions

import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailData

/**
 * 行程详情项数据
 * 用于在详情面板中显示各种信息
 */
data class PlanDetailItem(
    val icon: String,
    val label: String,
    val value: String,
    val type: PlanDetailItemType = PlanDetailItemType.TEXT
)

/**
 * 详情项类型
 */
enum class PlanDetailItemType {
    TEXT,       // 普通文本
    DURATION,   // 时长
    PRICE,      // 价格
    DISTANCE,   // 距离
    COUNT       // 数量
}

/**
 * 扩展函数：将PlanDetailData转换为详情项列表
 */
fun PlanDetailData.toDetailItems(): List<PlanDetailItem> {
    return listOf(
        PlanDetailItem(
            icon = "📅",
            label = "时长",
            value = "${days}天",
            type = PlanDetailItemType.DURATION
        ),
        PlanDetailItem(
            icon = "🏛️",
            label = "景点",
            value = "${sceneNum}个景点",
            type = PlanDetailItemType.COUNT
        ),
        PlanDetailItem(
            icon = "🚗",
            label = "类型",
            value = transport,
            type = PlanDetailItemType.TEXT
        ),
        PlanDetailItem(
            icon = "📍",
            label = "总行程",
            value = "${distance}公里",
            type = PlanDetailItemType.DISTANCE
        ),
        PlanDetailItem(
            icon = "👥",
            label = "适合人群",
            value = fitFor.ifEmpty { "所有人群" },
            type = PlanDetailItemType.TEXT
        ),
        PlanDetailItem(
            icon = "💰",
            label = "人均",
            value = budget.ifEmpty { "¥${String.format("%.0f", cost)}" },
            type = PlanDetailItemType.PRICE
        )
    )
}

/**
 * 扩展函数：获取轮播图列表
 */
fun PlanDetailData.getBanners(): List<String> = covers

/**
 * 扩展函数：获取地图坐标（使用第一个zone的坐标，如果没有则使用默认坐标）
 */
fun PlanDetailData.getMapLatitude(): Double {
    return sections.firstOrNull()?.zones?.firstOrNull()?.lat ?: 31.2304 // 默认上海坐标
}

fun PlanDetailData.getMapLongitude(): Double {
    return sections.firstOrNull()?.zones?.firstOrNull()?.lng ?: 121.4737 // 默认上海坐标
}
