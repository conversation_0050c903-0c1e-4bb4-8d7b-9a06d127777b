package com.yjsoft.roadtravel.ui.activities.plan.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailData
import com.yjsoft.roadtravel.R
import java.util.Locale

/**
 * 行程信息卡片组件
 *
 * 功能：
 * - 显示行程基本信息
 * - 渐变背景效果
 * - 右侧装饰图片
 * - 各种信息项展示
 *
 * @param planDetails 行程详情数据
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@Composable
fun PlanInfoCard(planDetails: PlanDetailData, modifier: Modifier = Modifier) {
    // 外层容器，允许图片超出
    Box(
        modifier = modifier
            .fillMaxWidth()
            .offset(y=(-16).dp)
            .height(140.dp)
    ) {
        // 背景容器 - 圆角背景
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomStart)
                .clip(RoundedCornerShape(10.dp))

                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0x00FFFFFF), // rgba(255,255,255,0) - 完全透明的白色
                            Color(0xADFFFAE4)  // rgba(255,250,228,0.68) - 68%透明度的淡黄色
                        )
                    )
                )
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFA5EAF9), // #A5EAF9 - 浅蓝色
                            Color(0xFFEEF9E9)  // #EEF9E9 - 浅绿色
                        )
                    )
                )
        ) {
            // 内容区域 - 为右侧图片留出空间
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(15.dp)
                    .padding(end = 40.dp), // 为右侧图片留出空间
            ) {
                // 信息项列表
                Column {
                    // 第一行：天数、景点、交通方式
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        PlanInfoItem(
                            iconRes = R.drawable.plan_days,
                            iconSize = 20.dp,
                            text = "${planDetails.days}天",
                            textColor = Color(0xFF333333),
                            iconColor = Color(0xFF4291F7)
                        )

                        PlanInfoItem(
                            iconRes = R.drawable.secnes,
                            iconSize = 20.dp,
                            text = "${planDetails.sceneNum}个景点",
                            textColor = Color(0xFF333333),
                            iconColor = Color(0xFFF3CB49)
                        )

                        PlanInfoItem(
                            iconRes = R.drawable.drive,
                            iconSize = 20.dp,
                            text = planDetails.transport.ifEmpty { "自驾游" },
                            textColor = Color(0xFF333333),
                            iconColor = Color(0xFF72C040)
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    // 第二行：总行程、最佳游玩时间
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        PlanInfoItem(
                            iconRes = R.drawable.all_distance,
                            iconSize = 14.dp,
                            text = "总行程：约${planDetails.distance}公里",
                            textColor = Color(0xFF333333),
                        )

                        PlanInfoItem(
                            iconRes = R.drawable.best_tour_time,
                            iconSize = 14.dp,
                            text = "最佳游玩：${planDetails.fitFor}",
                            textColor = Color(0xFF333333)
                        )
                    }

                    // 第三行：人均费用、查看明细、点赞
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        PlanInfoItem(
                            iconRes = R.drawable.cost,
                            iconSize = 14.dp,
                            text = "人均：¥${String.format(Locale.CHINA, "%.0f", planDetails.cost)}",
                            textColor = Color(0xFF333333)
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        // 查看明细按钮
                        Row(
                            modifier = Modifier
                                .clip(RoundedCornerShape(15.dp))
                                .padding(0.dp)
                                .background(
                                    brush = Brush.verticalGradient(
                                        colors = listOf(
                                            Color(0xFF333333), // rgba(165, 234, 249, 0.7)
                                            Color(0xFF626262)  // rgba(238, 249, 233, 0.2)
                                        )
                                    )
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                modifier = Modifier
                                    .padding(start = 4.dp)
                                    .size(10.dp),
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = Color.White
                            )
                            Spacer(modifier = Modifier.width(1.dp))
                            Text(
                                modifier = Modifier.padding(
                                    top = 0.dp,
                                    bottom = 0.dp,
                                    start = 0.dp,
                                    end = 4.dp
                                ),
                                lineHeight = 16.sp,
                                text = "查看明细",
                                fontSize = 10.sp,
                                color = Color.White
                            )
                        }

                        // 使用Spacer推动点赞按钮到最右侧
                        Spacer(modifier = Modifier.weight(1f))

                        PlanInfoItem(
                            modifier = Modifier.offset(x=40.dp),
                            iconRes = R.drawable.dz,
                            iconSize = 14.dp,
                            text = "点赞",
                            textColor = Color(0xFF333333)
                        )
                    }
                }
            }
        }

        // 右侧装饰图片 - 底部对齐，如果图片更高则向上超出
        Image(
            painter = painterResource(id = R.drawable.details_top_right),
            contentDescription = "装饰图片",
            modifier = Modifier
                .width(98.dp)
                .wrapContentHeight() // 让图片保持原始高度比例
                .align(Alignment.BottomEnd), // 底部右侧对齐，不偏移
            contentScale = ContentScale.Crop // 按宽度适配，保持比例
        )
    }
}

/** 行程信息项组件 - 支持Drawable资源 */
@Composable
private fun PlanInfoItem(
    iconRes: Int,
    iconSize: androidx.compose.ui.unit.Dp,
    text: String,
    textColor: Color,
    iconColor: Color? = Color(0xFF000000),
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            modifier = Modifier.size(iconSize),
            tint = iconColor ?: Color.Black
        )

        Text(
            text = text,
            fontSize =
                when (iconSize) {
                    20.dp -> 14.sp
                    14.dp -> 12.sp
                    else -> 10.sp
                },
            color = textColor,
            maxLines = 1
        )
    }
}

/** 预览组件 */
@Preview(showBackground = true)
@Composable
private fun PlanInfoCardPreview() {
    val mockPlanDetails =
        PlanDetailData(
            subject = "上海5天浪漫之旅",
            days = 5,
            sceneNum = 12,
            transport = "自驾游",
            distance = 120,
            cost = 3500.0
        )

    Column(modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(16.dp)) {
        // 使用Material Icons版本，避免drawable资源问题
        PlanInfoCard(planDetails = mockPlanDetails)
    }
}
