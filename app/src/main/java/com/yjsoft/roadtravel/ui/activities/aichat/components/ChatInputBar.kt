package com.yjsoft.roadtravel.ui.activities.aichat.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Keyboard
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import com.yjsoft.roadtravel.basiclibrary.speech.models.SpeechRecognitionParams
import kotlinx.coroutines.launch

/**
 * AI聊天输入框组件
 * 包含文本输入、语音输入和发送功能
 */
@Composable
fun ChatInputBar(
    modifier: Modifier = Modifier,
    text: String,
    onTextChange: (String) -> Unit,
    onSendMessage: (String) -> Unit,
    onVoiceInput: (String) -> Unit = {},
    placeholder: String = "输入您的问题...",
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val coroutineScope = rememberCoroutineScope()

    // UI状态管理
    var isVoiceMode by remember { mutableStateOf(false) }
    var isRecording by remember { mutableStateOf(false) }
    var permissionMessage by remember { mutableStateOf("") }

    // 语音识别管理器
    val speechManager = remember {
        SpeechRecognitionManager.getInstance(context)
    }

    // 录音动画效果
    val animatedScale by animateFloatAsState(
        targetValue = if (isRecording) 1.05f else 1f,
        animationSpec = tween(200),
        label = "scale"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 4.dp) // 减少底部padding
            .scale(animatedScale)
            .then(
                if (isVoiceMode) {
                    Modifier.pointerInput(Unit) {
                        detectTapGestures(
                            onPress = {
                                // 按下时开始录音
                                coroutineScope.launch {
                                    startRecording(speechManager, onVoiceInput, { error ->
                                        permissionMessage = error
                                    }) { isRecording = true }
                                }

                                // 等待释放
                                tryAwaitRelease()

                                // 释放时停止录音
                                coroutineScope.launch {
                                    stopRecording(speechManager, onVoiceInput, { error ->
                                        permissionMessage = error
                                    }) { isRecording = false }
                                }
                            }
                        )
                    }
                } else {
                    Modifier
                }
            ),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isRecording) 6.dp else 4.dp),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isRecording -> MaterialTheme.colorScheme.errorContainer
                isVoiceMode -> MaterialTheme.colorScheme.primaryContainer
                else -> Color.White
            }
        )
    ) {
        if (isVoiceMode) {
            // 语音模式：简化布局
            VoiceModeLayout(
                isRecording = isRecording,
                onToggleVoiceMode = { isVoiceMode = false },
                enabled = enabled
            )
        } else {
            // 文本模式布局
            TextModeLayout(
                text = text,
                onTextChange = onTextChange,
                onSendMessage = onSendMessage,
                onToggleVoiceMode = {
                    // 切换到语音模式前先检查权限
                    coroutineScope.launch {
                        // 检查权限
                        val speechManager = SpeechRecognitionManager.getInstance(context)
                        if (speechManager.hasRecordPermission()) {
                            isVoiceMode = true
                        } else {
                            permissionMessage = "需要录音权限才能使用语音输入功能"
                        }
                    }
                },
                placeholder = placeholder,
                enabled = enabled
            )
        }
    }

    // 权限提示消息
    if (permissionMessage.isNotEmpty()) {
        LaunchedEffect(permissionMessage) {
            // 3秒后清除消息
            kotlinx.coroutines.delay(3000)
            permissionMessage = ""
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Text(
                text = permissionMessage,
                modifier = Modifier.padding(12.dp),
                color = MaterialTheme.colorScheme.onErrorContainer,
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 文本模式布局
 */
@Composable
private fun TextModeLayout(
    text: String,
    onTextChange: (String) -> Unit,
    onSendMessage: (String) -> Unit,
    onToggleVoiceMode: () -> Unit,
    placeholder: String,
    enabled: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 4.dp)
    ) {
        // 语音输入按钮
        IconButton(
            onClick = onToggleVoiceMode,
            enabled = enabled
        ) {
            Icon(
                imageVector = Icons.Default.Mic,
                contentDescription = "切换到语音输入",
                tint = if (enabled) MaterialTheme.colorScheme.primary else Color.Gray
            )
        }

        // 文本输入框
        Box(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 8.dp)
        ) {
            BasicTextField(
                value = text,
                onValueChange = onTextChange,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                textStyle = TextStyle(
                    fontSize = 16.sp,
                    color = Color(0xFF333333)
                ),
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Send
                ),
                keyboardActions = KeyboardActions(
                    onSend = {
                        if (text.isNotBlank()) {
                            onSendMessage(text.trim())
                        }
                    }
                ),
                enabled = enabled,
                singleLine = false,
                maxLines = 4
            )

            // 占位符
            if (text.isEmpty()) {
                Text(
                    text = placeholder,
                    color = Color(0xFF999999),
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 12.dp)
                )
            }
        }

        // 发送按钮
        IconButton(
            onClick = {
                if (text.isNotBlank()) {
                    onSendMessage(text.trim())
                }
            },
            enabled = enabled && text.isNotBlank()
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.Send,
                contentDescription = "发送",
                tint = if (enabled && text.isNotBlank()) {
                    MaterialTheme.colorScheme.primary
                } else {
                    Color.Gray
                }
            )
        }
    }
}

/**
 * 语音模式布局 - 简化版本
 */
@Composable
private fun VoiceModeLayout(
    isRecording: Boolean,
    onToggleVoiceMode: () -> Unit,
    enabled: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 键盘按钮（切换回文本模式）
        IconButton(
            onClick = onToggleVoiceMode,
            enabled = enabled && !isRecording
        ) {
            Icon(
                imageVector = Icons.Default.Keyboard,
                contentDescription = "切换到键盘输入",
                tint = if (enabled && !isRecording) Color.Black else Color.Gray
            )
        }

        // 中间的提示文字区域
        Box(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 12.dp), // 与文本模式保持一致的高度
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isRecording) "松开发送" else "按住说话",
                color = Color.Black,
                fontSize = 16.sp,
                fontWeight = if (isRecording) FontWeight.Bold else FontWeight.Normal
            )
        }

        // 右侧占位，保持布局平衡
        Spacer(modifier = Modifier.width(48.dp))
    }
}


/**
 * 开始录音
 */
private suspend fun startRecording(
    speechManager: SpeechRecognitionManager,
    onVoiceInput: (String) -> Unit,
    onError: (String) -> Unit,
    onRecordingStarted: () -> Unit
) {
    try {
        LogManager.d("ChatInputBar", "开始录音")

        // 重置管理器状态（重要！）
        speechManager.reset()
        LogManager.d("ChatInputBar", "重置语音识别管理器状态")

        // 短暂延迟确保重置完成
        kotlinx.coroutines.delay(100)

        // 初始化SDK（如果需要）
        if (!speechManager.isSDKInitialized()) {
            val initSuccess = speechManager.initialize()
            if (!initSuccess) {
                onError("语音识别初始化失败")
                LogManager.e("ChatInputBar", "语音识别初始化失败")
                return
            }
        }

        // 使用手动模式参数
        val params = SpeechRecognitionParams(
            enableVad = false
        )

        val success = speechManager.startRecognition(params)
        if (success) {
            onRecordingStarted()
            LogManager.d("ChatInputBar", "录音开始成功")
        } else {
            onError("录音启动失败")
            LogManager.e("ChatInputBar", "录音启动失败")
        }
    } catch (e: Exception) {
        onError("录音启动异常: ${e.message}")
        LogManager.e("ChatInputBar", "录音启动异常", e)
    }
}

/**
 * 停止录音
 */
private suspend fun stopRecording(
    speechManager: SpeechRecognitionManager,
    onVoiceInput: (String) -> Unit,
    onError: (String) -> Unit,
    onRecordingStopped: () -> Unit
) {
    try {
        LogManager.d("ChatInputBar", "停止录音")
        speechManager.stopRecognition()
        onRecordingStopped()

        // 等待识别结果（简化处理）
        kotlinx.coroutines.delay(2000) // 等待2秒获取结果

        val result = speechManager.result.value
        if (result.isSuccess && result.isFinal && result.text.isNotEmpty()) {
            LogManager.d("ChatInputBar", "语音识别结果: ${result.text}")
            onVoiceInput(result.text)
        } else {
            LogManager.w("ChatInputBar", "未获取到有效的识别结果")
        }

    } catch (e: Exception) {
        onError("录音停止异常: ${e.message}")
        LogManager.e("ChatInputBar", "录音停止异常", e)
        onRecordingStopped()
    }
}
