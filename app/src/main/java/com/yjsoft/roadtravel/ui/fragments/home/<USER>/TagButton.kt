package com.yjsoft.roadtravel.ui.fragments.home.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 热门标签按钮组件
 */
@Composable
fun TagButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) Color(0xFF1890FF) else Color(0xFFFFF7D4)
    val textColor = if (isSelected) Color.White else Color(0xFFE6CC86)
    
    Text(
        text = text,
        color = textColor,
        fontSize = 14.sp,
        modifier = modifier
            .clip(RoundedCornerShape(20.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 6.dp)
    )
}

/**
 * 热门标签列表组件
 */
@Composable
fun HotTagsList(
    tags: List<String>,
    selectedIndex: Int,
    onTagSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    if (tags.isNotEmpty()) {
        LazyRow(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            itemsIndexed(tags) { index, tag ->
                TagButton(
                    text = tag,
                    isSelected = index == selectedIndex,
                    onClick = { onTagSelected(index) }
                )
            }
        }
    }
} 