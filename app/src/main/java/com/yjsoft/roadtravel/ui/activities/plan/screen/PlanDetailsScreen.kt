package com.yjsoft.roadtravel.ui.activities.plan.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImageWithPlaceholder
import com.yjsoft.roadtravel.ui.activities.plan.PlanDetailsViewModel
import com.yjsoft.roadtravel.ui.activities.plan.components.PlanInfoCard
import com.yjsoft.roadtravel.ui.activities.plan.components.TopContainer
import com.yjsoft.roadtravel.ui.activities.plan.extensions.getBanners
import com.yjsoft.roadtravel.ui.activities.plan.extensions.getMapLatitude
import com.yjsoft.roadtravel.ui.activities.plan.extensions.getMapLongitude
import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailData
import com.yjsoft.roadtravel.ui.activities.plan.models.Section
import com.yjsoft.roadtravel.ui.activities.plan.models.Timeline

// 时间线颜色数组，按照指定顺序循环使用
private val timelineColors = listOf(
    Color(0xFF1890FF), // 蓝色
    Color(0xFF52C41A), // 绿色
    Color(0xFFFA8C16), // 橙色
    Color(0xFF722ED1), // 紫色
    Color(0xFFEB2F96), // 粉色
    Color(0xFF13C2C2), // 青色
    Color(0xFFF5222D), // 红色
    Color(0xFFFAAD14)  // 黄色
)

/**
 * 根据天数获取对应的时间线颜色
 */
private fun getTimelineColor(dayNumber: Int): Color {
    return timelineColors[(dayNumber - 1) % timelineColors.size]
}

/**
 * 行程详情主屏幕
 *
 * 功能：
 * - 整合顶部容器和详情面板
 * - 管理布局和状态
 * - 处理用户交互
 *
 * @param viewModel 页面ViewModel
 * @param modifier 修饰符
 *
 * <AUTHOR> Team
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlanDetailsScreen(
    viewModel: PlanDetailsViewModel, modifier: Modifier = Modifier
) {
    // 收集UI状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val windowInfo = LocalWindowInfo.current
    val screenHeight = with(LocalDensity.current) {
        windowInfo.containerSize.height.toDp()
    }

    if (uiState.topDisplayMode == PlanDetailsViewModel.TopDisplayMode.MAP) {
        // 地图模式：地图全屏显示，详情面板浮动在上层
        val density = LocalDensity.current

        // 详情面板高度范围
        val minBottomSheetHeight = 200.dp // 最小面板高度
        val maxBottomSheetHeight = screenHeight - 100.dp // 最大面板高度，留100dp给地图
        val defaultBottomSheetHeight = screenHeight * 0.4f // 默认占屏幕40%

        // 当前底部面板高度状态
        var currentBottomSheetHeight by remember { mutableStateOf(defaultBottomSheetHeight) }

        Box(modifier = modifier.fillMaxSize()) {
            // 全屏地图背景
            TopContainer(
                displayMode = uiState.topDisplayMode,
                banners = uiState.planDetails?.getBanners() ?: emptyList(),
                mapLatitude = uiState.planDetails?.getMapLatitude() ?: 0.0,
                mapLongitude = uiState.planDetails?.getMapLongitude() ?: 0.0,
                routes = uiState.planPolyline?.routes ?: emptyList(),
                onModeToggle = { viewModel.toggleTopDisplayMode() },
                onBackClick = { viewModel.onBackPressed() },
                modifier = Modifier.fillMaxSize() // 地图全屏
            )

            // 浮动的详情面板
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(currentBottomSheetHeight)
                    .align(Alignment.BottomCenter), // 对齐到底部
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 12.dp) // 增加阴影，突出浮动效果
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 可拖拽的手柄区域
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(32.dp) // 增加拖拽区域高度
                            .draggable(
                                orientation = Orientation.Vertical,
                                state = rememberDraggableState { delta ->
                                    // 拖拽逻辑：向上拖拽增加面板高度，向下拖拽减少面板高度
                                    val deltaInDp = with(density) { -delta.toDp() }
                                    val newHeight = (currentBottomSheetHeight + deltaInDp).coerceIn(
                                        minimumValue = minBottomSheetHeight,
                                        maximumValue = maxBottomSheetHeight
                                    )
                                    currentBottomSheetHeight = newHeight
                                }
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        // 拖拽手柄视觉指示器
                        Box(
                            modifier = Modifier
                                .width(60.dp)
                                .height(5.dp)
                                .background(
                                    Color.Gray.copy(alpha = 0.4f),
                                    RoundedCornerShape(2.5.dp)
                                )
                        )
                    }

                    // 详情内容
                    DetailsContent(
                        planDetails = uiState.planDetails,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    )
                }
            }
        }
    } else {
        // 轮播模式：使用普通布局
        Box(modifier = modifier.fillMaxSize()) {
            // 顶部容器（轮播图）
            TopContainer(
                displayMode = uiState.topDisplayMode,
                banners = uiState.planDetails?.getBanners() ?: emptyList(),
                mapLatitude = uiState.planDetails?.getMapLatitude() ?: 0.0,
                mapLongitude = uiState.planDetails?.getMapLongitude() ?: 0.0,
                routes = uiState.planPolyline?.routes ?: emptyList(),
                onModeToggle = { viewModel.toggleTopDisplayMode() },
                onBackClick = { viewModel.onBackPressed() })

            // 在轮播模式下，在底部显示详情内容
            val topContainerHeight = PlanDetailsViewModel.CAROUSEL_HEIGHT_DP.dp
            val offsetY = topContainerHeight - PlanDetailsViewModel.DETAILS_OFFSET_DP.dp

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(screenHeight - offsetY) // 计算正确的高度，避免内容被截断
                    .offset(y = offsetY),
                shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                DetailsContent(
                    planDetails = uiState.planDetails,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}

/**
 * 详情内容组件
 */
@Composable
private fun DetailsContent(
    modifier: Modifier = Modifier,
    planDetails: PlanDetailData?
) {
    var selectedDay by remember { mutableIntStateOf(1) }

    if (planDetails != null) {
        // 所有天数的行程列表（包含吸顶tab和所有内容）
        AllDaysItineraryList(
            planDetails = planDetails,
            selectedDay = selectedDay,
            onDaySelected = { day ->
                selectedDay = day
            },
            modifier = modifier.fillMaxSize()
        )

    } else {
        // 加载状态
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(200.dp),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}

/**
 * 天数Tab行组件
 */
@Composable
private fun DaysTabRow(
    days: Int,
    selectedDay: Int,
    onDaySelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(20.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(days) { index ->
            val dayNumber = index + 1
            val isSelected = dayNumber == selectedDay

            DayTab(
                dayNumber = dayNumber,
                isSelected = isSelected,
                onClick = { onDaySelected(dayNumber) }
            )
        }
    }
}

/**
 * 单个天数Tab组件
 */
@Composable
private fun DayTab(
    dayNumber: Int,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(width = 42.dp, height = 32.dp)
            .clip(RoundedCornerShape(10.dp))
            .background(
                color = if (isSelected) Color(0xFFE7F4FF) else Color.Transparent
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // 底部指示器（仅在选中时显示）
        if (isSelected) {
            Box(
                modifier = Modifier
                    .size(width = 42.dp, height = 14.dp)
                    .align(Alignment.BottomCenter)
                    .clip(RoundedCornerShape(7.dp))
                    .background(Color(0xFFFACA14))
            )
        }

        // 文本 - 使用不同字体大小，基线对齐
        Row(
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = "第",
                fontSize = 12.sp,
                color = Color(0xFF694209),
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                modifier = Modifier.alignByBaseline()
            )
            Text(
                modifier = Modifier
                    .padding(start = 2.dp, end = 2.dp)
                    .alignByBaseline(),
                text = "$dayNumber",
                fontSize = 20.sp,
                color = Color(0xFF694209),
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
            )
            Text(
                text = "天",
                fontSize = 12.sp,
                color = Color(0xFF694209),
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                modifier = Modifier.alignByBaseline()
            )
        }
    }
}

/**
 * 所有天数行程列表组件（包含吸顶tab和所有内容）
 */
@Composable
private fun AllDaysItineraryList(
    modifier: Modifier = Modifier,
    planDetails: PlanDetailData,
    selectedDay: Int,
    onDaySelected: (Int) -> Unit
) {
    val listState = rememberLazyListState()
    val sections = planDetails.sections

    // 吸顶tab的高度（包括padding）
    val density = LocalDensity.current
    val stickyTabHeightPx = with(density) { 56.dp.toPx().toInt() }

    // 使用 derivedStateOf 来计算当前应该选中的天数
    val calculatedDay by remember {
        derivedStateOf {
            val firstVisibleItemIndex = listState.firstVisibleItemIndex
            calculateCurrentDay(firstVisibleItemIndex, sections)
        }
    }

    // 监听计算出的天数变化，更新选中状态
    LaunchedEffect(calculatedDay) {
        if (calculatedDay != selectedDay && calculatedDay > 0) {
            onDaySelected(calculatedDay)
        }
    }

    // 天数tab的原始位置索引（标题、行程信息、注意事项之后）
    val tabOriginalIndex = 3

    // 更自然的判断逻辑：当滚动接近原始tab位置时就开始显示吸顶tab
    val shouldShowStickyTab =
        remember { derivedStateOf { listState.firstVisibleItemIndex } }.value >= tabOriginalIndex

    Box(modifier = modifier) {
        // 所有内容的列表
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize()
        ) {
            // 标题
            item(key = "title") {
                Text(
                    text = planDetails.subject.ifEmpty { "行程详情" },
                    fontSize = 18.sp,
                    color = Color(0xFF333333),
                    fontWeight = FontWeight.W500,
                    modifier = Modifier.padding(start = 16.dp, top = 16.dp, end = 16.dp)
                )
            }

            // 行程信息卡片
            item(key = "plan_info") {
                PlanInfoCard(
                    planDetails = planDetails,
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .offset(y = (-10).dp)
                )
            }

            // 注意事项
            item(key = "notice") {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .offset(y = (-15).dp)
                        .background(
                            color = Color(0xFFFFFCF0),
                            shape = RoundedCornerShape(10.dp)
                        )
                ) {
                    Column(modifier = Modifier.padding(10.dp)) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = R.drawable.attentions),
                                contentDescription = "装饰图片",
                                modifier = Modifier
                                    .width(20.dp)
                                    .height(20.dp)
                                    .wrapContentHeight(),
                                contentScale = ContentScale.Crop
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                lineHeight = 18.sp,
                                text = "出行注意事项",
                                fontSize = 14.sp,
                                color = Color(0xFFFFA01D)
                            )
                        }
                        Text(
                            modifier = Modifier.padding(
                                start = 14.dp, end = 14.dp, top = 10.dp, bottom = 5.dp
                            ),
                            text = planDetails.notice,
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        )
                    }
                }
            }

            // 原始位置的天数tab
            item(key = "days_tab_original") {
                val coroutineScope = rememberCoroutineScope()
                DaysTabRow(
                    days = planDetails.days,
                    selectedDay = selectedDay,
                    onDaySelected = { day ->
                        onDaySelected(day)
                        // 滚动到对应的天数位置
                        val targetIndex = calculateTargetIndex(day, sections)
                        if (targetIndex >= 0) {
                            coroutineScope.launch {
                                // 如果吸顶tab可见，需要考虑其占用的高度偏移
                                if (shouldShowStickyTab) {
                                    // 滚动到目标位置，并添加偏移量避免被吸顶tab遮挡
                                    listState.animateScrollToItem(
                                        index = targetIndex,
                                        scrollOffset = -stickyTabHeightPx
                                    )
                                } else {
                                    listState.animateScrollToItem(targetIndex)
                                }
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 6.dp)
                )
            }

            // 所有天数的行程
            sections.forEachIndexed { dayIndex, section ->
                // 天数标题
                item(key = "day_title_$dayIndex") {
                    DayTitle(
                        title = "${section.sectionName}${section.subject}",
                        dayNumber = dayIndex + 1,
                        isFirst = dayIndex == 0,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }

                // 该天的时间线列表
                section.timeline.forEachIndexed { timelineIndex, timeline ->
                    item(key = "timeline_${dayIndex}_${timelineIndex}_${timeline.itemId}") {
                        TimelineItem(
                            timeline = timeline,
                            dayNumber = dayIndex + 1,
                            isLastInDay = timelineIndex == section.timeline.size - 1,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                    }
                }

                // 天数之间的间距
                if (dayIndex < sections.size - 1) {
                    item(key = "spacer_$dayIndex") {
                        Spacer(modifier = Modifier.height(24.dp))
                    }
                }
            }
        }

        // 带动画的吸顶tab
        AnimatedVisibility(
            visible = shouldShowStickyTab,
            enter = slideInVertically(
                initialOffsetY = { -it },
                animationSpec = tween(durationMillis = 300, easing = EaseOutCubic)
            ) + fadeIn(
                animationSpec = tween(durationMillis = 300)
            ),
            exit = slideOutVertically(
                targetOffsetY = { -it },
                animationSpec = tween(durationMillis = 300, easing = EaseInCubic)
            ) + fadeOut(
                animationSpec = tween(durationMillis = 300)
            ),
            modifier = Modifier.align(Alignment.TopCenter)
        ) {
            val coroutineScope = rememberCoroutineScope()
            DaysTabRow(
                days = planDetails.days,
                selectedDay = selectedDay,
                onDaySelected = { day ->
                    onDaySelected(day)
                    // 滚动到对应的天数位置
                    val targetIndex = calculateTargetIndex(day, sections)
                    if (targetIndex >= 0) {
                        coroutineScope.launch {
                            // 吸顶tab点击时，总是需要考虑偏移量避免遮挡
                            listState.animateScrollToItem(
                                index = targetIndex,
                                scrollOffset = -stickyTabHeightPx
                            )
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color.White.copy(alpha = 0.95f), // 半透明背景
                        shape = RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp)
                    )
                    .padding(vertical = 8.dp)
            )
        }
    }
}


/**
 * 根据滚动位置计算当前应该选中的天数
 */
private fun calculateCurrentDay(firstVisibleItemIndex: Int, sections: List<Section>): Int {
    // 前面有4个固定item：标题、行程信息、注意事项、天数tab
    val fixedItemsCount = 4

    if (firstVisibleItemIndex < fixedItemsCount) {
        return 1 // 如果还在固定内容区域，返回第1天
    }

    // 计算每一天的起始和结束索引，找到当前可见item属于哪一天
    var currentIndex = fixedItemsCount

    sections.forEachIndexed { dayIndex, section ->
        val dayStartIndex = currentIndex // 天数标题的索引
        val dayItemCount = 1 + section.timeline.size // 天数标题 + timeline items
        val dayEndIndex = currentIndex + dayItemCount - 1

        // 如果当前可见item在这一天的范围内，返回这一天
        if (firstVisibleItemIndex >= dayStartIndex && firstVisibleItemIndex <= dayEndIndex) {
            return dayIndex + 1
        }

        // 移动到下一天的起始位置
        currentIndex += dayItemCount
        if (dayIndex < sections.size - 1) {
            currentIndex += 1 // 间距
        }
    }

    // 如果超出了所有天数的范围，返回最后一天
    return sections.size
}

/**
 * 根据天数计算目标滚动位置
 */
private fun calculateTargetIndex(day: Int, sections: List<Section>): Int {
    if (day <= 0 || day > sections.size) return 0

    // 前面有4个固定item：标题、行程信息、注意事项、天数tab
    var targetIndex = 4

    for (i in 0 until day - 1) {
        // 天数标题占1个item
        targetIndex += 1
        // 该天的timeline items
        targetIndex += sections[i].timeline.size
        // 间距占1个item
        targetIndex += 1
    }
    return targetIndex
}

/**
 * 天数标题组件
 */
@Composable
private fun DayTitle(
    modifier: Modifier = Modifier,
    title: String,
    dayNumber: Int,
    isFirst: Boolean = false
) {
    val timelineColor = getTimelineColor(dayNumber)

    Row(
        modifier = modifier.padding(start = 0.dp, top = 6.dp)
    ) {
        // 左侧时间线布局，与TimelineItem保持一致
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(16.dp)
        ) {
            Spacer(modifier = Modifier.height(20.dp))
            // 天数标题的圆点
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = timelineColor,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
            )
            // 下连接线，连接到第一个时间线 - 调整高度确保完美连接
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(30.dp)
                    .background(timelineColor)
            )
        }
        Spacer(modifier = Modifier.width(12.dp))
        // 标题文本
        Text(
            modifier = Modifier.offset(y = (14).dp),
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF333333)
        )
    }
}

/**
 * 时间线项目组件
 */
@Composable
private fun TimelineItem(
    modifier: Modifier = Modifier,
    timeline: Timeline,
    dayNumber: Int,
    isLastInDay: Boolean = false
) {
    val timelineColor = getTimelineColor(dayNumber)

    Row(
        modifier = modifier.fillMaxWidth()
        // 移除 padding(vertical = 4.dp) 避免连接线间隙
    ) {
        // 左侧时间线
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(16.dp)
        ) {
            // 上半部分连接线 - 增加高度补偿padding
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(68.dp) // 64 + 4，补偿上方的padding
                    .background(timelineColor)
            )

            // 圆点
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = timelineColor,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
            )
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(68.dp) // 64 + 4，补偿下方的padding
                    .background(timelineColor)
            )
        }
        Spacer(modifier = Modifier.width(4.dp))
        // 右侧内容 - 添加垂直padding到内容区域
        TimelineContent(
            timeline = timeline,
            dayNumber = dayNumber,
            modifier = Modifier
                .weight(1f)
                .padding(vertical = 4.dp) // 将padding移到内容区域
        )
    }
}

/**
 * 时间线内容组件
 */
@Composable
private fun TimelineContent(
    timeline: Timeline,
    dayNumber: Int,
    modifier: Modifier = Modifier
) {
    // 根据type决定背景颜色
    val backgroundColor = if (timeline.type == "text") {
        Color(0xFFF7F7F9) // text类型使用F7F7F9
    } else {
        getTimelineColor(dayNumber).copy(alpha = 0.05f) // 其他类型使用当天颜色的5%透明度
    }

    // 边框颜色：该天颜色的8%透明度
    val borderColor = getTimelineColor(dayNumber).copy(alpha = 0.08f)

    Card(
        modifier = modifier
            .height(128.dp) // 修改为128dp，符合设计要求
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp) // 去除阴影
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // 主要内容区域
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(12.dp)
            ) {
                // 左侧图片（如果有）
                if (timeline.pics.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .size(68.dp)
                            .clip(RoundedCornerShape(6.dp))
                            .background(Color(0xFFE0E0E0))
                    ) {
                        NetworkImageWithPlaceholder(
                            modifier = Modifier
                                .fillMaxSize(),
                            url = timeline.pics.first(),
                            contentDescription = "时间线图片",
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))
                }

                // 右侧内容
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 时间和标题
                    Text(
                        text = "${timeline.time}: ${timeline.title}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333),
                        maxLines = 1
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    // 描述
                    Text(
                        text = timeline.desc,
                        fontSize = 12.sp,
                        color = Color(0xFF333333),
                        maxLines = 2
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 标签行
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 价格标签
//                        if (timeline.avgPrice > 0) {
//                            TagChip(
//                                text = "¥${(timeline.avgPrice) / 100}起",
//                                backgroundColor = Color(0xFFE3F2FD),
//                                textColor = Color(0xFF1976D2)
//                            )
//                        }

                        // 时长标签
//                        if (timeline.scene?.costTime != null) {
//                            val costTime = timeline.scene.costTime
//                            val timeText = if (costTime.standard > 0) {
//                                "${costTime.standard.toInt()}小时"
//                            } else "建议1小时"
//
//                            TagChip(
//                                text = timeText,
//                                backgroundColor = Color(0xFFFFF3E0),
//                                textColor = Color(0xFFE65100)
//                            )
//                        }

                        // 类型标签
                        if (timeline.tags?.isNotEmpty() ?: false) {
                            timeline.tags.forEachIndexed { index, tag ->
                                TagChip(
                                    text = tag,
                                    backgroundColor = getTimelineColor(index % timelineColors.size + 1).copy(
                                        0.1f
                                    ),
                                    textColor = getTimelineColor(index % timelineColors.size + 1).copy(
                                        0.8f
                                    )
                                )
                            }
                        }
                    }
                }


            }
            // 右上角装饰区域
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .align(Alignment.TopEnd)
                    .clip(RoundedCornerShape(topEnd = 8.dp, bottomStart = 8.dp))
                    .background(getTimelineColor(dayNumber)),
                contentAlignment = Alignment.Center
            ) {
                // 根据type选择对应的图标
                val iconRes = when (timeline.type) {
                    "scene" -> R.drawable.ancient_gate_fill
                    "text" -> R.drawable.ancient_pic_fill
                    "hotel" -> R.drawable.ancient_bed_fill
                    else -> R.drawable.ancient_gate_fill // 默认使用scene图标
                }

                Image(
                    painter = painterResource(id = iconRes),
                    contentDescription = "类型图标",
                    modifier = Modifier.size(16.dp),
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

/**
 * 标签芯片组件
 */
@Composable
private fun TagChip(
    text: String,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(4.dp)
            )
            .padding(horizontal = 6.dp, vertical = 2.dp)
    ) {
        Text(
            lineHeight = 10.sp,
            text = text,
            fontWeight = FontWeight.Medium,
            fontSize = 10.sp,
            color = textColor
        )
    }
}

/**
 * 天数Tab预览组件
 */
@Preview(showBackground = true)
@Composable
private fun DaysTabRowPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("3天行程的Tab:")
        DaysTabRow(
            days = 3,
            selectedDay = 1,
            onDaySelected = { },
            modifier = Modifier.fillMaxWidth()
        )

        Text("5天行程的Tab:")
        DaysTabRow(
            days = 5,
            selectedDay = 3,
            onDaySelected = { },
            modifier = Modifier.fillMaxWidth()
        )

        Text("单个Tab预览:")
        Row(horizontalArrangement = Arrangement.spacedBy(20.dp)) {
            DayTab(
                dayNumber = 1,
                isSelected = true,
                onClick = { }
            )
            DayTab(
                dayNumber = 2,
                isSelected = false,
                onClick = { }
            )
        }

        Text("时间线颜色预览:")
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            repeat(8) { index ->
                val dayNumber = index + 1
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(16.dp)
                            .background(
                                color = getTimelineColor(dayNumber),
                                shape = androidx.compose.foundation.shape.CircleShape
                            )
                    )
                    Text(
                        text = "第${dayNumber}天",
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    )
                }
            }
        }

        Text("时间线卡片背景和边框预览:")
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            // text类型背景预览
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp, 20.dp)
                        .border(
                            width = 1.dp,
                            color = getTimelineColor(1).copy(alpha = 0.08f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .background(
                            color = Color(0xFFF7F7F9),
                            shape = RoundedCornerShape(4.dp)
                        )
                )
                Text(
                    text = "text类型: #F7F7F9背景 + 8%边框",
                    fontSize = 12.sp,
                    color = Color(0xFF333333)
                )
            }

            // 其他类型背景预览（前3天）
            repeat(3) { index ->
                val dayNumber = index + 1
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(40.dp, 20.dp)
                            .border(
                                width = 1.dp,
                                color = getTimelineColor(dayNumber).copy(alpha = 0.08f),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .background(
                                color = getTimelineColor(dayNumber).copy(alpha = 0.05f),
                                shape = RoundedCornerShape(4.dp)
                            )
                    )
                    Text(
                        text = "第${dayNumber}天: 5%背景 + 8%边框",
                        fontSize = 12.sp,
                        color = Color(0xFF333333)
                    )
                }
            }
        }
    }
}
