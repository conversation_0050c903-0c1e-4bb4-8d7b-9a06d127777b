package com.yjsoft.roadtravel.ui.activities.plan.repository

import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseRepository
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.network.ApiService
import com.yjsoft.roadtravel.ui.activities.plan.models.MapV2Response
import com.yjsoft.roadtravel.ui.activities.plan.models.PlanDetailData
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

/**
 * 行程相关数据仓库
 *
 * 功能：
 * - 管理行程详情数据获取
 * - 统一的异常处理
 * - 网络请求封装
 *
 * 使用方式：
 * ```kotlin
 * @HiltViewModel
 * class PlanDetailsViewModel @Inject constructor(
 *     private val planRepository: PlanRepository
 * ) : BaseViewModel<PlanDetailsUiState>() {
 *
 *     suspend fun loadPlanDetails(planId: Int) {
 *         val result = planRepository.getPlanDetail(planId)
 *         // 处理结果
 *     }
 * }
 * ```
 *
 * <AUTHOR> Team
 */
@ViewModelScoped
class PlanRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "PlanRepository"
    }

    /**
     * 获取行程详情
     * @param planId 行程ID
     * @param aiReqid AI请求ID（可选）
     * @return Resource<PlanDetailData> 包装的行程详情数据
     */
    suspend fun getPlanDetail(planId: Int, aiReqid: String? = null): Resource<PlanDetailData> {
        return safeApiCall {
            val response = apiService.getPlanDetail(planId, aiReqid)
            response.getDataOrThrow()
        }
    }

    /**
     * 获取行程路线数据
     * @param planId 行程ID
     * @param aiReqid AI请求ID（可选）
     * @return Resource<MapV2Response> 包装的行程路线数据
     */
    suspend fun getPlanPolyline(planId: Int, aiReqid: String? = null): Resource<MapV2Response> {
        return safeApiCall {
            val response = apiService.getPlanPolyline(planId, aiReqid)
            response.getDataOrThrow()
        }
    }
}
