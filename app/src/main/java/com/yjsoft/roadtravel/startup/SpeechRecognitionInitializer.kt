package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.speech.core.SpeechRecognitionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 语音识别初始化器
 * 在应用启动时初始化语音识别SDK
 */
class SpeechRecognitionInitializer : Initializer<SpeechRecognitionManager> {
    
    companion object {
        private const val TAG = "SpeechRecognitionInitializer"
    }
    
    private val initScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    override fun create(context: Context): SpeechRecognitionManager {
        LogManager.d(TAG, "开始初始化语音识别模块")
        
        val speechManager = SpeechRecognitionManager.getInstance(context)
        
        // 异步初始化SDK，避免阻塞启动流程
        initScope.launch {
            try {
                val success = speechManager.initialize()
                if (success) {
                    LogManager.i(TAG, "语音识别模块初始化成功")
                } else {
                    LogManager.w(TAG, "语音识别模块初始化失败")
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "语音识别模块初始化异常", e)
            }
        }
        
        return speechManager
    }
    
    override fun dependencies(): List<Class<out Initializer<*>>> {
        // 依赖日志管理器和权限管理器
        return listOf(
            LogManagerInitializer::class.java,
            PermissionInitializer::class.java
        )
    }
}
