package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.utils.StartupTimer

/**
 * LogManager初始化器
 * 使用androidx.startup进行日志框架的初始化
 * 
 * 优先级：最高（无依赖）
 * 依赖：无
 */
class LogManagerInitializer : Initializer<LogManager> {

    companion object {
        private const val TAG = "LogManagerInitializer %s"
    }

    /**
     * 创建并初始化LogManager
     * @param context 应用上下文
     * @return LogManager实例
     */
    override fun create(context: Context): LogManager {
        StartupTimer.start("log_manager_init")

        try {
            // 初始化日志框架
            LogManager.init(context)

            // 记录初始化成功日志
            LogManager.d(TAG, "LogManager通过androidx.startup初始化成功")

            val duration = StartupTimer.end("log_manager_init")
            LogManager.i(TAG, "LogManager初始化耗时: ${duration}ms")

            return LogManager

        } catch (e: Exception) {
            // 如果初始化失败，记录错误并重新抛出异常
            android.util.Log.e(TAG, "LogManager初始化失败", e)
            StartupTimer.end("log_manager_init")
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * LogManager作为基础组件，不依赖其他组件
     * @return 空列表，表示无依赖
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
