package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.auth.WeChatTokenProvider
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.utils.StartupTimer

/**
 * 网络核心初始化器
 * 只初始化网络框架的核心配置，延迟加载重型组件
 * 
 * 优先级：中（依赖LogManager和DataStore）
 * 依赖：LogManager, DataStore
 */
class NetworkCoreInitializer : Initializer<NetworkManager> {

    companion object {
        private const val TAG = "NetworkCoreInitializer"
    }

    /**
     * 创建并初始化网络框架核心组件
     * @param context 应用上下文
     * @return NetworkManager实例
     */
    override fun create(context: Context): NetworkManager {
        StartupTimer.start("network_core_init")
        
        try {
            LogManager.d(TAG, "开始网络核心组件初始化")
            
            // 1. 快速初始化TokenManager（异步预热）
            TokenManager.initialize(context)
            LogManager.d(TAG, "TokenManager核心初始化完成")
            
            // 2. 创建微信Token提供者（轻量级）
            val weChatTokenProvider = WeChatTokenProvider(context)
            LogManager.d(TAG, "WeChatTokenProvider创建完成")
            
            // 3. 初始化网络框架核心配置（不预初始化RetrofitInstance）
            NetworkManager.initCore(context, weChatTokenProvider)
            LogManager.d(TAG, "网络框架核心配置完成")
            
            // 4. 记录网络框架状态
            val status = NetworkManager.getStatus()
            LogManager.d(TAG, "网络核心状态 - 环境: ${status.currentEnvironment?.name}, BaseURL: ${status.baseUrl}")
            
            val duration = StartupTimer.end("network_core_init")
            LogManager.i(TAG, "网络核心组件初始化完成，耗时: ${duration}ms")
            
            return NetworkManager
            
        } catch (e: Exception) {
            StartupTimer.end("network_core_init")
            LogManager.e(TAG, "网络核心组件初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * 网络核心组件依赖LogManager和DataStore
     * @return 依赖LogManagerInitializer和DataStoreInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(
            LogManagerInitializer::class.java,
            DataStoreInitializer::class.java
        )
    }
}
