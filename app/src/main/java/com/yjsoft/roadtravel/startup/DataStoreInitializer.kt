package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * DataStore初始化器
 * 使用androidx.startup进行DataStore框架的初始化
 * 
 * 优先级：高（依赖LogManager）
 * 依赖：LogManager
 */
class DataStoreInitializer : Initializer<DataStoreManager> {

    companion object {
        private const val TAG = "DataStoreInitializer"
    }

    /**
     * 创建并初始化DataStoreManager
     * @param context 应用上下文
     * @return DataStoreManager实例
     */
    override fun create(context: Context): DataStoreManager {
        try {
            // 初始化DataStore管理器
            DataStoreManager.init(context)
            
            // 记录初始化成功日志
            LogManager.d(TAG, "DataStoreManager通过androidx.startup初始化成功")
            
            return DataStoreManager
            
        } catch (e: Exception) {
            // 记录错误并重新抛出异常
            LogManager.e(TAG, "DataStoreManager初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * DataStore依赖LogManager进行日志记录
     * @return 依赖LogManagerInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(LogManagerInitializer::class.java)
    }
}
