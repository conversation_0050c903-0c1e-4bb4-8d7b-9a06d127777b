package com.yjsoft.roadtravel.data.repository

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.ui.fragments.home.models.PlanItem
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 收藏数据仓库
 * 
 * 功能：
 * - 管理用户收藏的行程数据
 * - 提供收藏/取消收藏操作
 * - 提供收藏列表查询
 * 
 * <AUTHOR> Team
 */
@Singleton
class FavoriteRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "FavoriteRepository"
        
        // DataStore 键定义
        private val FAVORITE_PLANS_KEY = PreferenceKey.string(
            name = "favorite_plans_json",
            defaultValue = "[]",
            description = "收藏的行程列表（JSON格式）"
        )
    }
    
    private val dataStoreRepository = DataStoreRepository.getInstance()
    private val gson = Gson()
    
    /**
     * 获取收藏列表的 Flow
     */
    fun getFavoritePlansFlow(): Flow<List<PlanItem>> {
        return dataStoreRepository.getValueFlow(FAVORITE_PLANS_KEY).map { jsonString ->
            try {
                if (jsonString.isBlank() || jsonString == "[]") {
                    emptyList()
                } else {
                    val type = object : TypeToken<List<PlanItem>>() {}.type
                    gson.fromJson<List<PlanItem>>(jsonString, type) ?: emptyList()
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "解析收藏列表失败", e)
                emptyList()
            }
        }
    }
    
    /**
     * 获取收藏列表（挂起函数）
     */
    suspend fun getFavoritePlans(): List<PlanItem> {
        return try {
            val jsonString = dataStoreRepository.getValue(FAVORITE_PLANS_KEY)
            if (jsonString.isBlank() || jsonString == "[]") {
                emptyList()
            } else {
                val type = object : TypeToken<List<PlanItem>>() {}.type
                gson.fromJson<List<PlanItem>>(jsonString, type) ?: emptyList()
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取收藏列表失败", e)
            emptyList()
        }
    }
    
    /**
     * 添加收藏
     */
    suspend fun addFavorite(planItem: PlanItem): Boolean {
        return try {
            val currentList = getFavoritePlans().toMutableList()

            LogManager.d(TAG, "准备添加收藏: ${planItem.subject}, planId=${planItem.planId}, aiReqid=${planItem.aiReqid}")
            LogManager.d(TAG, "当前收藏列表大小: ${currentList.size}")

            // 检查是否已经收藏
            val existingIndex = currentList.indexOfFirst {
                it.planId == planItem.planId || it.aiReqid == planItem.aiReqid
            }

            LogManager.d(TAG, "检查重复结果: existingIndex=$existingIndex")

            if (existingIndex == -1) {
                // 创建收藏版本的 PlanItem（设置 isOwn = true）
                val favoriteItem = planItem.copy(isOwn = true)
                currentList.add(0, favoriteItem) // 添加到列表开头

                val jsonString = gson.toJson(currentList)
                dataStoreRepository.setValue(FAVORITE_PLANS_KEY, jsonString)

                LogManager.d(TAG, "添加收藏成功: ${planItem.subject}, 新列表大小: ${currentList.size}")
                true
            } else {
                LogManager.d(TAG, "行程已经收藏: ${planItem.subject}")
                true
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "添加收藏失败", e)
            false
        }
    }
    
    /**
     * 移除收藏
     */
    suspend fun removeFavorite(planItem: PlanItem): Boolean {
        return try {
            val currentList = getFavoritePlans().toMutableList()
            
            // 根据 planId 或 aiReqid 查找并移除
            val removed = currentList.removeAll { 
                it.planId == planItem.planId || it.aiReqid == planItem.aiReqid 
            }
            
            if (removed) {
                val jsonString = gson.toJson(currentList)
                dataStoreRepository.setValue(FAVORITE_PLANS_KEY, jsonString)
                
                LogManager.d(TAG, "移除收藏成功: ${planItem.subject}")
                true
            } else {
                LogManager.d(TAG, "收藏中未找到该行程: ${planItem.subject}")
                false
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "移除收藏失败", e)
            false
        }
    }
    
    /**
     * 检查是否已收藏
     */
    suspend fun isFavorite(planItem: PlanItem): Boolean {
        return try {
            val currentList = getFavoritePlans()
            currentList.any { 
                it.planId == planItem.planId || it.aiReqid == planItem.aiReqid 
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "检查收藏状态失败", e)
            false
        }
    }
    
    /**
     * 切换收藏状态
     */
    suspend fun toggleFavorite(planItem: PlanItem): Boolean {
        return if (isFavorite(planItem)) {
            removeFavorite(planItem)
        } else {
            addFavorite(planItem)
        }
    }
    
    /**
     * 清空收藏列表
     */
    suspend fun clearFavorites(): Boolean {
        return try {
            dataStoreRepository.setValue(FAVORITE_PLANS_KEY, "[]")
            LogManager.d(TAG, "清空收藏列表成功")
            true
        } catch (e: Exception) {
            LogManager.e(TAG, "清空收藏列表失败", e)
            false
        }
    }
    
    /**
     * 获取收藏数量
     */
    suspend fun getFavoriteCount(): Int {
        return try {
            getFavoritePlans().size
        } catch (e: Exception) {
            LogManager.e(TAG, "获取收藏数量失败", e)
            0
        }
    }
}
